#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/Downloads/bugwatchgov-frontend/node_modules/.pnpm/autoprefixer@10.4.21_postcss@8.5.4/node_modules/autoprefixer/bin/node_modules:/home/<USER>/Downloads/bugwatchgov-frontend/node_modules/.pnpm/autoprefixer@10.4.21_postcss@8.5.4/node_modules/autoprefixer/node_modules:/home/<USER>/Downloads/bugwatchgov-frontend/node_modules/.pnpm/autoprefixer@10.4.21_postcss@8.5.4/node_modules:/home/<USER>/Downloads/bugwatchgov-frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/Downloads/bugwatchgov-frontend/node_modules/.pnpm/autoprefixer@10.4.21_postcss@8.5.4/node_modules/autoprefixer/bin/node_modules:/home/<USER>/Downloads/bugwatchgov-frontend/node_modules/.pnpm/autoprefixer@10.4.21_postcss@8.5.4/node_modules/autoprefixer/node_modules:/home/<USER>/Downloads/bugwatchgov-frontend/node_modules/.pnpm/autoprefixer@10.4.21_postcss@8.5.4/node_modules:/home/<USER>/Downloads/bugwatchgov-frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../autoprefixer/bin/autoprefixer" "$@"
else
  exec node  "$basedir/../autoprefixer/bin/autoprefixer" "$@"
fi
