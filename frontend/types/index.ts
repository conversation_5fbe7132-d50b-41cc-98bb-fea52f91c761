export interface User {
  id: string
  email: string
  name?: string
  role: "user" | "admin" | "super_admin"
  ministry?: string
}

export interface BugReport {
  id: string
  title: string
  description: string
  ministry: string
  location: string
  unit: string
  bug_type: BugType
  affected_system: string
  status: BugStatus
  priority: BugPriority
  created_by: string
  assigned_to?: string
  resolved_at?: string
  created_at: string
  updated_at: string
}

export type BugType =
  | "security"
  | "ui"
  | "network"
  | "performance"
  | "data"
  | "authentication"
  | "integration"
  | "hardware"
  | "other"

export type BugStatus = "open" | "in_progress" | "resolved" | "closed" | "rejected"

export type BugPriority = "low" | "medium" | "high" | "critical"

export interface CreateBugReportInput {
  title: string
  description: string
  ministry: string
  location: string
  unit: string
  bug_type: BugType
  affected_system: string
  priority?: BugPriority
}

export interface UpdateBugReportInput {
  title?: string
  description?: string
  ministry?: string
  location?: string
  unit?: string
  bug_type?: BugType
  affected_system?: string
  status?: BugStatus
  priority?: BugPriority
  assigned_to?: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    message: string
    code?: string
    details?: any
  }
}
