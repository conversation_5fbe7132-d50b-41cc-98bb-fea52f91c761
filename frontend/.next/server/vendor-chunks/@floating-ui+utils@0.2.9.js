"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@floating-ui+utils@0.2.9";
exports.ids = ["vendor-chunks/@floating-ui+utils@0.2.9"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@floating-ui+utils@0.2.9/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@floating-ui+utils@0.2.9/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getComputedStyle: () => (/* binding */ getComputedStyle),\n/* harmony export */   getContainingBlock: () => (/* binding */ getContainingBlock),\n/* harmony export */   getDocumentElement: () => (/* binding */ getDocumentElement),\n/* harmony export */   getFrameElement: () => (/* binding */ getFrameElement),\n/* harmony export */   getNearestOverflowAncestor: () => (/* binding */ getNearestOverflowAncestor),\n/* harmony export */   getNodeName: () => (/* binding */ getNodeName),\n/* harmony export */   getNodeScroll: () => (/* binding */ getNodeScroll),\n/* harmony export */   getOverflowAncestors: () => (/* binding */ getOverflowAncestors),\n/* harmony export */   getParentNode: () => (/* binding */ getParentNode),\n/* harmony export */   getWindow: () => (/* binding */ getWindow),\n/* harmony export */   isContainingBlock: () => (/* binding */ isContainingBlock),\n/* harmony export */   isElement: () => (/* binding */ isElement),\n/* harmony export */   isHTMLElement: () => (/* binding */ isHTMLElement),\n/* harmony export */   isLastTraversableNode: () => (/* binding */ isLastTraversableNode),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   isOverflowElement: () => (/* binding */ isOverflowElement),\n/* harmony export */   isShadowRoot: () => (/* binding */ isShadowRoot),\n/* harmony export */   isTableElement: () => (/* binding */ isTableElement),\n/* harmony export */   isTopLayer: () => (/* binding */ isTopLayer),\n/* harmony export */   isWebKit: () => (/* binding */ isWebKit)\n/* harmony export */ });\nfunction hasWindow() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction getNodeName(node) {\n    if (isNode(node)) {\n        return (node.nodeName || \"\").toLowerCase();\n    }\n    // Mocked nodes in testing environments may not be instances of Node. By\n    // returning `#document` an infinite loop won't occur.\n    // https://github.com/floating-ui/floating-ui/issues/2317\n    return \"#document\";\n}\nfunction getWindow(node) {\n    var _node$ownerDocument;\n    return (node == null || (_node$ownerDocument = node.ownerDocument) == null ? void 0 : _node$ownerDocument.defaultView) || window;\n}\nfunction getDocumentElement(node) {\n    var _ref;\n    return (_ref = (isNode(node) ? node.ownerDocument : node.document) || window.document) == null ? void 0 : _ref.documentElement;\n}\nfunction isNode(value) {\n    if (!hasWindow()) {\n        return false;\n    }\n    return value instanceof Node || value instanceof getWindow(value).Node;\n}\nfunction isElement(value) {\n    if (!hasWindow()) {\n        return false;\n    }\n    return value instanceof Element || value instanceof getWindow(value).Element;\n}\nfunction isHTMLElement(value) {\n    if (!hasWindow()) {\n        return false;\n    }\n    return value instanceof HTMLElement || value instanceof getWindow(value).HTMLElement;\n}\nfunction isShadowRoot(value) {\n    if (!hasWindow() || typeof ShadowRoot === \"undefined\") {\n        return false;\n    }\n    return value instanceof ShadowRoot || value instanceof getWindow(value).ShadowRoot;\n}\nfunction isOverflowElement(element) {\n    const { overflow, overflowX, overflowY, display } = getComputedStyle(element);\n    return /auto|scroll|overlay|hidden|clip/.test(overflow + overflowY + overflowX) && ![\n        \"inline\",\n        \"contents\"\n    ].includes(display);\n}\nfunction isTableElement(element) {\n    return [\n        \"table\",\n        \"td\",\n        \"th\"\n    ].includes(getNodeName(element));\n}\nfunction isTopLayer(element) {\n    return [\n        \":popover-open\",\n        \":modal\"\n    ].some((selector)=>{\n        try {\n            return element.matches(selector);\n        } catch (e) {\n            return false;\n        }\n    });\n}\nfunction isContainingBlock(elementOrCss) {\n    const webkit = isWebKit();\n    const css = isElement(elementOrCss) ? getComputedStyle(elementOrCss) : elementOrCss;\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n    // https://drafts.csswg.org/css-transforms-2/#individual-transforms\n    return [\n        \"transform\",\n        \"translate\",\n        \"scale\",\n        \"rotate\",\n        \"perspective\"\n    ].some((value)=>css[value] ? css[value] !== \"none\" : false) || (css.containerType ? css.containerType !== \"normal\" : false) || !webkit && (css.backdropFilter ? css.backdropFilter !== \"none\" : false) || !webkit && (css.filter ? css.filter !== \"none\" : false) || [\n        \"transform\",\n        \"translate\",\n        \"scale\",\n        \"rotate\",\n        \"perspective\",\n        \"filter\"\n    ].some((value)=>(css.willChange || \"\").includes(value)) || [\n        \"paint\",\n        \"layout\",\n        \"strict\",\n        \"content\"\n    ].some((value)=>(css.contain || \"\").includes(value));\n}\nfunction getContainingBlock(element) {\n    let currentNode = getParentNode(element);\n    while(isHTMLElement(currentNode) && !isLastTraversableNode(currentNode)){\n        if (isContainingBlock(currentNode)) {\n            return currentNode;\n        } else if (isTopLayer(currentNode)) {\n            return null;\n        }\n        currentNode = getParentNode(currentNode);\n    }\n    return null;\n}\nfunction isWebKit() {\n    if (typeof CSS === \"undefined\" || !CSS.supports) return false;\n    return CSS.supports(\"-webkit-backdrop-filter\", \"none\");\n}\nfunction isLastTraversableNode(node) {\n    return [\n        \"html\",\n        \"body\",\n        \"#document\"\n    ].includes(getNodeName(node));\n}\nfunction getComputedStyle(element) {\n    return getWindow(element).getComputedStyle(element);\n}\nfunction getNodeScroll(element) {\n    if (isElement(element)) {\n        return {\n            scrollLeft: element.scrollLeft,\n            scrollTop: element.scrollTop\n        };\n    }\n    return {\n        scrollLeft: element.scrollX,\n        scrollTop: element.scrollY\n    };\n}\nfunction getParentNode(node) {\n    if (getNodeName(node) === \"html\") {\n        return node;\n    }\n    const result = // Step into the shadow DOM of the parent of a slotted node.\n    node.assignedSlot || // DOM Element detected.\n    node.parentNode || // ShadowRoot detected.\n    isShadowRoot(node) && node.host || // Fallback.\n    getDocumentElement(node);\n    return isShadowRoot(result) ? result.host : result;\n}\nfunction getNearestOverflowAncestor(node) {\n    const parentNode = getParentNode(node);\n    if (isLastTraversableNode(parentNode)) {\n        return node.ownerDocument ? node.ownerDocument.body : node.body;\n    }\n    if (isHTMLElement(parentNode) && isOverflowElement(parentNode)) {\n        return parentNode;\n    }\n    return getNearestOverflowAncestor(parentNode);\n}\nfunction getOverflowAncestors(node, list, traverseIframes) {\n    var _node$ownerDocument2;\n    if (list === void 0) {\n        list = [];\n    }\n    if (traverseIframes === void 0) {\n        traverseIframes = true;\n    }\n    const scrollableAncestor = getNearestOverflowAncestor(node);\n    const isBody = scrollableAncestor === ((_node$ownerDocument2 = node.ownerDocument) == null ? void 0 : _node$ownerDocument2.body);\n    const win = getWindow(scrollableAncestor);\n    if (isBody) {\n        const frameElement = getFrameElement(win);\n        return list.concat(win, win.visualViewport || [], isOverflowElement(scrollableAncestor) ? scrollableAncestor : [], frameElement && traverseIframes ? getOverflowAncestors(frameElement) : []);\n    }\n    return list.concat(scrollableAncestor, getOverflowAncestors(scrollableAncestor, [], traverseIframes));\n}\nfunction getFrameElement(win) {\n    return win.parent && Object.getPrototypeOf(win.parent) ? win.frameElement : null;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@floating-ui+utils@0.2.9/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@floating-ui+utils@0.2.9/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@floating-ui+utils@0.2.9/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   alignments: () => (/* binding */ alignments),\n/* harmony export */   clamp: () => (/* binding */ clamp),\n/* harmony export */   createCoords: () => (/* binding */ createCoords),\n/* harmony export */   evaluate: () => (/* binding */ evaluate),\n/* harmony export */   expandPaddingObject: () => (/* binding */ expandPaddingObject),\n/* harmony export */   floor: () => (/* binding */ floor),\n/* harmony export */   getAlignment: () => (/* binding */ getAlignment),\n/* harmony export */   getAlignmentAxis: () => (/* binding */ getAlignmentAxis),\n/* harmony export */   getAlignmentSides: () => (/* binding */ getAlignmentSides),\n/* harmony export */   getAxisLength: () => (/* binding */ getAxisLength),\n/* harmony export */   getExpandedPlacements: () => (/* binding */ getExpandedPlacements),\n/* harmony export */   getOppositeAlignmentPlacement: () => (/* binding */ getOppositeAlignmentPlacement),\n/* harmony export */   getOppositeAxis: () => (/* binding */ getOppositeAxis),\n/* harmony export */   getOppositeAxisPlacements: () => (/* binding */ getOppositeAxisPlacements),\n/* harmony export */   getOppositePlacement: () => (/* binding */ getOppositePlacement),\n/* harmony export */   getPaddingObject: () => (/* binding */ getPaddingObject),\n/* harmony export */   getSide: () => (/* binding */ getSide),\n/* harmony export */   getSideAxis: () => (/* binding */ getSideAxis),\n/* harmony export */   max: () => (/* binding */ max),\n/* harmony export */   min: () => (/* binding */ min),\n/* harmony export */   placements: () => (/* binding */ placements),\n/* harmony export */   rectToClientRect: () => (/* binding */ rectToClientRect),\n/* harmony export */   round: () => (/* binding */ round),\n/* harmony export */   sides: () => (/* binding */ sides)\n/* harmony export */ });\n/**\n * Custom positioning reference element.\n * @see https://floating-ui.com/docs/virtual-elements\n */ const sides = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nconst alignments = [\n    \"start\",\n    \"end\"\n];\nconst placements = /*#__PURE__*/ sides.reduce((acc, side)=>acc.concat(side, side + \"-\" + alignments[0], side + \"-\" + alignments[1]), []);\nconst min = Math.min;\nconst max = Math.max;\nconst round = Math.round;\nconst floor = Math.floor;\nconst createCoords = (v)=>({\n        x: v,\n        y: v\n    });\nconst oppositeSideMap = {\n    left: \"right\",\n    right: \"left\",\n    bottom: \"top\",\n    top: \"bottom\"\n};\nconst oppositeAlignmentMap = {\n    start: \"end\",\n    end: \"start\"\n};\nfunction clamp(start, value, end) {\n    return max(start, min(value, end));\n}\nfunction evaluate(value, param) {\n    return typeof value === \"function\" ? value(param) : value;\n}\nfunction getSide(placement) {\n    return placement.split(\"-\")[0];\n}\nfunction getAlignment(placement) {\n    return placement.split(\"-\")[1];\n}\nfunction getOppositeAxis(axis) {\n    return axis === \"x\" ? \"y\" : \"x\";\n}\nfunction getAxisLength(axis) {\n    return axis === \"y\" ? \"height\" : \"width\";\n}\nfunction getSideAxis(placement) {\n    return [\n        \"top\",\n        \"bottom\"\n    ].includes(getSide(placement)) ? \"y\" : \"x\";\n}\nfunction getAlignmentAxis(placement) {\n    return getOppositeAxis(getSideAxis(placement));\n}\nfunction getAlignmentSides(placement, rects, rtl) {\n    if (rtl === void 0) {\n        rtl = false;\n    }\n    const alignment = getAlignment(placement);\n    const alignmentAxis = getAlignmentAxis(placement);\n    const length = getAxisLength(alignmentAxis);\n    let mainAlignmentSide = alignmentAxis === \"x\" ? alignment === (rtl ? \"end\" : \"start\") ? \"right\" : \"left\" : alignment === \"start\" ? \"bottom\" : \"top\";\n    if (rects.reference[length] > rects.floating[length]) {\n        mainAlignmentSide = getOppositePlacement(mainAlignmentSide);\n    }\n    return [\n        mainAlignmentSide,\n        getOppositePlacement(mainAlignmentSide)\n    ];\n}\nfunction getExpandedPlacements(placement) {\n    const oppositePlacement = getOppositePlacement(placement);\n    return [\n        getOppositeAlignmentPlacement(placement),\n        oppositePlacement,\n        getOppositeAlignmentPlacement(oppositePlacement)\n    ];\n}\nfunction getOppositeAlignmentPlacement(placement) {\n    return placement.replace(/start|end/g, (alignment)=>oppositeAlignmentMap[alignment]);\n}\nfunction getSideList(side, isStart, rtl) {\n    const lr = [\n        \"left\",\n        \"right\"\n    ];\n    const rl = [\n        \"right\",\n        \"left\"\n    ];\n    const tb = [\n        \"top\",\n        \"bottom\"\n    ];\n    const bt = [\n        \"bottom\",\n        \"top\"\n    ];\n    switch(side){\n        case \"top\":\n        case \"bottom\":\n            if (rtl) return isStart ? rl : lr;\n            return isStart ? lr : rl;\n        case \"left\":\n        case \"right\":\n            return isStart ? tb : bt;\n        default:\n            return [];\n    }\n}\nfunction getOppositeAxisPlacements(placement, flipAlignment, direction, rtl) {\n    const alignment = getAlignment(placement);\n    let list = getSideList(getSide(placement), direction === \"start\", rtl);\n    if (alignment) {\n        list = list.map((side)=>side + \"-\" + alignment);\n        if (flipAlignment) {\n            list = list.concat(list.map(getOppositeAlignmentPlacement));\n        }\n    }\n    return list;\n}\nfunction getOppositePlacement(placement) {\n    return placement.replace(/left|right|bottom|top/g, (side)=>oppositeSideMap[side]);\n}\nfunction expandPaddingObject(padding) {\n    return {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...padding\n    };\n}\nfunction getPaddingObject(padding) {\n    return typeof padding !== \"number\" ? expandPaddingObject(padding) : {\n        top: padding,\n        right: padding,\n        bottom: padding,\n        left: padding\n    };\n}\nfunction rectToClientRect(rect) {\n    const { x, y, width, height } = rect;\n    return {\n        width,\n        height,\n        top: y,\n        left: x,\n        right: x + width,\n        bottom: y + height,\n        x,\n        y\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@floating-ui+utils@0.2.9/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\n");

/***/ })

};
;