"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-select@2.2.5_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_74ff1e179f6928d61bde19586f0439bf";
exports.ids = ["vendor-chunks/@radix-ui+react-select@2.2.5_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_74ff1e179f6928d61bde19586f0439bf"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-select@2.2.5_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_74ff1e179f6928d61bde19586f0439bf/node_modules/@radix-ui/react-select/dist/index.mjs":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-select@2.2.5_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_74ff1e179f6928d61bde19586f0439bf/node_modules/@radix-ui/react-select/dist/index.mjs ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Icon: () => (/* binding */ Icon),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   ItemText: () => (/* binding */ ItemText),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   ScrollDownButton: () => (/* binding */ ScrollDownButton),\n/* harmony export */   ScrollUpButton: () => (/* binding */ ScrollUpButton),\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectArrow: () => (/* binding */ SelectArrow),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectIcon: () => (/* binding */ SelectIcon),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectItemIndicator: () => (/* binding */ SelectItemIndicator),\n/* harmony export */   SelectItemText: () => (/* binding */ SelectItemText),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectPortal: () => (/* binding */ SelectPortal),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue),\n/* harmony export */   SelectViewport: () => (/* binding */ SelectViewport),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   Value: () => (/* binding */ Value),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createSelectScope: () => (/* binding */ createSelectScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/.pnpm/@radix-ui+number@1.1.1/node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+r_8ed47621286c24058c66cffcdce48db5/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23___dbf8386523191e50867cd199de52aa0e/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guards@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+_f761c2ff2dd8a5cebf4e03dd795af57f/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_ffa2341e59ce9c78f0d0d849ccd75e57/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1.9_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_6c1cd0a6f7cc4779efee75f9fbbe7053/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23__@types+re_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.2.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-effect@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previous@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hidden@1.2.3_@types+react-dom@18.3.7_@types+react@18.3.23__@ty_bd769e2c7ddceeff6e63be21c84dfac7/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.6/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Group,Icon,Item,ItemIndicator,ItemText,Label,Portal,Root,ScrollDownButton,ScrollUpButton,Select,SelectArrow,SelectContent,SelectGroup,SelectIcon,SelectItem,SelectItemIndicator,SelectItemText,SelectLabel,SelectPortal,SelectScrollDownButton,SelectScrollUpButton,SelectSeparator,SelectTrigger,SelectValue,SelectViewport,Separator,Trigger,Value,Viewport,createSelectScope auto */ // src/select.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar OPEN_KEYS = [\n    \" \",\n    \"Enter\",\n    \"ArrowUp\",\n    \"ArrowDown\"\n];\nvar SELECTION_KEYS = [\n    \" \",\n    \"Enter\"\n];\nvar SELECT_NAME = \"Select\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(SELECT_NAME);\nvar [createSelectContext, createSelectScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(SELECT_NAME, [\n    createCollectionScope,\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope)();\nvar [SelectProvider, useSelectContext] = createSelectContext(SELECT_NAME);\nvar [SelectNativeOptionsProvider, useSelectNativeOptionsContext] = createSelectContext(SELECT_NAME);\nvar Select = (props)=>{\n    const { __scopeSelect, children, open: openProp, defaultOpen, onOpenChange, value: valueProp, defaultValue, onValueChange, dir, name, autoComplete, disabled, required, form } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNode, setValueNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNodeHasChildren, setValueNodeHasChildren] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection)(dir);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: onOpenChange,\n        caller: SELECT_NAME\n    });\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue,\n        onChange: onValueChange,\n        caller: SELECT_NAME\n    });\n    const triggerPointerDownPosRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFormControl = trigger ? form || !!trigger.closest(\"form\") : true;\n    const [nativeOptionsSet, setNativeOptionsSet] = react__WEBPACK_IMPORTED_MODULE_0__.useState(/* @__PURE__ */ new Set());\n    const nativeSelectKey = Array.from(nativeOptionsSet).map((option)=>option.props.value).join(\";\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(SelectProvider, {\n            required,\n            scope: __scopeSelect,\n            trigger,\n            onTriggerChange: setTrigger,\n            valueNode,\n            onValueNodeChange: setValueNode,\n            valueNodeHasChildren,\n            onValueNodeHasChildrenChange: setValueNodeHasChildren,\n            contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)(),\n            value,\n            onValueChange: setValue,\n            open,\n            onOpenChange: setOpen,\n            dir: direction,\n            triggerPointerDownPosRef,\n            disabled,\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n                    scope: __scopeSelect,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectNativeOptionsProvider, {\n                        scope: props.__scopeSelect,\n                        onNativeOptionAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((option)=>{\n                            setNativeOptionsSet((prev)=>new Set(prev).add(option));\n                        }, []),\n                        onNativeOptionRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((option)=>{\n                            setNativeOptionsSet((prev)=>{\n                                const optionsSet = new Set(prev);\n                                optionsSet.delete(option);\n                                return optionsSet;\n                            });\n                        }, []),\n                        children\n                    })\n                }),\n                isFormControl ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(SelectBubbleInput, {\n                    \"aria-hidden\": true,\n                    required,\n                    tabIndex: -1,\n                    name,\n                    autoComplete,\n                    value,\n                    onChange: (event)=>setValue(event.target.value),\n                    disabled,\n                    form,\n                    children: [\n                        value === void 0 ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n                            value: \"\"\n                        }) : null,\n                        Array.from(nativeOptionsSet)\n                    ]\n                }, nativeSelectKey) : null\n            ]\n        })\n    });\n};\nSelect.displayName = SELECT_NAME;\nvar TRIGGER_NAME = \"SelectTrigger\";\nvar SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search)=>{\n        const enabledItems = getItems().filter((item)=>!item.disabled);\n        const currentItem = enabledItems.find((item)=>item.value === context.value);\n        const nextItem = findNextItem(enabledItems, search, currentItem);\n        if (nextItem !== void 0) {\n            context.onValueChange(nextItem.value);\n        }\n    });\n    const handleOpen = (pointerEvent)=>{\n        if (!isDisabled) {\n            context.onOpenChange(true);\n            resetTypeahead();\n        }\n        if (pointerEvent) {\n            context.triggerPointerDownPosRef.current = {\n                x: Math.round(pointerEvent.pageX),\n                y: Math.round(pointerEvent.pageY)\n            };\n        }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.button, {\n            type: \"button\",\n            role: \"combobox\",\n            \"aria-controls\": context.contentId,\n            \"aria-expanded\": context.open,\n            \"aria-required\": context.required,\n            \"aria-autocomplete\": \"none\",\n            dir: context.dir,\n            \"data-state\": context.open ? \"open\" : \"closed\",\n            disabled: isDisabled,\n            \"data-disabled\": isDisabled ? \"\" : void 0,\n            \"data-placeholder\": shouldShowPlaceholder(context.value) ? \"\" : void 0,\n            ...triggerProps,\n            ref: composedRefs,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onClick, (event)=>{\n                event.currentTarget.focus();\n                if (pointerTypeRef.current !== \"mouse\") {\n                    handleOpen(event);\n                }\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onPointerDown, (event)=>{\n                pointerTypeRef.current = event.pointerType;\n                const target = event.target;\n                if (target.hasPointerCapture(event.pointerId)) {\n                    target.releasePointerCapture(event.pointerId);\n                }\n                if (event.button === 0 && event.ctrlKey === false && event.pointerType === \"mouse\") {\n                    handleOpen(event);\n                    event.preventDefault();\n                }\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onKeyDown, (event)=>{\n                const isTypingAhead = searchRef.current !== \"\";\n                const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                if (isTypingAhead && event.key === \" \") return;\n                if (OPEN_KEYS.includes(event.key)) {\n                    handleOpen();\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nSelectTrigger.displayName = TRIGGER_NAME;\nvar VALUE_NAME = \"SelectValue\";\nvar SelectValue = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, children, placeholder = \"\", ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== void 0;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onValueNodeChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        onValueNodeHasChildrenChange(hasChildren);\n    }, [\n        onValueNodeHasChildrenChange,\n        hasChildren\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        ...valueProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: \"none\"\n        },\n        children: shouldShowPlaceholder(context.value) ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n            children: placeholder\n        }) : children\n    });\n});\nSelectValue.displayName = VALUE_NAME;\nvar ICON_NAME = \"SelectIcon\";\nvar SelectIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, children, ...iconProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...iconProps,\n        ref: forwardedRef,\n        children: children || \"▼\"\n    });\n});\nSelectIcon.displayName = ICON_NAME;\nvar PORTAL_NAME = \"SelectPortal\";\nvar SelectPortal = (props)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        ...props\n    });\n};\nSelectPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"SelectContent\";\nvar SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        setFragment(new DocumentFragment());\n    }, []);\n    if (!context.open) {\n        const frag = fragment;\n        return frag ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n            scope: props.__scopeSelect,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: props.__scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n                    children: props.children\n                })\n            })\n        }), frag) : null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentImpl, {\n        ...props,\n        ref: forwardedRef\n    });\n});\nSelectContent.displayName = CONTENT_NAME;\nvar CONTENT_MARGIN = 10;\nvar [SelectContentProvider, useSelectContentContext] = createSelectContext(CONTENT_NAME);\nvar CONTENT_IMPL_NAME = \"SelectContentImpl\";\nvar Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_14__.createSlot)(\"SelectContent.RemoveScroll\");\nvar SelectContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, position = \"item-aligned\", onCloseAutoFocus, onEscapeKeyDown, onPointerDownOutside, //\n    // PopperContent props\n    side, sideOffset, align, alignOffset, arrowPadding, collisionBoundary, collisionPadding, sticky, hideWhenDetached, avoidCollisions, //\n    ...contentProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const [selectedItem, setSelectedItem] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [selectedItemText, setSelectedItemText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const firstValidItemFoundRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_15__.hideOthers)(content);\n    }, [\n        content\n    ]);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_16__.useFocusGuards)();\n    const focusFirst = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((candidates)=>{\n        const [firstItem, ...restItems] = getItems().map((item)=>item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates){\n            if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n            candidate?.scrollIntoView({\n                block: \"nearest\"\n            });\n            if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n            if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n            candidate?.focus();\n            if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n    }, [\n        getItems,\n        viewport\n    ]);\n    const focusSelectedItem = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>focusFirst([\n            selectedItem,\n            content\n        ]), [\n        focusFirst,\n        selectedItem,\n        content\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (isPositioned) {\n            focusSelectedItem();\n        }\n    }, [\n        isPositioned,\n        focusSelectedItem\n    ]);\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (content) {\n            let pointerMoveDelta = {\n                x: 0,\n                y: 0\n            };\n            const handlePointerMove = (event)=>{\n                pointerMoveDelta = {\n                    x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n                    y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0))\n                };\n            };\n            const handlePointerUp = (event)=>{\n                if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n                    event.preventDefault();\n                } else {\n                    if (!content.contains(event.target)) {\n                        onOpenChange(false);\n                    }\n                }\n                document.removeEventListener(\"pointermove\", handlePointerMove);\n                triggerPointerDownPosRef.current = null;\n            };\n            if (triggerPointerDownPosRef.current !== null) {\n                document.addEventListener(\"pointermove\", handlePointerMove);\n                document.addEventListener(\"pointerup\", handlePointerUp, {\n                    capture: true,\n                    once: true\n                });\n            }\n            return ()=>{\n                document.removeEventListener(\"pointermove\", handlePointerMove);\n                document.removeEventListener(\"pointerup\", handlePointerUp, {\n                    capture: true\n                });\n            };\n        }\n    }, [\n        content,\n        onOpenChange,\n        triggerPointerDownPosRef\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const close = ()=>onOpenChange(false);\n        window.addEventListener(\"blur\", close);\n        window.addEventListener(\"resize\", close);\n        return ()=>{\n            window.removeEventListener(\"blur\", close);\n            window.removeEventListener(\"resize\", close);\n        };\n    }, [\n        onOpenChange\n    ]);\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search)=>{\n        const enabledItems = getItems().filter((item)=>!item.disabled);\n        const currentItem = enabledItems.find((item)=>item.ref.current === document.activeElement);\n        const nextItem = findNextItem(enabledItems, search, currentItem);\n        if (nextItem) {\n            setTimeout(()=>nextItem.ref.current.focus());\n        }\n    });\n    const itemRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node, value, disabled)=>{\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n            setSelectedItem(node);\n            if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n    }, [\n        context.value\n    ]);\n    const handleItemLeave = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>content?.focus(), [\n        content\n    ]);\n    const itemTextRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node, value, disabled)=>{\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n            setSelectedItemText(node);\n        }\n    }, [\n        context.value\n    ]);\n    const SelectPosition = position === \"popper\" ? SelectPopperPosition : SelectItemAlignedPosition;\n    const popperContentProps = SelectPosition === SelectPopperPosition ? {\n        side,\n        sideOffset,\n        align,\n        alignOffset,\n        arrowPadding,\n        collisionBoundary,\n        collisionPadding,\n        sticky,\n        hideWhenDetached,\n        avoidCollisions\n    } : {};\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n        scope: __scopeSelect,\n        content,\n        viewport,\n        onViewportChange: setViewport,\n        itemRefCallback,\n        selectedItem,\n        onItemLeave: handleItemLeave,\n        itemTextRefCallback,\n        focusSelectedItem,\n        selectedItemText,\n        position,\n        isPositioned,\n        searchRef,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            as: Slot,\n            allowPinchZoom: true,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__.FocusScope, {\n                asChild: true,\n                trapped: context.open,\n                onMountAutoFocus: (event)=>{\n                    event.preventDefault();\n                },\n                onUnmountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(onCloseAutoFocus, (event)=>{\n                    context.trigger?.focus({\n                        preventScroll: true\n                    });\n                    event.preventDefault();\n                }),\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__.DismissableLayer, {\n                    asChild: true,\n                    disableOutsidePointerEvents: true,\n                    onEscapeKeyDown,\n                    onPointerDownOutside,\n                    onFocusOutside: (event)=>event.preventDefault(),\n                    onDismiss: ()=>context.onOpenChange(false),\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectPosition, {\n                        role: \"listbox\",\n                        id: context.contentId,\n                        \"data-state\": context.open ? \"open\" : \"closed\",\n                        dir: context.dir,\n                        onContextMenu: (event)=>event.preventDefault(),\n                        ...contentProps,\n                        ...popperContentProps,\n                        onPlaced: ()=>setIsPositioned(true),\n                        ref: composedRefs,\n                        style: {\n                            // flex layout so we can place the scroll buttons properly\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            // reset the outline by default as the content MAY get focused\n                            outline: \"none\",\n                            ...contentProps.style\n                        },\n                        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(contentProps.onKeyDown, (event)=>{\n                            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                            if (event.key === \"Tab\") event.preventDefault();\n                            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                            if ([\n                                \"ArrowUp\",\n                                \"ArrowDown\",\n                                \"Home\",\n                                \"End\"\n                            ].includes(event.key)) {\n                                const items = getItems().filter((item)=>!item.disabled);\n                                let candidateNodes = items.map((item)=>item.ref.current);\n                                if ([\n                                    \"ArrowUp\",\n                                    \"End\"\n                                ].includes(event.key)) {\n                                    candidateNodes = candidateNodes.slice().reverse();\n                                }\n                                if ([\n                                    \"ArrowUp\",\n                                    \"ArrowDown\"\n                                ].includes(event.key)) {\n                                    const currentElement = event.target;\n                                    const currentIndex = candidateNodes.indexOf(currentElement);\n                                    candidateNodes = candidateNodes.slice(currentIndex + 1);\n                                }\n                                setTimeout(()=>focusFirst(candidateNodes));\n                                event.preventDefault();\n                            }\n                        })\n                    })\n                })\n            })\n        })\n    });\n});\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\nvar ITEM_ALIGNED_POSITION_NAME = \"SelectItemAlignedPosition\";\nvar SelectItemAlignedPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onPlaced, ...popperProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n    const [contentWrapper, setContentWrapper] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const getItems = useCollection(__scopeSelect);\n    const shouldExpandOnScrollRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const shouldRepositionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n    const position = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (context.trigger && context.valueNode && contentWrapper && content && viewport && selectedItem && selectedItemText) {\n            const triggerRect = context.trigger.getBoundingClientRect();\n            const contentRect = content.getBoundingClientRect();\n            const valueNodeRect = context.valueNode.getBoundingClientRect();\n            const itemTextRect = selectedItemText.getBoundingClientRect();\n            if (context.dir !== \"rtl\") {\n                const itemTextOffset = itemTextRect.left - contentRect.left;\n                const left = valueNodeRect.left - itemTextOffset;\n                const leftDelta = triggerRect.left - left;\n                const minContentWidth = triggerRect.width + leftDelta;\n                const contentWidth = Math.max(minContentWidth, contentRect.width);\n                const rightEdge = window.innerWidth - CONTENT_MARGIN;\n                const clampedLeft = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(left, [\n                    CONTENT_MARGIN,\n                    // Prevents the content from going off the starting edge of the\n                    // viewport. It may still go off the ending edge, but this can be\n                    // controlled by the user since they may want to manage overflow in a\n                    // specific way.\n                    // https://github.com/radix-ui/primitives/issues/2049\n                    Math.max(CONTENT_MARGIN, rightEdge - contentWidth)\n                ]);\n                contentWrapper.style.minWidth = minContentWidth + \"px\";\n                contentWrapper.style.left = clampedLeft + \"px\";\n            } else {\n                const itemTextOffset = contentRect.right - itemTextRect.right;\n                const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n                const rightDelta = window.innerWidth - triggerRect.right - right;\n                const minContentWidth = triggerRect.width + rightDelta;\n                const contentWidth = Math.max(minContentWidth, contentRect.width);\n                const leftEdge = window.innerWidth - CONTENT_MARGIN;\n                const clampedRight = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(right, [\n                    CONTENT_MARGIN,\n                    Math.max(CONTENT_MARGIN, leftEdge - contentWidth)\n                ]);\n                contentWrapper.style.minWidth = minContentWidth + \"px\";\n                contentWrapper.style.right = clampedRight + \"px\";\n            }\n            const items = getItems();\n            const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n            const itemsHeight = viewport.scrollHeight;\n            const contentStyles = window.getComputedStyle(content);\n            const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n            const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n            const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n            const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n            const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth;\n            const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n            const viewportStyles = window.getComputedStyle(viewport);\n            const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n            const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n            const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n            const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n            const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n            const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n            const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n            const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n            const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n            if (willAlignWithoutTopOverflow) {\n                const isLastItem = items.length > 0 && selectedItem === items[items.length - 1].ref.current;\n                contentWrapper.style.bottom = \"0px\";\n                const viewportOffsetBottom = content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n                const clampedTriggerMiddleToBottomEdge = Math.max(triggerMiddleToBottomEdge, selectedItemHalfHeight + // viewport might have padding bottom, include it to avoid a scrollable viewport\n                (isLastItem ? viewportPaddingBottom : 0) + viewportOffsetBottom + contentBorderBottomWidth);\n                const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n                contentWrapper.style.height = height + \"px\";\n            } else {\n                const isFirstItem = items.length > 0 && selectedItem === items[0].ref.current;\n                contentWrapper.style.top = \"0px\";\n                const clampedTopEdgeToTriggerMiddle = Math.max(topEdgeToTriggerMiddle, contentBorderTopWidth + viewport.offsetTop + // viewport might have padding top, include it to avoid a scrollable viewport\n                (isFirstItem ? viewportPaddingTop : 0) + selectedItemHalfHeight);\n                const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n                contentWrapper.style.height = height + \"px\";\n                viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n            }\n            contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n            contentWrapper.style.minHeight = minContentHeight + \"px\";\n            contentWrapper.style.maxHeight = availableHeight + \"px\";\n            onPlaced?.();\n            requestAnimationFrame(()=>shouldExpandOnScrollRef.current = true);\n        }\n    }, [\n        getItems,\n        context.trigger,\n        context.valueNode,\n        contentWrapper,\n        content,\n        viewport,\n        selectedItem,\n        selectedItemText,\n        context.dir,\n        onPlaced\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>position(), [\n        position\n    ]);\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [\n        content\n    ]);\n    const handleScrollButtonChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n        if (node && shouldRepositionRef.current === true) {\n            position();\n            focusSelectedItem?.();\n            shouldRepositionRef.current = false;\n        }\n    }, [\n        position,\n        focusSelectedItem\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectViewportProvider, {\n        scope: __scopeSelect,\n        contentWrapper,\n        shouldExpandOnScrollRef,\n        onScrollButtonChange: handleScrollButtonChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n            ref: setContentWrapper,\n            style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                position: \"fixed\",\n                zIndex: contentZIndex\n            },\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                ...popperProps,\n                ref: composedRefs,\n                style: {\n                    // When we get the height of the content, it includes borders. If we were to set\n                    // the height without having `boxSizing: 'border-box'` it would be too big.\n                    boxSizing: \"border-box\",\n                    // We need to ensure the content doesn't get taller than the wrapper\n                    maxHeight: \"100%\",\n                    ...popperProps.style\n                }\n            })\n        })\n    });\n});\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\nvar POPPER_POSITION_NAME = \"SelectPopperPosition\";\nvar SelectPopperPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, align = \"start\", collisionPadding = CONTENT_MARGIN, ...popperProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Content, {\n        ...popperScope,\n        ...popperProps,\n        ref: forwardedRef,\n        align,\n        collisionPadding,\n        style: {\n            // Ensure border-box for floating-ui calculations\n            boxSizing: \"border-box\",\n            ...popperProps.style,\n            // re-namespace exposed content custom properties\n            ...{\n                \"--radix-select-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-select-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-select-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-select-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-select-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n        }\n    });\n});\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\nvar [SelectViewportProvider, useSelectViewportContext] = createSelectContext(CONTENT_NAME, {});\nvar VIEWPORT_NAME = \"SelectViewport\";\nvar SelectViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                    \"data-radix-select-viewport\": \"\",\n                    role: \"presentation\",\n                    ...viewportProps,\n                    ref: composedRefs,\n                    style: {\n                        // we use position: 'relative' here on the `viewport` so that when we call\n                        // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n                        // (independent of the scrollUpButton).\n                        position: \"relative\",\n                        flex: 1,\n                        // Viewport should only be scrollable in the vertical direction.\n                        // This won't work in vertical writing modes, so we'll need to\n                        // revisit this if/when that is supported\n                        // https://developer.chrome.com/blog/vertical-form-controls\n                        overflow: \"hidden auto\",\n                        ...viewportProps.style\n                    },\n                    onScroll: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(viewportProps.onScroll, (event)=>{\n                        const viewport = event.currentTarget;\n                        const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n                        if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                            const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                            if (scrolledBy > 0) {\n                                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                                const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                                const cssHeight = parseFloat(contentWrapper.style.height);\n                                const prevHeight = Math.max(cssMinHeight, cssHeight);\n                                if (prevHeight < availableHeight) {\n                                    const nextHeight = prevHeight + scrolledBy;\n                                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                                    const heightDiff = nextHeight - clampedNextHeight;\n                                    contentWrapper.style.height = clampedNextHeight + \"px\";\n                                    if (contentWrapper.style.bottom === \"0px\") {\n                                        viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                                        contentWrapper.style.justifyContent = \"flex-end\";\n                                    }\n                                }\n                            }\n                        }\n                        prevScrollTopRef.current = viewport.scrollTop;\n                    })\n                })\n            })\n        ]\n    });\n});\nSelectViewport.displayName = VIEWPORT_NAME;\nvar GROUP_NAME = \"SelectGroup\";\nvar [SelectGroupContextProvider, useSelectGroupContext] = createSelectContext(GROUP_NAME);\nvar SelectGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectGroupContextProvider, {\n        scope: __scopeSelect,\n        id: groupId,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n            role: \"group\",\n            \"aria-labelledby\": groupId,\n            ...groupProps,\n            ref: forwardedRef\n        })\n    });\n});\nSelectGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"SelectLabel\";\nvar SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        id: groupContext.id,\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nSelectLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"SelectItem\";\nvar [SelectItemContextProvider, useSelectItemContext] = createSelectContext(ITEM_NAME);\nvar SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, value, disabled = false, textValue: textValueProp, ...itemProps } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(textValueProp ?? \"\");\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>contentContext.itemRefCallback?.(node, value, disabled));\n    const textId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const handleSelect = ()=>{\n        if (!disabled) {\n            context.onValueChange(value);\n            context.onOpenChange(false);\n        }\n    };\n    if (value === \"\") {\n        throw new Error(\"A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\");\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectItemContextProvider, {\n        scope: __scopeSelect,\n        value,\n        disabled,\n        textId,\n        isSelected,\n        onItemTextChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n            setTextValue((prevTextValue)=>prevTextValue || (node?.textContent ?? \"\").trim());\n        }, []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n            scope: __scopeSelect,\n            value,\n            disabled,\n            textValue,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                role: \"option\",\n                \"aria-labelledby\": textId,\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-selected\": isSelected && isFocused,\n                \"data-state\": isSelected ? \"checked\" : \"unchecked\",\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                tabIndex: disabled ? void 0 : -1,\n                ...itemProps,\n                ref: composedRefs,\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onFocus, ()=>setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onBlur, ()=>setIsFocused(false)),\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onClick, ()=>{\n                    if (pointerTypeRef.current !== \"mouse\") handleSelect();\n                }),\n                onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerUp, ()=>{\n                    if (pointerTypeRef.current === \"mouse\") handleSelect();\n                }),\n                onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerDown, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                }),\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerMove, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                    if (disabled) {\n                        contentContext.onItemLeave?.();\n                    } else if (pointerTypeRef.current === \"mouse\") {\n                        event.currentTarget.focus({\n                            preventScroll: true\n                        });\n                    }\n                }),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerLeave, (event)=>{\n                    if (event.currentTarget === document.activeElement) {\n                        contentContext.onItemLeave?.();\n                    }\n                }),\n                onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onKeyDown, (event)=>{\n                    const isTypingAhead = contentContext.searchRef?.current !== \"\";\n                    if (isTypingAhead && event.key === \" \") return;\n                    if (SELECTION_KEYS.includes(event.key)) handleSelect();\n                    if (event.key === \" \") event.preventDefault();\n                })\n            })\n        })\n    });\n});\nSelectItem.displayName = ITEM_NAME;\nvar ITEM_TEXT_NAME = \"SelectItemText\";\nvar SelectItemText = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setItemTextNode(node), itemContext.onItemTextChange, (node)=>contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled));\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n            value: itemContext.value,\n            disabled: itemContext.disabled,\n            children: textContent\n        }, itemContext.value), [\n        itemContext.disabled,\n        itemContext.value,\n        textContent\n    ]);\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        onNativeOptionAdd(nativeOption);\n        return ()=>onNativeOptionRemove(nativeOption);\n    }, [\n        onNativeOptionAdd,\n        onNativeOptionRemove,\n        nativeOption\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n                id: itemContext.textId,\n                ...itemTextProps,\n                ref: composedRefs\n            }),\n            itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(itemTextProps.children, context.valueNode) : null\n        ]\n    });\n});\nSelectItemText.displayName = ITEM_TEXT_NAME;\nvar ITEM_INDICATOR_NAME = \"SelectItemIndicator\";\nvar SelectItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...itemIndicatorProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SCROLL_UP_BUTTON_NAME = \"SelectScrollUpButton\";\nvar SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollUp, setCanScrollUp] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (contentContext.viewport && contentContext.isPositioned) {\n            let handleScroll2 = function() {\n                const canScrollUp2 = viewport.scrollTop > 0;\n                setCanScrollUp(canScrollUp2);\n            };\n            var handleScroll = handleScroll2;\n            const viewport = contentContext.viewport;\n            handleScroll2();\n            viewport.addEventListener(\"scroll\", handleScroll2);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll2);\n        }\n    }, [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollUp ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\nvar SCROLL_DOWN_BUTTON_NAME = \"SelectScrollDownButton\";\nvar SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollDown, setCanScrollDown] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (contentContext.viewport && contentContext.isPositioned) {\n            let handleScroll2 = function() {\n                const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n                const canScrollDown2 = Math.ceil(viewport.scrollTop) < maxScroll;\n                setCanScrollDown(canScrollDown2);\n            };\n            var handleScroll = handleScroll2;\n            const viewport = contentContext.viewport;\n            handleScroll2();\n            viewport.addEventListener(\"scroll\", handleScroll2);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll2);\n        }\n    }, [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollDown ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\nvar SelectScrollButtonImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n    const contentContext = useSelectContentContext(\"SelectScrollButton\", __scopeSelect);\n    const autoScrollTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const getItems = useCollection(__scopeSelect);\n    const clearAutoScrollTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (autoScrollTimerRef.current !== null) {\n            window.clearInterval(autoScrollTimerRef.current);\n            autoScrollTimerRef.current = null;\n        }\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>clearAutoScrollTimer();\n    }, [\n        clearAutoScrollTimer\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        const activeItem = getItems().find((item)=>item.ref.current === document.activeElement);\n        activeItem?.ref.current?.scrollIntoView({\n            block: \"nearest\"\n        });\n    }, [\n        getItems\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...scrollIndicatorProps,\n        ref: forwardedRef,\n        style: {\n            flexShrink: 0,\n            ...scrollIndicatorProps.style\n        },\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerDown, ()=>{\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerMove, ()=>{\n            contentContext.onItemLeave?.();\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerLeave, ()=>{\n            clearAutoScrollTimer();\n        })\n    });\n});\nvar SEPARATOR_NAME = \"SelectSeparator\";\nvar SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nSelectSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"SelectArrow\";\nvar SelectArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === \"popper\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectArrow.displayName = ARROW_NAME;\nvar BUBBLE_INPUT_NAME = \"SelectBubbleInput\";\nvar SelectBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeSelect, value, ...props }, forwardedRef)=>{\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, ref);\n    const prevValue = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__.usePrevious)(value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const select = ref.current;\n        if (!select) return;\n        const selectProto = window.HTMLSelectElement.prototype;\n        const descriptor = Object.getOwnPropertyDescriptor(selectProto, \"value\");\n        const setValue = descriptor.set;\n        if (prevValue !== value && setValue) {\n            const event = new Event(\"change\", {\n                bubbles: true\n            });\n            setValue.call(select, value);\n            select.dispatchEvent(event);\n        }\n    }, [\n        prevValue,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.select, {\n        ...props,\n        style: {\n            ..._radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__.VISUALLY_HIDDEN_STYLES,\n            ...props.style\n        },\n        ref: composedRefs,\n        defaultValue: value\n    });\n});\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction shouldShowPlaceholder(value) {\n    return value === \"\" || value === void 0;\n}\nfunction useTypeaheadSearch(onSearchChange) {\n    const handleSearchChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__.useCallbackRef)(onSearchChange);\n    const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const handleTypeaheadSearch = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((key)=>{\n        const search = searchRef.current + key;\n        handleSearchChange(search);\n        (function updateSearch(value) {\n            searchRef.current = value;\n            window.clearTimeout(timerRef.current);\n            if (value !== \"\") timerRef.current = window.setTimeout(()=>updateSearch(\"\"), 1e3);\n        })(search);\n    }, [\n        handleSearchChange\n    ]);\n    const resetTypeahead = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        searchRef.current = \"\";\n        window.clearTimeout(timerRef.current);\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>window.clearTimeout(timerRef.current);\n    }, []);\n    return [\n        searchRef,\n        handleTypeaheadSearch,\n        resetTypeahead\n    ];\n}\nfunction findNextItem(items, search, currentItem) {\n    const isRepeated = search.length > 1 && Array.from(search).every((char)=>char === search[0]);\n    const normalizedSearch = isRepeated ? search[0] : search;\n    const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n    let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n    const excludeCurrentItem = normalizedSearch.length === 1;\n    if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v)=>v !== currentItem);\n    const nextItem = wrappedItems.find((item)=>item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase()));\n    return nextItem !== currentItem ? nextItem : void 0;\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nvar Root2 = Select;\nvar Trigger = SelectTrigger;\nvar Value = SelectValue;\nvar Icon = SelectIcon;\nvar Portal = SelectPortal;\nvar Content2 = SelectContent;\nvar Viewport = SelectViewport;\nvar Group = SelectGroup;\nvar Label = SelectLabel;\nvar Item = SelectItem;\nvar ItemText = SelectItemText;\nvar ItemIndicator = SelectItemIndicator;\nvar ScrollUpButton = SelectScrollUpButton;\nvar ScrollDownButton = SelectScrollDownButton;\nvar Separator = SelectSeparator;\nvar Arrow2 = SelectArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-select@2.2.5_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_74ff1e179f6928d61bde19586f0439bf/node_modules/@radix-ui/react-select/dist/index.mjs\n");

/***/ })

};
;