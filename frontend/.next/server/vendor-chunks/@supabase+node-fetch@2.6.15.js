"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+node-fetch@2.6.15";
exports.ids = ["vendor-chunks/@supabase+node-fetch@2.6.15"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js ***!
  \*******************************************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nfunction _interopDefault(ex) {\n    return ex && typeof ex === \"object\" && \"default\" in ex ? ex[\"default\"] : ex;\n}\nvar Stream = _interopDefault(__webpack_require__(/*! stream */ \"stream\"));\nvar http = _interopDefault(__webpack_require__(/*! http */ \"http\"));\nvar Url = _interopDefault(__webpack_require__(/*! url */ \"url\"));\nvar whatwgUrl = _interopDefault(__webpack_require__(/*! whatwg-url */ \"(ssr)/./node_modules/.pnpm/whatwg-url@5.0.0/node_modules/whatwg-url/lib/public-api.js\"));\nvar https = _interopDefault(__webpack_require__(/*! https */ \"https\"));\nvar zlib = _interopDefault(__webpack_require__(/*! zlib */ \"zlib\"));\n// Based on https://github.com/tmpvar/jsdom/blob/aa85b2abf07766ff7bf5c1f6daafb3726f2f2db5/lib/jsdom/living/blob.js\n// fix for \"Readable\" isn't a named export issue\nconst Readable = Stream.Readable;\nconst BUFFER = Symbol(\"buffer\");\nconst TYPE = Symbol(\"type\");\nclass Blob {\n    constructor(){\n        this[TYPE] = \"\";\n        const blobParts = arguments[0];\n        const options = arguments[1];\n        const buffers = [];\n        let size = 0;\n        if (blobParts) {\n            const a = blobParts;\n            const length = Number(a.length);\n            for(let i = 0; i < length; i++){\n                const element = a[i];\n                let buffer;\n                if (element instanceof Buffer) {\n                    buffer = element;\n                } else if (ArrayBuffer.isView(element)) {\n                    buffer = Buffer.from(element.buffer, element.byteOffset, element.byteLength);\n                } else if (element instanceof ArrayBuffer) {\n                    buffer = Buffer.from(element);\n                } else if (element instanceof Blob) {\n                    buffer = element[BUFFER];\n                } else {\n                    buffer = Buffer.from(typeof element === \"string\" ? element : String(element));\n                }\n                size += buffer.length;\n                buffers.push(buffer);\n            }\n        }\n        this[BUFFER] = Buffer.concat(buffers);\n        let type = options && options.type !== undefined && String(options.type).toLowerCase();\n        if (type && !/[^\\u0020-\\u007E]/.test(type)) {\n            this[TYPE] = type;\n        }\n    }\n    get size() {\n        return this[BUFFER].length;\n    }\n    get type() {\n        return this[TYPE];\n    }\n    text() {\n        return Promise.resolve(this[BUFFER].toString());\n    }\n    arrayBuffer() {\n        const buf = this[BUFFER];\n        const ab = buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.byteLength);\n        return Promise.resolve(ab);\n    }\n    stream() {\n        const readable = new Readable();\n        readable._read = function() {};\n        readable.push(this[BUFFER]);\n        readable.push(null);\n        return readable;\n    }\n    toString() {\n        return \"[object Blob]\";\n    }\n    slice() {\n        const size = this.size;\n        const start = arguments[0];\n        const end = arguments[1];\n        let relativeStart, relativeEnd;\n        if (start === undefined) {\n            relativeStart = 0;\n        } else if (start < 0) {\n            relativeStart = Math.max(size + start, 0);\n        } else {\n            relativeStart = Math.min(start, size);\n        }\n        if (end === undefined) {\n            relativeEnd = size;\n        } else if (end < 0) {\n            relativeEnd = Math.max(size + end, 0);\n        } else {\n            relativeEnd = Math.min(end, size);\n        }\n        const span = Math.max(relativeEnd - relativeStart, 0);\n        const buffer = this[BUFFER];\n        const slicedBuffer = buffer.slice(relativeStart, relativeStart + span);\n        const blob = new Blob([], {\n            type: arguments[2]\n        });\n        blob[BUFFER] = slicedBuffer;\n        return blob;\n    }\n}\nObject.defineProperties(Blob.prototype, {\n    size: {\n        enumerable: true\n    },\n    type: {\n        enumerable: true\n    },\n    slice: {\n        enumerable: true\n    }\n});\nObject.defineProperty(Blob.prototype, Symbol.toStringTag, {\n    value: \"Blob\",\n    writable: false,\n    enumerable: false,\n    configurable: true\n});\n/**\n * fetch-error.js\n *\n * FetchError interface for operational errors\n */ /**\n * Create FetchError instance\n *\n * @param   String      message      Error message for human\n * @param   String      type         Error type for machine\n * @param   String      systemError  For Node.js system error\n * @return  FetchError\n */ function FetchError(message, type, systemError) {\n    Error.call(this, message);\n    this.message = message;\n    this.type = type;\n    // when err.type is `system`, err.code contains system error code\n    if (systemError) {\n        this.code = this.errno = systemError.code;\n    }\n    // hide custom error implementation details from end-users\n    Error.captureStackTrace(this, this.constructor);\n}\nFetchError.prototype = Object.create(Error.prototype);\nFetchError.prototype.constructor = FetchError;\nFetchError.prototype.name = \"FetchError\";\nlet convert;\nconst INTERNALS = Symbol(\"Body internals\");\n// fix an issue where \"PassThrough\" isn't a named export for node <10\nconst PassThrough = Stream.PassThrough;\n/**\n * Body mixin\n *\n * Ref: https://fetch.spec.whatwg.org/#body\n *\n * @param   Stream  body  Readable stream\n * @param   Object  opts  Response options\n * @return  Void\n */ function Body(body) {\n    var _this = this;\n    var _ref = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {}, _ref$size = _ref.size;\n    let size = _ref$size === undefined ? 0 : _ref$size;\n    var _ref$timeout = _ref.timeout;\n    let timeout = _ref$timeout === undefined ? 0 : _ref$timeout;\n    if (body == null) {\n        // body is undefined or null\n        body = null;\n    } else if (isURLSearchParams(body)) {\n        // body is a URLSearchParams\n        body = Buffer.from(body.toString());\n    } else if (isBlob(body)) ;\n    else if (Buffer.isBuffer(body)) ;\n    else if (Object.prototype.toString.call(body) === \"[object ArrayBuffer]\") {\n        // body is ArrayBuffer\n        body = Buffer.from(body);\n    } else if (ArrayBuffer.isView(body)) {\n        // body is ArrayBufferView\n        body = Buffer.from(body.buffer, body.byteOffset, body.byteLength);\n    } else if (body instanceof Stream) ;\n    else {\n        // none of the above\n        // coerce to string then buffer\n        body = Buffer.from(String(body));\n    }\n    this[INTERNALS] = {\n        body,\n        disturbed: false,\n        error: null\n    };\n    this.size = size;\n    this.timeout = timeout;\n    if (body instanceof Stream) {\n        body.on(\"error\", function(err) {\n            const error = err.name === \"AbortError\" ? err : new FetchError(`Invalid response body while trying to fetch ${_this.url}: ${err.message}`, \"system\", err);\n            _this[INTERNALS].error = error;\n        });\n    }\n}\nBody.prototype = {\n    get body () {\n        return this[INTERNALS].body;\n    },\n    get bodyUsed () {\n        return this[INTERNALS].disturbed;\n    },\n    /**\n  * Decode response as ArrayBuffer\n  *\n  * @return  Promise\n  */ arrayBuffer () {\n        return consumeBody.call(this).then(function(buf) {\n            return buf.buffer.slice(buf.byteOffset, buf.byteOffset + buf.byteLength);\n        });\n    },\n    /**\n  * Return raw response as Blob\n  *\n  * @return Promise\n  */ blob () {\n        let ct = this.headers && this.headers.get(\"content-type\") || \"\";\n        return consumeBody.call(this).then(function(buf) {\n            return Object.assign(// Prevent copying\n            new Blob([], {\n                type: ct.toLowerCase()\n            }), {\n                [BUFFER]: buf\n            });\n        });\n    },\n    /**\n  * Decode response as json\n  *\n  * @return  Promise\n  */ json () {\n        var _this2 = this;\n        return consumeBody.call(this).then(function(buffer) {\n            try {\n                return JSON.parse(buffer.toString());\n            } catch (err) {\n                return Body.Promise.reject(new FetchError(`invalid json response body at ${_this2.url} reason: ${err.message}`, \"invalid-json\"));\n            }\n        });\n    },\n    /**\n  * Decode response as text\n  *\n  * @return  Promise\n  */ text () {\n        return consumeBody.call(this).then(function(buffer) {\n            return buffer.toString();\n        });\n    },\n    /**\n  * Decode response as buffer (non-spec api)\n  *\n  * @return  Promise\n  */ buffer () {\n        return consumeBody.call(this);\n    },\n    /**\n  * Decode response as text, while automatically detecting the encoding and\n  * trying to decode to UTF-8 (non-spec api)\n  *\n  * @return  Promise\n  */ textConverted () {\n        var _this3 = this;\n        return consumeBody.call(this).then(function(buffer) {\n            return convertBody(buffer, _this3.headers);\n        });\n    }\n};\n// In browsers, all properties are enumerable.\nObject.defineProperties(Body.prototype, {\n    body: {\n        enumerable: true\n    },\n    bodyUsed: {\n        enumerable: true\n    },\n    arrayBuffer: {\n        enumerable: true\n    },\n    blob: {\n        enumerable: true\n    },\n    json: {\n        enumerable: true\n    },\n    text: {\n        enumerable: true\n    }\n});\nBody.mixIn = function(proto) {\n    for (const name of Object.getOwnPropertyNames(Body.prototype)){\n        // istanbul ignore else: future proof\n        if (!(name in proto)) {\n            const desc = Object.getOwnPropertyDescriptor(Body.prototype, name);\n            Object.defineProperty(proto, name, desc);\n        }\n    }\n};\n/**\n * Consume and convert an entire Body to a Buffer.\n *\n * Ref: https://fetch.spec.whatwg.org/#concept-body-consume-body\n *\n * @return  Promise\n */ function consumeBody() {\n    var _this4 = this;\n    if (this[INTERNALS].disturbed) {\n        return Body.Promise.reject(new TypeError(`body used already for: ${this.url}`));\n    }\n    this[INTERNALS].disturbed = true;\n    if (this[INTERNALS].error) {\n        return Body.Promise.reject(this[INTERNALS].error);\n    }\n    let body = this.body;\n    // body is null\n    if (body === null) {\n        return Body.Promise.resolve(Buffer.alloc(0));\n    }\n    // body is blob\n    if (isBlob(body)) {\n        body = body.stream();\n    }\n    // body is buffer\n    if (Buffer.isBuffer(body)) {\n        return Body.Promise.resolve(body);\n    }\n    // istanbul ignore if: should never happen\n    if (!(body instanceof Stream)) {\n        return Body.Promise.resolve(Buffer.alloc(0));\n    }\n    // body is stream\n    // get ready to actually consume the body\n    let accum = [];\n    let accumBytes = 0;\n    let abort = false;\n    return new Body.Promise(function(resolve, reject) {\n        let resTimeout;\n        // allow timeout on slow response body\n        if (_this4.timeout) {\n            resTimeout = setTimeout(function() {\n                abort = true;\n                reject(new FetchError(`Response timeout while trying to fetch ${_this4.url} (over ${_this4.timeout}ms)`, \"body-timeout\"));\n            }, _this4.timeout);\n        }\n        // handle stream errors\n        body.on(\"error\", function(err) {\n            if (err.name === \"AbortError\") {\n                // if the request was aborted, reject with this Error\n                abort = true;\n                reject(err);\n            } else {\n                // other errors, such as incorrect content-encoding\n                reject(new FetchError(`Invalid response body while trying to fetch ${_this4.url}: ${err.message}`, \"system\", err));\n            }\n        });\n        body.on(\"data\", function(chunk) {\n            if (abort || chunk === null) {\n                return;\n            }\n            if (_this4.size && accumBytes + chunk.length > _this4.size) {\n                abort = true;\n                reject(new FetchError(`content size at ${_this4.url} over limit: ${_this4.size}`, \"max-size\"));\n                return;\n            }\n            accumBytes += chunk.length;\n            accum.push(chunk);\n        });\n        body.on(\"end\", function() {\n            if (abort) {\n                return;\n            }\n            clearTimeout(resTimeout);\n            try {\n                resolve(Buffer.concat(accum, accumBytes));\n            } catch (err) {\n                // handle streams that have accumulated too much data (issue #414)\n                reject(new FetchError(`Could not create Buffer from response body for ${_this4.url}: ${err.message}`, \"system\", err));\n            }\n        });\n    });\n}\n/**\n * Detect buffer encoding and convert to target encoding\n * ref: http://www.w3.org/TR/2011/WD-html5-20110113/parsing.html#determining-the-character-encoding\n *\n * @param   Buffer  buffer    Incoming buffer\n * @param   String  encoding  Target encoding\n * @return  String\n */ function convertBody(buffer, headers) {\n    {\n        throw new Error(\"The package `encoding` must be installed to use the textConverted() function\");\n    }\n    const ct = headers.get(\"content-type\");\n    let charset = \"utf-8\";\n    let res, str;\n    // header\n    if (ct) {\n        res = /charset=([^;]*)/i.exec(ct);\n    }\n    // no charset in content type, peek at response body for at most 1024 bytes\n    str = buffer.slice(0, 1024).toString();\n    // html5\n    if (!res && str) {\n        res = /<meta.+?charset=(['\"])(.+?)\\1/i.exec(str);\n    }\n    // html4\n    if (!res && str) {\n        res = /<meta[\\s]+?http-equiv=(['\"])content-type\\1[\\s]+?content=(['\"])(.+?)\\2/i.exec(str);\n        if (!res) {\n            res = /<meta[\\s]+?content=(['\"])(.+?)\\1[\\s]+?http-equiv=(['\"])content-type\\3/i.exec(str);\n            if (res) {\n                res.pop(); // drop last quote\n            }\n        }\n        if (res) {\n            res = /charset=(.*)/i.exec(res.pop());\n        }\n    }\n    // xml\n    if (!res && str) {\n        res = /<\\?xml.+?encoding=(['\"])(.+?)\\1/i.exec(str);\n    }\n    // found charset\n    if (res) {\n        charset = res.pop();\n        // prevent decode issues when sites use incorrect encoding\n        // ref: https://hsivonen.fi/encoding-menu/\n        if (charset === \"gb2312\" || charset === \"gbk\") {\n            charset = \"gb18030\";\n        }\n    }\n    // turn raw buffers into a single utf-8 buffer\n    return convert(buffer, \"UTF-8\", charset).toString();\n}\n/**\n * Detect a URLSearchParams object\n * ref: https://github.com/bitinn/node-fetch/issues/296#issuecomment-307598143\n *\n * @param   Object  obj     Object to detect by type or brand\n * @return  String\n */ function isURLSearchParams(obj) {\n    // Duck-typing as a necessary condition.\n    if (typeof obj !== \"object\" || typeof obj.append !== \"function\" || typeof obj.delete !== \"function\" || typeof obj.get !== \"function\" || typeof obj.getAll !== \"function\" || typeof obj.has !== \"function\" || typeof obj.set !== \"function\") {\n        return false;\n    }\n    // Brand-checking and more duck-typing as optional condition.\n    return obj.constructor.name === \"URLSearchParams\" || Object.prototype.toString.call(obj) === \"[object URLSearchParams]\" || typeof obj.sort === \"function\";\n}\n/**\n * Check if `obj` is a W3C `Blob` object (which `File` inherits from)\n * @param  {*} obj\n * @return {boolean}\n */ function isBlob(obj) {\n    return typeof obj === \"object\" && typeof obj.arrayBuffer === \"function\" && typeof obj.type === \"string\" && typeof obj.stream === \"function\" && typeof obj.constructor === \"function\" && typeof obj.constructor.name === \"string\" && /^(Blob|File)$/.test(obj.constructor.name) && /^(Blob|File)$/.test(obj[Symbol.toStringTag]);\n}\n/**\n * Clone body given Res/Req instance\n *\n * @param   Mixed  instance  Response or Request instance\n * @return  Mixed\n */ function clone(instance) {\n    let p1, p2;\n    let body = instance.body;\n    // don't allow cloning a used body\n    if (instance.bodyUsed) {\n        throw new Error(\"cannot clone body after it is used\");\n    }\n    // check that body is a stream and not form-data object\n    // note: we can't clone the form-data object without having it as a dependency\n    if (body instanceof Stream && typeof body.getBoundary !== \"function\") {\n        // tee instance body\n        p1 = new PassThrough();\n        p2 = new PassThrough();\n        body.pipe(p1);\n        body.pipe(p2);\n        // set instance body to teed body and return the other teed body\n        instance[INTERNALS].body = p1;\n        body = p2;\n    }\n    return body;\n}\n/**\n * Performs the operation \"extract a `Content-Type` value from |object|\" as\n * specified in the specification:\n * https://fetch.spec.whatwg.org/#concept-bodyinit-extract\n *\n * This function assumes that instance.body is present.\n *\n * @param   Mixed  instance  Any options.body input\n */ function extractContentType(body) {\n    if (body === null) {\n        // body is null\n        return null;\n    } else if (typeof body === \"string\") {\n        // body is string\n        return \"text/plain;charset=UTF-8\";\n    } else if (isURLSearchParams(body)) {\n        // body is a URLSearchParams\n        return \"application/x-www-form-urlencoded;charset=UTF-8\";\n    } else if (isBlob(body)) {\n        // body is blob\n        return body.type || null;\n    } else if (Buffer.isBuffer(body)) {\n        // body is buffer\n        return null;\n    } else if (Object.prototype.toString.call(body) === \"[object ArrayBuffer]\") {\n        // body is ArrayBuffer\n        return null;\n    } else if (ArrayBuffer.isView(body)) {\n        // body is ArrayBufferView\n        return null;\n    } else if (typeof body.getBoundary === \"function\") {\n        // detect form data input from form-data module\n        return `multipart/form-data;boundary=${body.getBoundary()}`;\n    } else if (body instanceof Stream) {\n        // body is stream\n        // can't really do much about this\n        return null;\n    } else {\n        // Body constructor defaults other things to string\n        return \"text/plain;charset=UTF-8\";\n    }\n}\n/**\n * The Fetch Standard treats this as if \"total bytes\" is a property on the body.\n * For us, we have to explicitly get it with a function.\n *\n * ref: https://fetch.spec.whatwg.org/#concept-body-total-bytes\n *\n * @param   Body    instance   Instance of Body\n * @return  Number?            Number of bytes, or null if not possible\n */ function getTotalBytes(instance) {\n    const body = instance.body;\n    if (body === null) {\n        // body is null\n        return 0;\n    } else if (isBlob(body)) {\n        return body.size;\n    } else if (Buffer.isBuffer(body)) {\n        // body is buffer\n        return body.length;\n    } else if (body && typeof body.getLengthSync === \"function\") {\n        // detect form data input from form-data module\n        if (body._lengthRetrievers && body._lengthRetrievers.length == 0 || // 1.x\n        body.hasKnownLength && body.hasKnownLength()) {\n            // 2.x\n            return body.getLengthSync();\n        }\n        return null;\n    } else {\n        // body is stream\n        return null;\n    }\n}\n/**\n * Write a Body to a Node.js WritableStream (e.g. http.Request) object.\n *\n * @param   Body    instance   Instance of Body\n * @return  Void\n */ function writeToStream(dest, instance) {\n    const body = instance.body;\n    if (body === null) {\n        // body is null\n        dest.end();\n    } else if (isBlob(body)) {\n        body.stream().pipe(dest);\n    } else if (Buffer.isBuffer(body)) {\n        // body is buffer\n        dest.write(body);\n        dest.end();\n    } else {\n        // body is stream\n        body.pipe(dest);\n    }\n}\n// expose Promise\nBody.Promise = global.Promise;\n/**\n * headers.js\n *\n * Headers class offers convenient helpers\n */ const invalidTokenRegex = /[^\\^_`a-zA-Z\\-0-9!#$%&'*+.|~]/;\nconst invalidHeaderCharRegex = /[^\\t\\x20-\\x7e\\x80-\\xff]/;\nfunction validateName(name) {\n    name = `${name}`;\n    if (invalidTokenRegex.test(name) || name === \"\") {\n        throw new TypeError(`${name} is not a legal HTTP header name`);\n    }\n}\nfunction validateValue(value) {\n    value = `${value}`;\n    if (invalidHeaderCharRegex.test(value)) {\n        throw new TypeError(`${value} is not a legal HTTP header value`);\n    }\n}\n/**\n * Find the key in the map object given a header name.\n *\n * Returns undefined if not found.\n *\n * @param   String  name  Header name\n * @return  String|Undefined\n */ function find(map, name) {\n    name = name.toLowerCase();\n    for(const key in map){\n        if (key.toLowerCase() === name) {\n            return key;\n        }\n    }\n    return undefined;\n}\nconst MAP = Symbol(\"map\");\nclass Headers {\n    /**\n  * Headers class\n  *\n  * @param   Object  headers  Response headers\n  * @return  Void\n  */ constructor(){\n        let init = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : undefined;\n        this[MAP] = Object.create(null);\n        if (init instanceof Headers) {\n            const rawHeaders = init.raw();\n            const headerNames = Object.keys(rawHeaders);\n            for (const headerName of headerNames){\n                for (const value of rawHeaders[headerName]){\n                    this.append(headerName, value);\n                }\n            }\n            return;\n        }\n        // We don't worry about converting prop to ByteString here as append()\n        // will handle it.\n        if (init == null) ;\n        else if (typeof init === \"object\") {\n            const method = init[Symbol.iterator];\n            if (method != null) {\n                if (typeof method !== \"function\") {\n                    throw new TypeError(\"Header pairs must be iterable\");\n                }\n                // sequence<sequence<ByteString>>\n                // Note: per spec we have to first exhaust the lists then process them\n                const pairs = [];\n                for (const pair of init){\n                    if (typeof pair !== \"object\" || typeof pair[Symbol.iterator] !== \"function\") {\n                        throw new TypeError(\"Each header pair must be iterable\");\n                    }\n                    pairs.push(Array.from(pair));\n                }\n                for (const pair of pairs){\n                    if (pair.length !== 2) {\n                        throw new TypeError(\"Each header pair must be a name/value tuple\");\n                    }\n                    this.append(pair[0], pair[1]);\n                }\n            } else {\n                // record<ByteString, ByteString>\n                for (const key of Object.keys(init)){\n                    const value = init[key];\n                    this.append(key, value);\n                }\n            }\n        } else {\n            throw new TypeError(\"Provided initializer must be an object\");\n        }\n    }\n    /**\n  * Return combined header value given name\n  *\n  * @param   String  name  Header name\n  * @return  Mixed\n  */ get(name) {\n        name = `${name}`;\n        validateName(name);\n        const key = find(this[MAP], name);\n        if (key === undefined) {\n            return null;\n        }\n        return this[MAP][key].join(\", \");\n    }\n    /**\n  * Iterate over all headers\n  *\n  * @param   Function  callback  Executed for each item with parameters (value, name, thisArg)\n  * @param   Boolean   thisArg   `this` context for callback function\n  * @return  Void\n  */ forEach(callback) {\n        let thisArg = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : undefined;\n        let pairs = getHeaders(this);\n        let i = 0;\n        while(i < pairs.length){\n            var _pairs$i = pairs[i];\n            const name = _pairs$i[0], value = _pairs$i[1];\n            callback.call(thisArg, value, name, this);\n            pairs = getHeaders(this);\n            i++;\n        }\n    }\n    /**\n  * Overwrite header values given name\n  *\n  * @param   String  name   Header name\n  * @param   String  value  Header value\n  * @return  Void\n  */ set(name, value) {\n        name = `${name}`;\n        value = `${value}`;\n        validateName(name);\n        validateValue(value);\n        const key = find(this[MAP], name);\n        this[MAP][key !== undefined ? key : name] = [\n            value\n        ];\n    }\n    /**\n  * Append a value onto existing header\n  *\n  * @param   String  name   Header name\n  * @param   String  value  Header value\n  * @return  Void\n  */ append(name, value) {\n        name = `${name}`;\n        value = `${value}`;\n        validateName(name);\n        validateValue(value);\n        const key = find(this[MAP], name);\n        if (key !== undefined) {\n            this[MAP][key].push(value);\n        } else {\n            this[MAP][name] = [\n                value\n            ];\n        }\n    }\n    /**\n  * Check for header name existence\n  *\n  * @param   String   name  Header name\n  * @return  Boolean\n  */ has(name) {\n        name = `${name}`;\n        validateName(name);\n        return find(this[MAP], name) !== undefined;\n    }\n    /**\n  * Delete all header values given name\n  *\n  * @param   String  name  Header name\n  * @return  Void\n  */ delete(name) {\n        name = `${name}`;\n        validateName(name);\n        const key = find(this[MAP], name);\n        if (key !== undefined) {\n            delete this[MAP][key];\n        }\n    }\n    /**\n  * Return raw headers (non-spec api)\n  *\n  * @return  Object\n  */ raw() {\n        return this[MAP];\n    }\n    /**\n  * Get an iterator on keys.\n  *\n  * @return  Iterator\n  */ keys() {\n        return createHeadersIterator(this, \"key\");\n    }\n    /**\n  * Get an iterator on values.\n  *\n  * @return  Iterator\n  */ values() {\n        return createHeadersIterator(this, \"value\");\n    }\n    /**\n  * Get an iterator on entries.\n  *\n  * This is the default iterator of the Headers object.\n  *\n  * @return  Iterator\n  */ [Symbol.iterator]() {\n        return createHeadersIterator(this, \"key+value\");\n    }\n}\nHeaders.prototype.entries = Headers.prototype[Symbol.iterator];\nObject.defineProperty(Headers.prototype, Symbol.toStringTag, {\n    value: \"Headers\",\n    writable: false,\n    enumerable: false,\n    configurable: true\n});\nObject.defineProperties(Headers.prototype, {\n    get: {\n        enumerable: true\n    },\n    forEach: {\n        enumerable: true\n    },\n    set: {\n        enumerable: true\n    },\n    append: {\n        enumerable: true\n    },\n    has: {\n        enumerable: true\n    },\n    delete: {\n        enumerable: true\n    },\n    keys: {\n        enumerable: true\n    },\n    values: {\n        enumerable: true\n    },\n    entries: {\n        enumerable: true\n    }\n});\nfunction getHeaders(headers) {\n    let kind = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"key+value\";\n    const keys = Object.keys(headers[MAP]).sort();\n    return keys.map(kind === \"key\" ? function(k) {\n        return k.toLowerCase();\n    } : kind === \"value\" ? function(k) {\n        return headers[MAP][k].join(\", \");\n    } : function(k) {\n        return [\n            k.toLowerCase(),\n            headers[MAP][k].join(\", \")\n        ];\n    });\n}\nconst INTERNAL = Symbol(\"internal\");\nfunction createHeadersIterator(target, kind) {\n    const iterator = Object.create(HeadersIteratorPrototype);\n    iterator[INTERNAL] = {\n        target,\n        kind,\n        index: 0\n    };\n    return iterator;\n}\nconst HeadersIteratorPrototype = Object.setPrototypeOf({\n    next () {\n        // istanbul ignore if\n        if (!this || Object.getPrototypeOf(this) !== HeadersIteratorPrototype) {\n            throw new TypeError(\"Value of `this` is not a HeadersIterator\");\n        }\n        var _INTERNAL = this[INTERNAL];\n        const target = _INTERNAL.target, kind = _INTERNAL.kind, index = _INTERNAL.index;\n        const values = getHeaders(target, kind);\n        const len = values.length;\n        if (index >= len) {\n            return {\n                value: undefined,\n                done: true\n            };\n        }\n        this[INTERNAL].index = index + 1;\n        return {\n            value: values[index],\n            done: false\n        };\n    }\n}, Object.getPrototypeOf(Object.getPrototypeOf([][Symbol.iterator]())));\nObject.defineProperty(HeadersIteratorPrototype, Symbol.toStringTag, {\n    value: \"HeadersIterator\",\n    writable: false,\n    enumerable: false,\n    configurable: true\n});\n/**\n * Export the Headers object in a form that Node.js can consume.\n *\n * @param   Headers  headers\n * @return  Object\n */ function exportNodeCompatibleHeaders(headers) {\n    const obj = Object.assign({\n        __proto__: null\n    }, headers[MAP]);\n    // http.request() only supports string as Host header. This hack makes\n    // specifying custom Host header possible.\n    const hostHeaderKey = find(headers[MAP], \"Host\");\n    if (hostHeaderKey !== undefined) {\n        obj[hostHeaderKey] = obj[hostHeaderKey][0];\n    }\n    return obj;\n}\n/**\n * Create a Headers object from an object of headers, ignoring those that do\n * not conform to HTTP grammar productions.\n *\n * @param   Object  obj  Object of headers\n * @return  Headers\n */ function createHeadersLenient(obj) {\n    const headers = new Headers();\n    for (const name of Object.keys(obj)){\n        if (invalidTokenRegex.test(name)) {\n            continue;\n        }\n        if (Array.isArray(obj[name])) {\n            for (const val of obj[name]){\n                if (invalidHeaderCharRegex.test(val)) {\n                    continue;\n                }\n                if (headers[MAP][name] === undefined) {\n                    headers[MAP][name] = [\n                        val\n                    ];\n                } else {\n                    headers[MAP][name].push(val);\n                }\n            }\n        } else if (!invalidHeaderCharRegex.test(obj[name])) {\n            headers[MAP][name] = [\n                obj[name]\n            ];\n        }\n    }\n    return headers;\n}\nconst INTERNALS$1 = Symbol(\"Response internals\");\n// fix an issue where \"STATUS_CODES\" aren't a named export for node <10\nconst STATUS_CODES = http.STATUS_CODES;\n/**\n * Response class\n *\n * @param   Stream  body  Readable stream\n * @param   Object  opts  Response options\n * @return  Void\n */ class Response {\n    constructor(){\n        let body = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;\n        let opts = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        Body.call(this, body, opts);\n        const status = opts.status || 200;\n        const headers = new Headers(opts.headers);\n        if (body != null && !headers.has(\"Content-Type\")) {\n            const contentType = extractContentType(body);\n            if (contentType) {\n                headers.append(\"Content-Type\", contentType);\n            }\n        }\n        this[INTERNALS$1] = {\n            url: opts.url,\n            status,\n            statusText: opts.statusText || STATUS_CODES[status],\n            headers,\n            counter: opts.counter\n        };\n    }\n    get url() {\n        return this[INTERNALS$1].url || \"\";\n    }\n    get status() {\n        return this[INTERNALS$1].status;\n    }\n    /**\n  * Convenience property representing if the request ended normally\n  */ get ok() {\n        return this[INTERNALS$1].status >= 200 && this[INTERNALS$1].status < 300;\n    }\n    get redirected() {\n        return this[INTERNALS$1].counter > 0;\n    }\n    get statusText() {\n        return this[INTERNALS$1].statusText;\n    }\n    get headers() {\n        return this[INTERNALS$1].headers;\n    }\n    /**\n  * Clone this response\n  *\n  * @return  Response\n  */ clone() {\n        return new Response(clone(this), {\n            url: this.url,\n            status: this.status,\n            statusText: this.statusText,\n            headers: this.headers,\n            ok: this.ok,\n            redirected: this.redirected\n        });\n    }\n}\nBody.mixIn(Response.prototype);\nObject.defineProperties(Response.prototype, {\n    url: {\n        enumerable: true\n    },\n    status: {\n        enumerable: true\n    },\n    ok: {\n        enumerable: true\n    },\n    redirected: {\n        enumerable: true\n    },\n    statusText: {\n        enumerable: true\n    },\n    headers: {\n        enumerable: true\n    },\n    clone: {\n        enumerable: true\n    }\n});\nObject.defineProperty(Response.prototype, Symbol.toStringTag, {\n    value: \"Response\",\n    writable: false,\n    enumerable: false,\n    configurable: true\n});\nconst INTERNALS$2 = Symbol(\"Request internals\");\nconst URL = Url.URL || whatwgUrl.URL;\n// fix an issue where \"format\", \"parse\" aren't a named export for node <10\nconst parse_url = Url.parse;\nconst format_url = Url.format;\n/**\n * Wrapper around `new URL` to handle arbitrary URLs\n *\n * @param  {string} urlStr\n * @return {void}\n */ function parseURL(urlStr) {\n    /*\n \tCheck whether the URL is absolute or not\n \t\tScheme: https://tools.ietf.org/html/rfc3986#section-3.1\n \tAbsolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\n */ if (/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.exec(urlStr)) {\n        urlStr = new URL(urlStr).toString();\n    }\n    // Fallback to old implementation for arbitrary URLs\n    return parse_url(urlStr);\n}\nconst streamDestructionSupported = \"destroy\" in Stream.Readable.prototype;\n/**\n * Check if a value is an instance of Request.\n *\n * @param   Mixed   input\n * @return  Boolean\n */ function isRequest(input) {\n    return typeof input === \"object\" && typeof input[INTERNALS$2] === \"object\";\n}\nfunction isAbortSignal(signal) {\n    const proto = signal && typeof signal === \"object\" && Object.getPrototypeOf(signal);\n    return !!(proto && proto.constructor.name === \"AbortSignal\");\n}\n/**\n * Request class\n *\n * @param   Mixed   input  Url or Request instance\n * @param   Object  init   Custom options\n * @return  Void\n */ class Request {\n    constructor(input){\n        let init = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n        let parsedURL;\n        // normalize input\n        if (!isRequest(input)) {\n            if (input && input.href) {\n                // in order to support Node.js' Url objects; though WHATWG's URL objects\n                // will fall into this branch also (since their `toString()` will return\n                // `href` property anyway)\n                parsedURL = parseURL(input.href);\n            } else {\n                // coerce input to a string before attempting to parse\n                parsedURL = parseURL(`${input}`);\n            }\n            input = {};\n        } else {\n            parsedURL = parseURL(input.url);\n        }\n        let method = init.method || input.method || \"GET\";\n        method = method.toUpperCase();\n        if ((init.body != null || isRequest(input) && input.body !== null) && (method === \"GET\" || method === \"HEAD\")) {\n            throw new TypeError(\"Request with GET/HEAD method cannot have body\");\n        }\n        let inputBody = init.body != null ? init.body : isRequest(input) && input.body !== null ? clone(input) : null;\n        Body.call(this, inputBody, {\n            timeout: init.timeout || input.timeout || 0,\n            size: init.size || input.size || 0\n        });\n        const headers = new Headers(init.headers || input.headers || {});\n        if (inputBody != null && !headers.has(\"Content-Type\")) {\n            const contentType = extractContentType(inputBody);\n            if (contentType) {\n                headers.append(\"Content-Type\", contentType);\n            }\n        }\n        let signal = isRequest(input) ? input.signal : null;\n        if (\"signal\" in init) signal = init.signal;\n        if (signal != null && !isAbortSignal(signal)) {\n            throw new TypeError(\"Expected signal to be an instanceof AbortSignal\");\n        }\n        this[INTERNALS$2] = {\n            method,\n            redirect: init.redirect || input.redirect || \"follow\",\n            headers,\n            parsedURL,\n            signal\n        };\n        // node-fetch-only options\n        this.follow = init.follow !== undefined ? init.follow : input.follow !== undefined ? input.follow : 20;\n        this.compress = init.compress !== undefined ? init.compress : input.compress !== undefined ? input.compress : true;\n        this.counter = init.counter || input.counter || 0;\n        this.agent = init.agent || input.agent;\n    }\n    get method() {\n        return this[INTERNALS$2].method;\n    }\n    get url() {\n        return format_url(this[INTERNALS$2].parsedURL);\n    }\n    get headers() {\n        return this[INTERNALS$2].headers;\n    }\n    get redirect() {\n        return this[INTERNALS$2].redirect;\n    }\n    get signal() {\n        return this[INTERNALS$2].signal;\n    }\n    /**\n  * Clone this request\n  *\n  * @return  Request\n  */ clone() {\n        return new Request(this);\n    }\n}\nBody.mixIn(Request.prototype);\nObject.defineProperty(Request.prototype, Symbol.toStringTag, {\n    value: \"Request\",\n    writable: false,\n    enumerable: false,\n    configurable: true\n});\nObject.defineProperties(Request.prototype, {\n    method: {\n        enumerable: true\n    },\n    url: {\n        enumerable: true\n    },\n    headers: {\n        enumerable: true\n    },\n    redirect: {\n        enumerable: true\n    },\n    clone: {\n        enumerable: true\n    },\n    signal: {\n        enumerable: true\n    }\n});\n/**\n * Convert a Request to Node.js http request options.\n *\n * @param   Request  A Request instance\n * @return  Object   The options object to be passed to http.request\n */ function getNodeRequestOptions(request) {\n    const parsedURL = request[INTERNALS$2].parsedURL;\n    const headers = new Headers(request[INTERNALS$2].headers);\n    // fetch step 1.3\n    if (!headers.has(\"Accept\")) {\n        headers.set(\"Accept\", \"*/*\");\n    }\n    // Basic fetch\n    if (!parsedURL.protocol || !parsedURL.hostname) {\n        throw new TypeError(\"Only absolute URLs are supported\");\n    }\n    if (!/^https?:$/.test(parsedURL.protocol)) {\n        throw new TypeError(\"Only HTTP(S) protocols are supported\");\n    }\n    if (request.signal && request.body instanceof Stream.Readable && !streamDestructionSupported) {\n        throw new Error(\"Cancellation of streamed requests with AbortSignal is not supported in node < 8\");\n    }\n    // HTTP-network-or-cache fetch steps 2.4-2.7\n    let contentLengthValue = null;\n    if (request.body == null && /^(POST|PUT)$/i.test(request.method)) {\n        contentLengthValue = \"0\";\n    }\n    if (request.body != null) {\n        const totalBytes = getTotalBytes(request);\n        if (typeof totalBytes === \"number\") {\n            contentLengthValue = String(totalBytes);\n        }\n    }\n    if (contentLengthValue) {\n        headers.set(\"Content-Length\", contentLengthValue);\n    }\n    // HTTP-network-or-cache fetch step 2.11\n    if (!headers.has(\"User-Agent\")) {\n        headers.set(\"User-Agent\", \"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)\");\n    }\n    // HTTP-network-or-cache fetch step 2.15\n    if (request.compress && !headers.has(\"Accept-Encoding\")) {\n        headers.set(\"Accept-Encoding\", \"gzip,deflate\");\n    }\n    let agent = request.agent;\n    if (typeof agent === \"function\") {\n        agent = agent(parsedURL);\n    }\n    if (!headers.has(\"Connection\") && !agent) {\n        headers.set(\"Connection\", \"close\");\n    }\n    // HTTP-network fetch step 4.2\n    // chunked encoding is handled by Node.js\n    return Object.assign({}, parsedURL, {\n        method: request.method,\n        headers: exportNodeCompatibleHeaders(headers),\n        agent\n    });\n}\n/**\n * abort-error.js\n *\n * AbortError interface for cancelled requests\n */ /**\n * Create AbortError instance\n *\n * @param   String      message      Error message for human\n * @return  AbortError\n */ function AbortError(message) {\n    Error.call(this, message);\n    this.type = \"aborted\";\n    this.message = message;\n    // hide custom error implementation details from end-users\n    Error.captureStackTrace(this, this.constructor);\n}\nAbortError.prototype = Object.create(Error.prototype);\nAbortError.prototype.constructor = AbortError;\nAbortError.prototype.name = \"AbortError\";\nconst URL$1 = Url.URL || whatwgUrl.URL;\n// fix an issue where \"PassThrough\", \"resolve\" aren't a named export for node <10\nconst PassThrough$1 = Stream.PassThrough;\nconst isDomainOrSubdomain = function isDomainOrSubdomain(destination, original) {\n    const orig = new URL$1(original).hostname;\n    const dest = new URL$1(destination).hostname;\n    return orig === dest || orig[orig.length - dest.length - 1] === \".\" && orig.endsWith(dest);\n};\n/**\n * isSameProtocol reports whether the two provided URLs use the same protocol.\n *\n * Both domains must already be in canonical form.\n * @param {string|URL} original\n * @param {string|URL} destination\n */ const isSameProtocol = function isSameProtocol(destination, original) {\n    const orig = new URL$1(original).protocol;\n    const dest = new URL$1(destination).protocol;\n    return orig === dest;\n};\n/**\n * Fetch function\n *\n * @param   Mixed    url   Absolute url or Request instance\n * @param   Object   opts  Fetch options\n * @return  Promise\n */ function fetch(url, opts) {\n    // allow custom promise\n    if (!fetch.Promise) {\n        throw new Error(\"native promise missing, set fetch.Promise to your favorite alternative\");\n    }\n    Body.Promise = fetch.Promise;\n    // wrap http.request into fetch\n    return new fetch.Promise(function(resolve, reject) {\n        // build request object\n        const request = new Request(url, opts);\n        const options = getNodeRequestOptions(request);\n        const send = (options.protocol === \"https:\" ? https : http).request;\n        const signal = request.signal;\n        let response = null;\n        const abort = function abort() {\n            let error = new AbortError(\"The user aborted a request.\");\n            reject(error);\n            if (request.body && request.body instanceof Stream.Readable) {\n                destroyStream(request.body, error);\n            }\n            if (!response || !response.body) return;\n            response.body.emit(\"error\", error);\n        };\n        if (signal && signal.aborted) {\n            abort();\n            return;\n        }\n        const abortAndFinalize = function abortAndFinalize() {\n            abort();\n            finalize();\n        };\n        // send request\n        const req = send(options);\n        let reqTimeout;\n        if (signal) {\n            signal.addEventListener(\"abort\", abortAndFinalize);\n        }\n        function finalize() {\n            req.abort();\n            if (signal) signal.removeEventListener(\"abort\", abortAndFinalize);\n            clearTimeout(reqTimeout);\n        }\n        if (request.timeout) {\n            req.once(\"socket\", function(socket) {\n                reqTimeout = setTimeout(function() {\n                    reject(new FetchError(`network timeout at: ${request.url}`, \"request-timeout\"));\n                    finalize();\n                }, request.timeout);\n            });\n        }\n        req.on(\"error\", function(err) {\n            reject(new FetchError(`request to ${request.url} failed, reason: ${err.message}`, \"system\", err));\n            if (response && response.body) {\n                destroyStream(response.body, err);\n            }\n            finalize();\n        });\n        fixResponseChunkedTransferBadEnding(req, function(err) {\n            if (signal && signal.aborted) {\n                return;\n            }\n            if (response && response.body) {\n                destroyStream(response.body, err);\n            }\n        });\n        /* c8 ignore next 18 */ if (parseInt(process.version.substring(1)) < 14) {\n            // Before Node.js 14, pipeline() does not fully support async iterators and does not always\n            // properly handle when the socket close/end events are out of order.\n            req.on(\"socket\", function(s) {\n                s.addListener(\"close\", function(hadError) {\n                    // if a data listener is still present we didn't end cleanly\n                    const hasDataListener = s.listenerCount(\"data\") > 0;\n                    // if end happened before close but the socket didn't emit an error, do it now\n                    if (response && hasDataListener && !hadError && !(signal && signal.aborted)) {\n                        const err = new Error(\"Premature close\");\n                        err.code = \"ERR_STREAM_PREMATURE_CLOSE\";\n                        response.body.emit(\"error\", err);\n                    }\n                });\n            });\n        }\n        req.on(\"response\", function(res) {\n            clearTimeout(reqTimeout);\n            const headers = createHeadersLenient(res.headers);\n            // HTTP fetch step 5\n            if (fetch.isRedirect(res.statusCode)) {\n                // HTTP fetch step 5.2\n                const location = headers.get(\"Location\");\n                // HTTP fetch step 5.3\n                let locationURL = null;\n                try {\n                    locationURL = location === null ? null : new URL$1(location, request.url).toString();\n                } catch (err) {\n                    // error here can only be invalid URL in Location: header\n                    // do not throw when options.redirect == manual\n                    // let the user extract the errorneous redirect URL\n                    if (request.redirect !== \"manual\") {\n                        reject(new FetchError(`uri requested responds with an invalid redirect URL: ${location}`, \"invalid-redirect\"));\n                        finalize();\n                        return;\n                    }\n                }\n                // HTTP fetch step 5.5\n                switch(request.redirect){\n                    case \"error\":\n                        reject(new FetchError(`uri requested responds with a redirect, redirect mode is set to error: ${request.url}`, \"no-redirect\"));\n                        finalize();\n                        return;\n                    case \"manual\":\n                        // node-fetch-specific step: make manual redirect a bit easier to use by setting the Location header value to the resolved URL.\n                        if (locationURL !== null) {\n                            // handle corrupted header\n                            try {\n                                headers.set(\"Location\", locationURL);\n                            } catch (err) {\n                                // istanbul ignore next: nodejs server prevent invalid response headers, we can't test this through normal request\n                                reject(err);\n                            }\n                        }\n                        break;\n                    case \"follow\":\n                        // HTTP-redirect fetch step 2\n                        if (locationURL === null) {\n                            break;\n                        }\n                        // HTTP-redirect fetch step 5\n                        if (request.counter >= request.follow) {\n                            reject(new FetchError(`maximum redirect reached at: ${request.url}`, \"max-redirect\"));\n                            finalize();\n                            return;\n                        }\n                        // HTTP-redirect fetch step 6 (counter increment)\n                        // Create a new Request object.\n                        const requestOpts = {\n                            headers: new Headers(request.headers),\n                            follow: request.follow,\n                            counter: request.counter + 1,\n                            agent: request.agent,\n                            compress: request.compress,\n                            method: request.method,\n                            body: request.body,\n                            signal: request.signal,\n                            timeout: request.timeout,\n                            size: request.size\n                        };\n                        if (!isDomainOrSubdomain(request.url, locationURL) || !isSameProtocol(request.url, locationURL)) {\n                            for (const name of [\n                                \"authorization\",\n                                \"www-authenticate\",\n                                \"cookie\",\n                                \"cookie2\"\n                            ]){\n                                requestOpts.headers.delete(name);\n                            }\n                        }\n                        // HTTP-redirect fetch step 9\n                        if (res.statusCode !== 303 && request.body && getTotalBytes(request) === null) {\n                            reject(new FetchError(\"Cannot follow redirect with body being a readable stream\", \"unsupported-redirect\"));\n                            finalize();\n                            return;\n                        }\n                        // HTTP-redirect fetch step 11\n                        if (res.statusCode === 303 || (res.statusCode === 301 || res.statusCode === 302) && request.method === \"POST\") {\n                            requestOpts.method = \"GET\";\n                            requestOpts.body = undefined;\n                            requestOpts.headers.delete(\"content-length\");\n                        }\n                        // HTTP-redirect fetch step 15\n                        resolve(fetch(new Request(locationURL, requestOpts)));\n                        finalize();\n                        return;\n                }\n            }\n            // prepare response\n            res.once(\"end\", function() {\n                if (signal) signal.removeEventListener(\"abort\", abortAndFinalize);\n            });\n            let body = res.pipe(new PassThrough$1());\n            const response_options = {\n                url: request.url,\n                status: res.statusCode,\n                statusText: res.statusMessage,\n                headers: headers,\n                size: request.size,\n                timeout: request.timeout,\n                counter: request.counter\n            };\n            // HTTP-network fetch step ********\n            const codings = headers.get(\"Content-Encoding\");\n            // HTTP-network fetch step ********: handle content codings\n            // in following scenarios we ignore compression support\n            // 1. compression support is disabled\n            // 2. HEAD request\n            // 3. no Content-Encoding header\n            // 4. no content response (204)\n            // 5. content not modified response (304)\n            if (!request.compress || request.method === \"HEAD\" || codings === null || res.statusCode === 204 || res.statusCode === 304) {\n                response = new Response(body, response_options);\n                resolve(response);\n                return;\n            }\n            // For Node v6+\n            // Be less strict when decoding compressed responses, since sometimes\n            // servers send slightly invalid responses that are still accepted\n            // by common browsers.\n            // Always using Z_SYNC_FLUSH is what cURL does.\n            const zlibOptions = {\n                flush: zlib.Z_SYNC_FLUSH,\n                finishFlush: zlib.Z_SYNC_FLUSH\n            };\n            // for gzip\n            if (codings == \"gzip\" || codings == \"x-gzip\") {\n                body = body.pipe(zlib.createGunzip(zlibOptions));\n                response = new Response(body, response_options);\n                resolve(response);\n                return;\n            }\n            // for deflate\n            if (codings == \"deflate\" || codings == \"x-deflate\") {\n                // handle the infamous raw deflate response from old servers\n                // a hack for old IIS and Apache servers\n                const raw = res.pipe(new PassThrough$1());\n                raw.once(\"data\", function(chunk) {\n                    // see http://stackoverflow.com/questions/37519828\n                    if ((chunk[0] & 0x0F) === 0x08) {\n                        body = body.pipe(zlib.createInflate());\n                    } else {\n                        body = body.pipe(zlib.createInflateRaw());\n                    }\n                    response = new Response(body, response_options);\n                    resolve(response);\n                });\n                raw.on(\"end\", function() {\n                    // some old IIS servers return zero-length OK deflate responses, so 'data' is never emitted.\n                    if (!response) {\n                        response = new Response(body, response_options);\n                        resolve(response);\n                    }\n                });\n                return;\n            }\n            // for br\n            if (codings == \"br\" && typeof zlib.createBrotliDecompress === \"function\") {\n                body = body.pipe(zlib.createBrotliDecompress());\n                response = new Response(body, response_options);\n                resolve(response);\n                return;\n            }\n            // otherwise, use response as-is\n            response = new Response(body, response_options);\n            resolve(response);\n        });\n        writeToStream(req, request);\n    });\n}\nfunction fixResponseChunkedTransferBadEnding(request, errorCallback) {\n    let socket;\n    request.on(\"socket\", function(s) {\n        socket = s;\n    });\n    request.on(\"response\", function(response) {\n        const headers = response.headers;\n        if (headers[\"transfer-encoding\"] === \"chunked\" && !headers[\"content-length\"]) {\n            response.once(\"close\", function(hadError) {\n                // tests for socket presence, as in some situations the\n                // the 'socket' event is not triggered for the request\n                // (happens in deno), avoids `TypeError`\n                // if a data listener is still present we didn't end cleanly\n                const hasDataListener = socket && socket.listenerCount(\"data\") > 0;\n                if (hasDataListener && !hadError) {\n                    const err = new Error(\"Premature close\");\n                    err.code = \"ERR_STREAM_PREMATURE_CLOSE\";\n                    errorCallback(err);\n                }\n            });\n        }\n    });\n}\nfunction destroyStream(stream, err) {\n    if (stream.destroy) {\n        stream.destroy(err);\n    } else {\n        // node < 8\n        stream.emit(\"error\", err);\n        stream.end();\n    }\n}\n/**\n * Redirect code matching\n *\n * @param   Number   code  Status code\n * @return  Boolean\n */ fetch.isRedirect = function(code) {\n    return code === 301 || code === 302 || code === 303 || code === 307 || code === 308;\n};\n// expose Promise\nfetch.Promise = global.Promise;\nmodule.exports = exports = fetch;\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = exports;\nexports.Headers = Headers;\nexports.Request = Request;\nexports.Response = Response;\nexports.FetchError = FetchError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\n");

/***/ })

};
;