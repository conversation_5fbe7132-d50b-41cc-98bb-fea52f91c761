"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-popper@1.2.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_ffa2341e59ce9c78f0d0d849ccd75e57";
exports.ids = ["vendor-chunks/@radix-ui+react-popper@1.2.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_ffa2341e59ce9c78f0d0d849ccd75e57"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_ffa2341e59ce9c78f0d0d849ccd75e57/node_modules/@radix-ui/react-popper/dist/index.mjs":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-popper@1.2.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_ffa2341e59ce9c78f0d0d849ccd75e57/node_modules/@radix-ui/react-popper/dist/index.mjs ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALIGN_OPTIONS: () => (/* binding */ ALIGN_OPTIONS),\n/* harmony export */   Anchor: () => (/* binding */ Anchor),\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Popper: () => (/* binding */ Popper),\n/* harmony export */   PopperAnchor: () => (/* binding */ PopperAnchor),\n/* harmony export */   PopperArrow: () => (/* binding */ PopperArrow),\n/* harmony export */   PopperContent: () => (/* binding */ PopperContent),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   SIDE_OPTIONS: () => (/* binding */ SIDE_OPTIONS),\n/* harmony export */   createPopperScope: () => (/* binding */ createPopperScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/.pnpm/@floating-ui+react-dom@2.1.2_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/.pnpm/@floating-ui+dom@1.7.0/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-arrow */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-arrow@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_e9e31f839ccc03b965a9c76fb12e37fb/node_modules/@radix-ui/react-arrow/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23__@types+re_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-effect@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ALIGN_OPTIONS,Anchor,Arrow,Content,Popper,PopperAnchor,PopperArrow,PopperContent,Root,SIDE_OPTIONS,createPopperScope auto */ // src/popper.tsx\n\n\n\n\n\n\n\n\n\n\nvar SIDE_OPTIONS = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nvar ALIGN_OPTIONS = [\n    \"start\",\n    \"center\",\n    \"end\"\n];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props)=>{\n    const { __scopePopper, children } = props;\n    const [anchor, setAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperProvider, {\n        scope: __scopePopper,\n        anchor,\n        onAnchorChange: setAnchor,\n        children\n    });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n    return virtualRef ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...anchorProps,\n        ref: composedRefs\n    });\n});\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, side = \"bottom\", sideOffset = 0, align = \"center\", alignOffset = 0, arrowPadding = 0, avoidCollisions = true, collisionBoundary = [], collisionPadding: collisionPaddingProp = 0, sticky = \"partial\", hideWhenDetached = false, updatePositionStrategy = \"optimized\", onPlaced, ...contentProps } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const [arrow, setArrow] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const arrowSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__.useSize)(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...collisionPaddingProp\n    };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [\n        collisionBoundary\n    ];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n        padding: collisionPadding,\n        boundary: boundary.filter(isNotNull),\n        // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n        altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.useFloating)({\n        // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n        strategy: \"fixed\",\n        placement: desiredPlacement,\n        whileElementsMounted: (...args)=>{\n            const cleanup = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__.autoUpdate)(...args, {\n                animationFrame: updatePositionStrategy === \"always\"\n            });\n            return cleanup;\n        },\n        elements: {\n            reference: context.anchor\n        },\n        middleware: [\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.offset)({\n                mainAxis: sideOffset + arrowHeight,\n                alignmentAxis: alignOffset\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.shift)({\n                mainAxis: true,\n                crossAxis: false,\n                limiter: sticky === \"partial\" ? (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.limitShift)() : void 0,\n                ...detectOverflowOptions\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.flip)({\n                ...detectOverflowOptions\n            }),\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.size)({\n                ...detectOverflowOptions,\n                apply: ({ elements, rects, availableWidth, availableHeight })=>{\n                    const { width: anchorWidth, height: anchorHeight } = rects.reference;\n                    const contentStyle = elements.floating.style;\n                    contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n                    contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n                    contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n                    contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n                }\n            }),\n            arrow && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.arrow)({\n                element: arrow,\n                padding: arrowPadding\n            }),\n            transformOrigin({\n                arrowWidth,\n                arrowHeight\n            }),\n            hideWhenDetached && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.hide)({\n                strategy: \"referenceHidden\",\n                ...detectOverflowOptions\n            })\n        ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onPlaced);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(()=>{\n        if (isPositioned) {\n            handlePlaced?.();\n        }\n    }, [\n        isPositioned,\n        handlePlaced\n    ]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(()=>{\n        if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [\n        content\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n            ...floatingStyles,\n            transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n            // keep off the page when measuring\n            minWidth: \"max-content\",\n            zIndex: contentZIndex,\n            [\"--radix-popper-transform-origin\"]: [\n                middlewareData.transformOrigin?.x,\n                middlewareData.transformOrigin?.y\n            ].join(\" \"),\n            // hide the content if using the hide middleware and should be hidden\n            // set visibility to hidden and disable pointer events so the UI behaves\n            // as if the PopperContent isn't there at all\n            ...middlewareData.hide?.referenceHidden && {\n                visibility: \"hidden\",\n                pointerEvents: \"none\"\n            }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperContentProvider, {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                    ...contentProps.style,\n                    // if the PopperContent hasn't been placed yet (not all measurements done)\n                    // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                    animation: !isPositioned ? \"none\" : void 0\n                }\n            })\n        })\n    });\n});\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n    top: \"bottom\",\n    right: \"left\",\n    bottom: \"top\",\n    left: \"right\"\n};\nvar PopperArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function PopperArrow2(props, forwardedRef) {\n    const { __scopePopper, ...arrowProps } = props;\n    const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n    const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n    return(// we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        ref: contentContext.onArrowChange,\n        style: {\n            position: \"absolute\",\n            left: contentContext.arrowX,\n            top: contentContext.arrowY,\n            [baseSide]: 0,\n            transformOrigin: {\n                top: \"\",\n                right: \"0 0\",\n                bottom: \"center 0\",\n                left: \"100% 0\"\n            }[contentContext.placedSide],\n            transform: {\n                top: \"translateY(100%)\",\n                right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n                bottom: `rotate(180deg)`,\n                left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n            }[contentContext.placedSide],\n            visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__.Root, {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n                ...arrowProps.style,\n                // ensures the element can be measured correctly (mostly for if SVG)\n                display: \"block\"\n            }\n        })\n    }));\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n    return value !== null;\n}\nvar transformOrigin = (options)=>({\n        name: \"transformOrigin\",\n        options,\n        fn (data) {\n            const { placement, rects, middlewareData } = data;\n            const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n            const isArrowHidden = cannotCenterArrow;\n            const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n            const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n            const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n            const noArrowAlign = {\n                start: \"0%\",\n                center: \"50%\",\n                end: \"100%\"\n            }[placedAlign];\n            const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n            const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n            let x = \"\";\n            let y = \"\";\n            if (placedSide === \"bottom\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${-arrowHeight}px`;\n            } else if (placedSide === \"top\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${rects.floating.height + arrowHeight}px`;\n            } else if (placedSide === \"right\") {\n                x = `${-arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            } else if (placedSide === \"left\") {\n                x = `${rects.floating.width + arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            }\n            return {\n                data: {\n                    x,\n                    y\n                }\n            };\n        }\n    });\nfunction getSideAndAlignFromPlacement(placement) {\n    const [side, align = \"center\"] = placement.split(\"-\");\n    return [\n        side,\n        align\n    ];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_ffa2341e59ce9c78f0d0d849ccd75e57/node_modules/@radix-ui/react-popper/dist/index.mjs\n");

/***/ })

};
;