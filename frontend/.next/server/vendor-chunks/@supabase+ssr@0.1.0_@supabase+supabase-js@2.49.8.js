"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+ssr@0.1.0_@supabase+supabase-js@2.49.8";
exports.ids = ["vendor-chunks/@supabase+ssr@0.1.0_@supabase+supabase-js@2.49.8"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@supabase+ssr@0.1.0_@supabase+supabase-js@2.49.8/node_modules/@supabase/ssr/dist/index.mjs":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+ssr@0.1.0_@supabase+supabase-js@2.49.8/node_modules/@supabase/ssr/dist/index.mjs ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* binding */ DEFAULT_COOKIE_OPTIONS),\n/* harmony export */   combineChunks: () => (/* binding */ combineChunks),\n/* harmony export */   createBrowserClient: () => (/* binding */ createBrowserClient),\n/* harmony export */   createChunks: () => (/* binding */ createChunks),\n/* harmony export */   createServerClient: () => (/* binding */ createServerClient),\n/* harmony export */   deleteChunks: () => (/* binding */ deleteChunks),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   parse: () => (/* reexport safe */ cookie__WEBPACK_IMPORTED_MODULE_0__.parse),\n/* harmony export */   serialize: () => (/* reexport safe */ cookie__WEBPACK_IMPORTED_MODULE_0__.serialize)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.49.8/node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_mergeDeepRight_ramda__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=mergeDeepRight!=!ramda */ \"(ssr)/./node_modules/.pnpm/ramda@0.29.1/node_modules/ramda/es/mergeDeepRight.js\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cookie */ \"(ssr)/./node_modules/.pnpm/cookie@0.5.0/node_modules/cookie/index.js\");\n// src/createBrowserClient.ts\n\n\n// src/utils/helpers.ts\n\nfunction isBrowser() {\n    return  false && 0;\n}\n// src/utils/constants.ts\nvar DEFAULT_COOKIE_OPTIONS = {\n    path: \"/\",\n    sameSite: \"lax\",\n    httpOnly: false,\n    maxAge: 60 * 60 * 24 * 365 * 1e3\n};\n// src/utils/chunker.ts\nvar MAX_CHUNK_SIZE = 3180;\nfunction createChunks(key, value, chunkSize) {\n    const resolvedChunkSize = chunkSize ?? MAX_CHUNK_SIZE;\n    let encodedValue = encodeURIComponent(value);\n    if (encodedValue.length <= resolvedChunkSize) {\n        return [\n            {\n                name: key,\n                value\n            }\n        ];\n    }\n    const chunks = [];\n    while(encodedValue.length > 0){\n        let encodedChunkHead = encodedValue.slice(0, resolvedChunkSize);\n        const lastEscapePos = encodedChunkHead.lastIndexOf(\"%\");\n        if (lastEscapePos > resolvedChunkSize - 3) {\n            encodedChunkHead = encodedChunkHead.slice(0, lastEscapePos);\n        }\n        let valueHead = \"\";\n        while(encodedChunkHead.length > 0){\n            try {\n                valueHead = decodeURIComponent(encodedChunkHead);\n                break;\n            } catch (error) {\n                if (error instanceof URIError && encodedChunkHead.at(-3) === \"%\" && encodedChunkHead.length > 3) {\n                    encodedChunkHead = encodedChunkHead.slice(0, encodedChunkHead.length - 3);\n                } else {\n                    throw error;\n                }\n            }\n        }\n        chunks.push(valueHead);\n        encodedValue = encodedValue.slice(encodedChunkHead.length);\n    }\n    return chunks.map((value2, i)=>({\n            name: `${key}.${i}`,\n            value: value2\n        }));\n}\nasync function combineChunks(key, retrieveChunk) {\n    const value = await retrieveChunk(key);\n    if (value) {\n        return value;\n    }\n    let values = [];\n    for(let i = 0;; i++){\n        const chunkName = `${key}.${i}`;\n        const chunk = await retrieveChunk(chunkName);\n        if (!chunk) {\n            break;\n        }\n        values.push(chunk);\n    }\n    if (values.length > 0) {\n        return values.join(\"\");\n    }\n}\nasync function deleteChunks(key, retrieveChunk, removeChunk) {\n    const value = await retrieveChunk(key);\n    if (value) {\n        await removeChunk(key);\n        return;\n    }\n    for(let i = 0;; i++){\n        const chunkName = `${key}.${i}`;\n        const chunk = await retrieveChunk(chunkName);\n        if (!chunk) {\n            break;\n        }\n        await removeChunk(chunkName);\n    }\n}\n// src/createBrowserClient.ts\n\nvar cachedBrowserClient;\nfunction createBrowserClient(supabaseUrl, supabaseKey, options) {\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(`Your project's URL and Key are required to create a Supabase client!\n\nCheck your Supabase project's API settings to find these values\n\nhttps://supabase.com/dashboard/project/_/settings/api`);\n    }\n    let cookies = {};\n    let isSingleton = true;\n    let cookieOptions;\n    let userDefinedClientOptions;\n    if (options) {\n        ({ cookies, isSingleton = true, cookieOptions, ...userDefinedClientOptions } = options);\n    }\n    const cookieClientOptions = {\n        global: {\n            headers: {\n                \"X-Client-Info\": `${\"supabase-ssr\"}/${\"0.1.0\"}`\n            }\n        },\n        auth: {\n            flowType: \"pkce\",\n            autoRefreshToken: isBrowser(),\n            detectSessionInUrl: isBrowser(),\n            persistSession: true,\n            storage: {\n                // this client is used on the browser so cookies can be trusted\n                isServer: false,\n                getItem: async (key)=>{\n                    const chunkedCookie = await combineChunks(key, async (chunkName)=>{\n                        if (typeof cookies.get === \"function\") {\n                            return await cookies.get(chunkName);\n                        }\n                        if (isBrowser()) {\n                            const cookie = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.parse)(document.cookie);\n                            return cookie[chunkName];\n                        }\n                    });\n                    return chunkedCookie;\n                },\n                setItem: async (key, value)=>{\n                    const chunks = await createChunks(key, value);\n                    await Promise.all(chunks.map(async (chunk)=>{\n                        if (typeof cookies.set === \"function\") {\n                            await cookies.set(chunk.name, chunk.value, {\n                                ...DEFAULT_COOKIE_OPTIONS,\n                                ...cookieOptions,\n                                maxAge: DEFAULT_COOKIE_OPTIONS.maxAge\n                            });\n                        } else {\n                            if (isBrowser()) {\n                                document.cookie = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.serialize)(chunk.name, chunk.value, {\n                                    ...DEFAULT_COOKIE_OPTIONS,\n                                    ...cookieOptions,\n                                    maxAge: DEFAULT_COOKIE_OPTIONS.maxAge\n                                });\n                            }\n                        }\n                    }));\n                },\n                removeItem: async (key)=>{\n                    if (typeof cookies.remove === \"function\" && typeof cookies.get !== \"function\") {\n                        console.log(\"Removing chunked cookie without a `get` method is not supported.\\n\\n\tWhen you call the `createBrowserClient` function from the `@supabase/ssr` package, make sure you declare both a `get` and `remove` method on the `cookies` object.\\n\\nhttps://supabase.com/docs/guides/auth/server-side/creating-a-client\");\n                        return;\n                    }\n                    await deleteChunks(key, async (chunkName)=>{\n                        if (typeof cookies.get === \"function\") {\n                            return await cookies.get(chunkName);\n                        }\n                        if (isBrowser()) {\n                            const documentCookies = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.parse)(document.cookie);\n                            return documentCookies[chunkName];\n                        }\n                    }, async (chunkName)=>{\n                        if (typeof cookies.remove === \"function\") {\n                            await cookies.remove(chunkName, {\n                                ...DEFAULT_COOKIE_OPTIONS,\n                                ...cookieOptions,\n                                maxAge: 0\n                            });\n                        } else {\n                            if (isBrowser()) {\n                                document.cookie = (0,cookie__WEBPACK_IMPORTED_MODULE_0__.serialize)(chunkName, \"\", {\n                                    ...DEFAULT_COOKIE_OPTIONS,\n                                    ...cookieOptions,\n                                    maxAge: 0\n                                });\n                            }\n                        }\n                    });\n                }\n            }\n        }\n    };\n    const clientOptions = (0,_barrel_optimize_names_mergeDeepRight_ramda__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cookieClientOptions, userDefinedClientOptions);\n    if (isSingleton) {\n        const browser = isBrowser();\n        if (browser && cachedBrowserClient) {\n            return cachedBrowserClient;\n        }\n        const client = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(supabaseUrl, supabaseKey, clientOptions);\n        if (browser) {\n            cachedBrowserClient = client;\n        }\n        return client;\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(supabaseUrl, supabaseKey, clientOptions);\n}\n// src/createServerClient.ts\n\n\nfunction createServerClient(supabaseUrl, supabaseKey, options) {\n    if (!supabaseUrl || !supabaseKey) {\n        throw new Error(`Your project's URL and Key are required to create a Supabase client!\n\nCheck your Supabase project's API settings to find these values\n\nhttps://supabase.com/dashboard/project/_/settings/api`);\n    }\n    const { cookies, cookieOptions, ...userDefinedClientOptions } = options;\n    if (cookieOptions == null ? void 0 : cookieOptions.name) {\n        userDefinedClientOptions.auth = {\n            ...userDefinedClientOptions.auth,\n            storageKey: cookieOptions.name\n        };\n    }\n    const cookieClientOptions = {\n        global: {\n            headers: {\n                \"X-Client-Info\": `${\"supabase-ssr\"}/${\"0.1.0\"}`\n            }\n        },\n        auth: {\n            flowType: \"pkce\",\n            autoRefreshToken: isBrowser(),\n            detectSessionInUrl: isBrowser(),\n            persistSession: true,\n            storage: {\n                // to signal to the libraries that these cookies are coming from a server environment and their value should not be trusted\n                isServer: true,\n                getItem: async (key)=>{\n                    const chunkedCookie = await combineChunks(key, async (chunkName)=>{\n                        if (typeof cookies.get === \"function\") {\n                            return await cookies.get(chunkName);\n                        }\n                    });\n                    return chunkedCookie;\n                },\n                setItem: async (key, value)=>{\n                    const chunks = createChunks(key, value);\n                    await Promise.all(chunks.map(async (chunk)=>{\n                        if (typeof cookies.set === \"function\") {\n                            await cookies.set(chunk.name, chunk.value, {\n                                ...DEFAULT_COOKIE_OPTIONS,\n                                ...cookieOptions,\n                                maxAge: DEFAULT_COOKIE_OPTIONS.maxAge\n                            });\n                        }\n                    }));\n                },\n                removeItem: async (key)=>{\n                    if (typeof cookies.remove === \"function\" && typeof cookies.get !== \"function\") {\n                        console.log(\"Removing chunked cookie without a `get` method is not supported.\\n\\n\tWhen you call the `createServerClient` function from the `@supabase/ssr` package, make sure you declare both a `get` and `remove` method on the `cookies` object.\\n\\nhttps://supabase.com/docs/guides/auth/server-side/creating-a-client\");\n                        return;\n                    }\n                    deleteChunks(key, async (chunkName)=>{\n                        if (typeof cookies.get === \"function\") {\n                            return await cookies.get(chunkName);\n                        }\n                    }, async (chunkName)=>{\n                        if (typeof cookies.remove === \"function\") {\n                            return await cookies.remove(chunkName, {\n                                ...DEFAULT_COOKIE_OPTIONS,\n                                ...cookieOptions,\n                                maxAge: 0\n                            });\n                        }\n                    });\n                }\n            }\n        }\n    };\n    const clientOptions = (0,_barrel_optimize_names_mergeDeepRight_ramda__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(cookieClientOptions, userDefinedClientOptions);\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(supabaseUrl, supabaseKey, clientOptions);\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3NzckAwLjEuMF9Ac3VwYWJhc2Urc3VwYWJhc2UtanNAMi40OS44L25vZGVfbW9kdWxlcy9Ac3VwYWJhc2Uvc3NyL2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUFBLDZCQUE2QjtBQUN3QjtBQUNkO0FBRXZDLHVCQUF1QjtBQUNtQjtBQUMxQyxTQUFTSTtJQUNQLE9BQU8sTUFBNkIsSUFBSSxDQUFzQztBQUNoRjtBQUVBLHlCQUF5QjtBQUN6QixJQUFJRyx5QkFBeUI7SUFDM0JDLE1BQU07SUFDTkMsVUFBVTtJQUNWQyxVQUFVO0lBQ1ZDLFFBQVEsS0FBSyxLQUFLLEtBQUssTUFBTTtBQUMvQjtBQUVBLHVCQUF1QjtBQUN2QixJQUFJQyxpQkFBaUI7QUFDckIsU0FBU0MsYUFBYUMsR0FBRyxFQUFFQyxLQUFLLEVBQUVDLFNBQVM7SUFDekMsTUFBTUMsb0JBQW9CRCxhQUFhSjtJQUN2QyxJQUFJTSxlQUFlQyxtQkFBbUJKO0lBQ3RDLElBQUlHLGFBQWFFLE1BQU0sSUFBSUgsbUJBQW1CO1FBQzVDLE9BQU87WUFBQztnQkFBRUksTUFBTVA7Z0JBQUtDO1lBQU07U0FBRTtJQUMvQjtJQUNBLE1BQU1PLFNBQVMsRUFBRTtJQUNqQixNQUFPSixhQUFhRSxNQUFNLEdBQUcsRUFBRztRQUM5QixJQUFJRyxtQkFBbUJMLGFBQWFNLEtBQUssQ0FBQyxHQUFHUDtRQUM3QyxNQUFNUSxnQkFBZ0JGLGlCQUFpQkcsV0FBVyxDQUFDO1FBQ25ELElBQUlELGdCQUFnQlIsb0JBQW9CLEdBQUc7WUFDekNNLG1CQUFtQkEsaUJBQWlCQyxLQUFLLENBQUMsR0FBR0M7UUFDL0M7UUFDQSxJQUFJRSxZQUFZO1FBQ2hCLE1BQU9KLGlCQUFpQkgsTUFBTSxHQUFHLEVBQUc7WUFDbEMsSUFBSTtnQkFDRk8sWUFBWUMsbUJBQW1CTDtnQkFDL0I7WUFDRixFQUFFLE9BQU9NLE9BQU87Z0JBQ2QsSUFBSUEsaUJBQWlCQyxZQUFZUCxpQkFBaUJRLEVBQUUsQ0FBQyxDQUFDLE9BQU8sT0FBT1IsaUJBQWlCSCxNQUFNLEdBQUcsR0FBRztvQkFDL0ZHLG1CQUFtQkEsaUJBQWlCQyxLQUFLLENBQUMsR0FBR0QsaUJBQWlCSCxNQUFNLEdBQUc7Z0JBQ3pFLE9BQU87b0JBQ0wsTUFBTVM7Z0JBQ1I7WUFDRjtRQUNGO1FBQ0FQLE9BQU9VLElBQUksQ0FBQ0w7UUFDWlQsZUFBZUEsYUFBYU0sS0FBSyxDQUFDRCxpQkFBaUJILE1BQU07SUFDM0Q7SUFDQSxPQUFPRSxPQUFPVyxHQUFHLENBQUMsQ0FBQ0MsUUFBUUMsSUFBTztZQUFFZCxNQUFNLENBQUMsRUFBRVAsSUFBSSxDQUFDLEVBQUVxQixFQUFFLENBQUM7WUFBRXBCLE9BQU9tQjtRQUFPO0FBQ3pFO0FBQ0EsZUFBZUUsY0FBY3RCLEdBQUcsRUFBRXVCLGFBQWE7SUFDN0MsTUFBTXRCLFFBQVEsTUFBTXNCLGNBQWN2QjtJQUNsQyxJQUFJQyxPQUFPO1FBQ1QsT0FBT0E7SUFDVDtJQUNBLElBQUl1QixTQUFTLEVBQUU7SUFDZixJQUFLLElBQUlILElBQUksSUFBS0EsSUFBSztRQUNyQixNQUFNSSxZQUFZLENBQUMsRUFBRXpCLElBQUksQ0FBQyxFQUFFcUIsRUFBRSxDQUFDO1FBQy9CLE1BQU1LLFFBQVEsTUFBTUgsY0FBY0U7UUFDbEMsSUFBSSxDQUFDQyxPQUFPO1lBQ1Y7UUFDRjtRQUNBRixPQUFPTixJQUFJLENBQUNRO0lBQ2Q7SUFDQSxJQUFJRixPQUFPbEIsTUFBTSxHQUFHLEdBQUc7UUFDckIsT0FBT2tCLE9BQU9HLElBQUksQ0FBQztJQUNyQjtBQUNGO0FBQ0EsZUFBZUMsYUFBYTVCLEdBQUcsRUFBRXVCLGFBQWEsRUFBRU0sV0FBVztJQUN6RCxNQUFNNUIsUUFBUSxNQUFNc0IsY0FBY3ZCO0lBQ2xDLElBQUlDLE9BQU87UUFDVCxNQUFNNEIsWUFBWTdCO1FBQ2xCO0lBQ0Y7SUFDQSxJQUFLLElBQUlxQixJQUFJLElBQUtBLElBQUs7UUFDckIsTUFBTUksWUFBWSxDQUFDLEVBQUV6QixJQUFJLENBQUMsRUFBRXFCLEVBQUUsQ0FBQztRQUMvQixNQUFNSyxRQUFRLE1BQU1ILGNBQWNFO1FBQ2xDLElBQUksQ0FBQ0MsT0FBTztZQUNWO1FBQ0Y7UUFDQSxNQUFNRyxZQUFZSjtJQUNwQjtBQUNGO0FBRUEsNkJBQTZCO0FBQ3FDO0FBQ2xFLElBQUlPO0FBQ0osU0FBU0Msb0JBQW9CQyxXQUFXLEVBQUVDLFdBQVcsRUFBRUMsT0FBTztJQUM1RCxJQUFJLENBQUNGLGVBQWUsQ0FBQ0MsYUFBYTtRQUNoQyxNQUFNLElBQUlFLE1BQ1IsQ0FBQzs7OztxREFJOEMsQ0FBQztJQUVwRDtJQUNBLElBQUlDLFVBQVUsQ0FBQztJQUNmLElBQUlDLGNBQWM7SUFDbEIsSUFBSUM7SUFDSixJQUFJQztJQUNKLElBQUlMLFNBQVM7UUFDVixHQUFFRSxPQUFPLEVBQUVDLGNBQWMsSUFBSSxFQUFFQyxhQUFhLEVBQUUsR0FBR0MsMEJBQTBCLEdBQUdMLE9BQU07SUFDdkY7SUFDQSxNQUFNTSxzQkFBc0I7UUFDMUJDLFFBQVE7WUFDTkMsU0FBUztnQkFDUCxpQkFBaUIsQ0FBQyxFQUFFLGVBQWUsQ0FBQyxFQUFFLFFBQVEsQ0FBQztZQUNqRDtRQUNGO1FBQ0FDLE1BQU07WUFDSkMsVUFBVTtZQUNWQyxrQkFBa0J6RDtZQUNsQjBELG9CQUFvQjFEO1lBQ3BCMkQsZ0JBQWdCO1lBQ2hCQyxTQUFTO2dCQUNQLCtEQUErRDtnQkFDL0RDLFVBQVU7Z0JBQ1ZDLFNBQVMsT0FBT3BEO29CQUNkLE1BQU1xRCxnQkFBZ0IsTUFBTS9CLGNBQWN0QixLQUFLLE9BQU95Qjt3QkFDcEQsSUFBSSxPQUFPYSxRQUFRZ0IsR0FBRyxLQUFLLFlBQVk7NEJBQ3JDLE9BQU8sTUFBTWhCLFFBQVFnQixHQUFHLENBQUM3Qjt3QkFDM0I7d0JBQ0EsSUFBSW5DLGFBQWE7NEJBQ2YsTUFBTWlFLFNBQVN6Qiw2Q0FBTUEsQ0FBQ3RDLFNBQVMrRCxNQUFNOzRCQUNyQyxPQUFPQSxNQUFNLENBQUM5QixVQUFVO3dCQUMxQjtvQkFDRjtvQkFDQSxPQUFPNEI7Z0JBQ1Q7Z0JBQ0FHLFNBQVMsT0FBT3hELEtBQUtDO29CQUNuQixNQUFNTyxTQUFTLE1BQU1ULGFBQWFDLEtBQUtDO29CQUN2QyxNQUFNd0QsUUFBUUMsR0FBRyxDQUNmbEQsT0FBT1csR0FBRyxDQUFDLE9BQU9PO3dCQUNoQixJQUFJLE9BQU9ZLFFBQVFxQixHQUFHLEtBQUssWUFBWTs0QkFDckMsTUFBTXJCLFFBQVFxQixHQUFHLENBQUNqQyxNQUFNbkIsSUFBSSxFQUFFbUIsTUFBTXpCLEtBQUssRUFBRTtnQ0FDekMsR0FBR1Isc0JBQXNCO2dDQUN6QixHQUFHK0MsYUFBYTtnQ0FDaEIzQyxRQUFRSix1QkFBdUJJLE1BQU07NEJBQ3ZDO3dCQUNGLE9BQU87NEJBQ0wsSUFBSVAsYUFBYTtnQ0FDZkUsU0FBUytELE1BQU0sR0FBR3hCLGlEQUFVQSxDQUFDTCxNQUFNbkIsSUFBSSxFQUFFbUIsTUFBTXpCLEtBQUssRUFBRTtvQ0FDcEQsR0FBR1Isc0JBQXNCO29DQUN6QixHQUFHK0MsYUFBYTtvQ0FDaEIzQyxRQUFRSix1QkFBdUJJLE1BQU07Z0NBQ3ZDOzRCQUNGO3dCQUNGO29CQUNGO2dCQUVKO2dCQUNBK0QsWUFBWSxPQUFPNUQ7b0JBQ2pCLElBQUksT0FBT3NDLFFBQVF1QixNQUFNLEtBQUssY0FBYyxPQUFPdkIsUUFBUWdCLEdBQUcsS0FBSyxZQUFZO3dCQUM3RVEsUUFBUUMsR0FBRyxDQUNUO3dCQUVGO29CQUNGO29CQUNBLE1BQU1uQyxhQUNKNUIsS0FDQSxPQUFPeUI7d0JBQ0wsSUFBSSxPQUFPYSxRQUFRZ0IsR0FBRyxLQUFLLFlBQVk7NEJBQ3JDLE9BQU8sTUFBTWhCLFFBQVFnQixHQUFHLENBQUM3Qjt3QkFDM0I7d0JBQ0EsSUFBSW5DLGFBQWE7NEJBQ2YsTUFBTTBFLGtCQUFrQmxDLDZDQUFNQSxDQUFDdEMsU0FBUytELE1BQU07NEJBQzlDLE9BQU9TLGVBQWUsQ0FBQ3ZDLFVBQVU7d0JBQ25DO29CQUNGLEdBQ0EsT0FBT0E7d0JBQ0wsSUFBSSxPQUFPYSxRQUFRdUIsTUFBTSxLQUFLLFlBQVk7NEJBQ3hDLE1BQU12QixRQUFRdUIsTUFBTSxDQUFDcEMsV0FBVztnQ0FDOUIsR0FBR2hDLHNCQUFzQjtnQ0FDekIsR0FBRytDLGFBQWE7Z0NBQ2hCM0MsUUFBUTs0QkFDVjt3QkFDRixPQUFPOzRCQUNMLElBQUlQLGFBQWE7Z0NBQ2ZFLFNBQVMrRCxNQUFNLEdBQUd4QixpREFBVUEsQ0FBQ04sV0FBVyxJQUFJO29DQUMxQyxHQUFHaEMsc0JBQXNCO29DQUN6QixHQUFHK0MsYUFBYTtvQ0FDaEIzQyxRQUFRO2dDQUNWOzRCQUNGO3dCQUNGO29CQUNGO2dCQUVKO1lBQ0Y7UUFDRjtJQUNGO0lBQ0EsTUFBTW9FLGdCQUFnQjlFLHVGQUFjQSxDQUNsQ3VELHFCQUNBRDtJQUVGLElBQUlGLGFBQWE7UUFDZixNQUFNMkIsVUFBVTVFO1FBQ2hCLElBQUk0RSxXQUFXbEMscUJBQXFCO1lBQ2xDLE9BQU9BO1FBQ1Q7UUFDQSxNQUFNbUMsU0FBU2pGLG1FQUFZQSxDQUN6QmdELGFBQ0FDLGFBQ0E4QjtRQUVGLElBQUlDLFNBQVM7WUFDWGxDLHNCQUFzQm1DO1FBQ3hCO1FBQ0EsT0FBT0E7SUFDVDtJQUNBLE9BQU9qRixtRUFBWUEsQ0FBQ2dELGFBQWFDLGFBQWE4QjtBQUNoRDtBQUVBLDRCQUE0QjtBQUMwQztBQUNaO0FBQzFELFNBQVNLLG1CQUFtQnBDLFdBQVcsRUFBRUMsV0FBVyxFQUFFQyxPQUFPO0lBQzNELElBQUksQ0FBQ0YsZUFBZSxDQUFDQyxhQUFhO1FBQ2hDLE1BQU0sSUFBSUUsTUFDUixDQUFDOzs7O3FEQUk4QyxDQUFDO0lBRXBEO0lBQ0EsTUFBTSxFQUFFQyxPQUFPLEVBQUVFLGFBQWEsRUFBRSxHQUFHQywwQkFBMEIsR0FBR0w7SUFDaEUsSUFBSUksaUJBQWlCLE9BQU8sS0FBSyxJQUFJQSxjQUFjakMsSUFBSSxFQUFFO1FBQ3ZEa0MseUJBQXlCSSxJQUFJLEdBQUc7WUFDOUIsR0FBR0oseUJBQXlCSSxJQUFJO1lBQ2hDMEIsWUFBWS9CLGNBQWNqQyxJQUFJO1FBQ2hDO0lBQ0Y7SUFDQSxNQUFNbUMsc0JBQXNCO1FBQzFCQyxRQUFRO1lBQ05DLFNBQVM7Z0JBQ1AsaUJBQWlCLENBQUMsRUFBRSxlQUFlLENBQUMsRUFBRSxRQUFRLENBQUM7WUFDakQ7UUFDRjtRQUNBQyxNQUFNO1lBQ0pDLFVBQVU7WUFDVkMsa0JBQWtCekQ7WUFDbEIwRCxvQkFBb0IxRDtZQUNwQjJELGdCQUFnQjtZQUNoQkMsU0FBUztnQkFDUCwySEFBMkg7Z0JBQzNIQyxVQUFVO2dCQUNWQyxTQUFTLE9BQU9wRDtvQkFDZCxNQUFNcUQsZ0JBQWdCLE1BQU0vQixjQUFjdEIsS0FBSyxPQUFPeUI7d0JBQ3BELElBQUksT0FBT2EsUUFBUWdCLEdBQUcsS0FBSyxZQUFZOzRCQUNyQyxPQUFPLE1BQU1oQixRQUFRZ0IsR0FBRyxDQUFDN0I7d0JBQzNCO29CQUNGO29CQUNBLE9BQU80QjtnQkFDVDtnQkFDQUcsU0FBUyxPQUFPeEQsS0FBS0M7b0JBQ25CLE1BQU1PLFNBQVNULGFBQWFDLEtBQUtDO29CQUNqQyxNQUFNd0QsUUFBUUMsR0FBRyxDQUNmbEQsT0FBT1csR0FBRyxDQUFDLE9BQU9PO3dCQUNoQixJQUFJLE9BQU9ZLFFBQVFxQixHQUFHLEtBQUssWUFBWTs0QkFDckMsTUFBTXJCLFFBQVFxQixHQUFHLENBQUNqQyxNQUFNbkIsSUFBSSxFQUFFbUIsTUFBTXpCLEtBQUssRUFBRTtnQ0FDekMsR0FBR1Isc0JBQXNCO2dDQUN6QixHQUFHK0MsYUFBYTtnQ0FDaEIzQyxRQUFRSix1QkFBdUJJLE1BQU07NEJBQ3ZDO3dCQUNGO29CQUNGO2dCQUVKO2dCQUNBK0QsWUFBWSxPQUFPNUQ7b0JBQ2pCLElBQUksT0FBT3NDLFFBQVF1QixNQUFNLEtBQUssY0FBYyxPQUFPdkIsUUFBUWdCLEdBQUcsS0FBSyxZQUFZO3dCQUM3RVEsUUFBUUMsR0FBRyxDQUNUO3dCQUVGO29CQUNGO29CQUNBbkMsYUFDRTVCLEtBQ0EsT0FBT3lCO3dCQUNMLElBQUksT0FBT2EsUUFBUWdCLEdBQUcsS0FBSyxZQUFZOzRCQUNyQyxPQUFPLE1BQU1oQixRQUFRZ0IsR0FBRyxDQUFDN0I7d0JBQzNCO29CQUNGLEdBQ0EsT0FBT0E7d0JBQ0wsSUFBSSxPQUFPYSxRQUFRdUIsTUFBTSxLQUFLLFlBQVk7NEJBQ3hDLE9BQU8sTUFBTXZCLFFBQVF1QixNQUFNLENBQUNwQyxXQUFXO2dDQUNyQyxHQUFHaEMsc0JBQXNCO2dDQUN6QixHQUFHK0MsYUFBYTtnQ0FDaEIzQyxRQUFROzRCQUNWO3dCQUNGO29CQUNGO2dCQUVKO1lBQ0Y7UUFDRjtJQUNGO0lBQ0EsTUFBTW9FLGdCQUFnQkksdUZBQWVBLENBQ25DM0IscUJBQ0FEO0lBRUYsT0FBTzJCLG1FQUFhQSxDQUFDbEMsYUFBYUMsYUFBYThCO0FBQ2pEO0FBV0UsQ0FDRixrQ0FBa0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWd3YXRjaGdvdi1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9Ac3VwYWJhc2Urc3NyQDAuMS4wX0BzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjQ5Ljgvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9zc3IvZGlzdC9pbmRleC5tanM/NzA5NiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvY3JlYXRlQnJvd3NlckNsaWVudC50c1xuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSBcIkBzdXBhYmFzZS9zdXBhYmFzZS1qc1wiO1xuaW1wb3J0IHsgbWVyZ2VEZWVwUmlnaHQgfSBmcm9tIFwicmFtZGFcIjtcblxuLy8gc3JjL3V0aWxzL2hlbHBlcnMudHNcbmltcG9ydCB7IHBhcnNlLCBzZXJpYWxpemUgfSBmcm9tIFwiY29va2llXCI7XG5mdW5jdGlvbiBpc0Jyb3dzZXIoKSB7XG4gIHJldHVybiB0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiICYmIHR5cGVvZiB3aW5kb3cuZG9jdW1lbnQgIT09IFwidW5kZWZpbmVkXCI7XG59XG5cbi8vIHNyYy91dGlscy9jb25zdGFudHMudHNcbnZhciBERUZBVUxUX0NPT0tJRV9PUFRJT05TID0ge1xuICBwYXRoOiBcIi9cIixcbiAgc2FtZVNpdGU6IFwibGF4XCIsXG4gIGh0dHBPbmx5OiBmYWxzZSxcbiAgbWF4QWdlOiA2MCAqIDYwICogMjQgKiAzNjUgKiAxZTNcbn07XG5cbi8vIHNyYy91dGlscy9jaHVua2VyLnRzXG52YXIgTUFYX0NIVU5LX1NJWkUgPSAzMTgwO1xuZnVuY3Rpb24gY3JlYXRlQ2h1bmtzKGtleSwgdmFsdWUsIGNodW5rU2l6ZSkge1xuICBjb25zdCByZXNvbHZlZENodW5rU2l6ZSA9IGNodW5rU2l6ZSA/PyBNQVhfQ0hVTktfU0laRTtcbiAgbGV0IGVuY29kZWRWYWx1ZSA9IGVuY29kZVVSSUNvbXBvbmVudCh2YWx1ZSk7XG4gIGlmIChlbmNvZGVkVmFsdWUubGVuZ3RoIDw9IHJlc29sdmVkQ2h1bmtTaXplKSB7XG4gICAgcmV0dXJuIFt7IG5hbWU6IGtleSwgdmFsdWUgfV07XG4gIH1cbiAgY29uc3QgY2h1bmtzID0gW107XG4gIHdoaWxlIChlbmNvZGVkVmFsdWUubGVuZ3RoID4gMCkge1xuICAgIGxldCBlbmNvZGVkQ2h1bmtIZWFkID0gZW5jb2RlZFZhbHVlLnNsaWNlKDAsIHJlc29sdmVkQ2h1bmtTaXplKTtcbiAgICBjb25zdCBsYXN0RXNjYXBlUG9zID0gZW5jb2RlZENodW5rSGVhZC5sYXN0SW5kZXhPZihcIiVcIik7XG4gICAgaWYgKGxhc3RFc2NhcGVQb3MgPiByZXNvbHZlZENodW5rU2l6ZSAtIDMpIHtcbiAgICAgIGVuY29kZWRDaHVua0hlYWQgPSBlbmNvZGVkQ2h1bmtIZWFkLnNsaWNlKDAsIGxhc3RFc2NhcGVQb3MpO1xuICAgIH1cbiAgICBsZXQgdmFsdWVIZWFkID0gXCJcIjtcbiAgICB3aGlsZSAoZW5jb2RlZENodW5rSGVhZC5sZW5ndGggPiAwKSB7XG4gICAgICB0cnkge1xuICAgICAgICB2YWx1ZUhlYWQgPSBkZWNvZGVVUklDb21wb25lbnQoZW5jb2RlZENodW5rSGVhZCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgVVJJRXJyb3IgJiYgZW5jb2RlZENodW5rSGVhZC5hdCgtMykgPT09IFwiJVwiICYmIGVuY29kZWRDaHVua0hlYWQubGVuZ3RoID4gMykge1xuICAgICAgICAgIGVuY29kZWRDaHVua0hlYWQgPSBlbmNvZGVkQ2h1bmtIZWFkLnNsaWNlKDAsIGVuY29kZWRDaHVua0hlYWQubGVuZ3RoIC0gMyk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gICAgY2h1bmtzLnB1c2godmFsdWVIZWFkKTtcbiAgICBlbmNvZGVkVmFsdWUgPSBlbmNvZGVkVmFsdWUuc2xpY2UoZW5jb2RlZENodW5rSGVhZC5sZW5ndGgpO1xuICB9XG4gIHJldHVybiBjaHVua3MubWFwKCh2YWx1ZTIsIGkpID0+ICh7IG5hbWU6IGAke2tleX0uJHtpfWAsIHZhbHVlOiB2YWx1ZTIgfSkpO1xufVxuYXN5bmMgZnVuY3Rpb24gY29tYmluZUNodW5rcyhrZXksIHJldHJpZXZlQ2h1bmspIHtcbiAgY29uc3QgdmFsdWUgPSBhd2FpdCByZXRyaWV2ZUNodW5rKGtleSk7XG4gIGlmICh2YWx1ZSkge1xuICAgIHJldHVybiB2YWx1ZTtcbiAgfVxuICBsZXQgdmFsdWVzID0gW107XG4gIGZvciAobGV0IGkgPSAwOyA7IGkrKykge1xuICAgIGNvbnN0IGNodW5rTmFtZSA9IGAke2tleX0uJHtpfWA7XG4gICAgY29uc3QgY2h1bmsgPSBhd2FpdCByZXRyaWV2ZUNodW5rKGNodW5rTmFtZSk7XG4gICAgaWYgKCFjaHVuaykge1xuICAgICAgYnJlYWs7XG4gICAgfVxuICAgIHZhbHVlcy5wdXNoKGNodW5rKTtcbiAgfVxuICBpZiAodmFsdWVzLmxlbmd0aCA+IDApIHtcbiAgICByZXR1cm4gdmFsdWVzLmpvaW4oXCJcIik7XG4gIH1cbn1cbmFzeW5jIGZ1bmN0aW9uIGRlbGV0ZUNodW5rcyhrZXksIHJldHJpZXZlQ2h1bmssIHJlbW92ZUNodW5rKSB7XG4gIGNvbnN0IHZhbHVlID0gYXdhaXQgcmV0cmlldmVDaHVuayhrZXkpO1xuICBpZiAodmFsdWUpIHtcbiAgICBhd2FpdCByZW1vdmVDaHVuayhrZXkpO1xuICAgIHJldHVybjtcbiAgfVxuICBmb3IgKGxldCBpID0gMDsgOyBpKyspIHtcbiAgICBjb25zdCBjaHVua05hbWUgPSBgJHtrZXl9LiR7aX1gO1xuICAgIGNvbnN0IGNodW5rID0gYXdhaXQgcmV0cmlldmVDaHVuayhjaHVua05hbWUpO1xuICAgIGlmICghY2h1bmspIHtcbiAgICAgIGJyZWFrO1xuICAgIH1cbiAgICBhd2FpdCByZW1vdmVDaHVuayhjaHVua05hbWUpO1xuICB9XG59XG5cbi8vIHNyYy9jcmVhdGVCcm93c2VyQ2xpZW50LnRzXG5pbXBvcnQgeyBwYXJzZSBhcyBwYXJzZTIsIHNlcmlhbGl6ZSBhcyBzZXJpYWxpemUyIH0gZnJvbSBcImNvb2tpZVwiO1xudmFyIGNhY2hlZEJyb3dzZXJDbGllbnQ7XG5mdW5jdGlvbiBjcmVhdGVCcm93c2VyQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUtleSwgb3B0aW9ucykge1xuICBpZiAoIXN1cGFiYXNlVXJsIHx8ICFzdXBhYmFzZUtleSkge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgIGBZb3VyIHByb2plY3QncyBVUkwgYW5kIEtleSBhcmUgcmVxdWlyZWQgdG8gY3JlYXRlIGEgU3VwYWJhc2UgY2xpZW50IVxuXG5DaGVjayB5b3VyIFN1cGFiYXNlIHByb2plY3QncyBBUEkgc2V0dGluZ3MgdG8gZmluZCB0aGVzZSB2YWx1ZXNcblxuaHR0cHM6Ly9zdXBhYmFzZS5jb20vZGFzaGJvYXJkL3Byb2plY3QvXy9zZXR0aW5ncy9hcGlgXG4gICAgKTtcbiAgfVxuICBsZXQgY29va2llcyA9IHt9O1xuICBsZXQgaXNTaW5nbGV0b24gPSB0cnVlO1xuICBsZXQgY29va2llT3B0aW9ucztcbiAgbGV0IHVzZXJEZWZpbmVkQ2xpZW50T3B0aW9ucztcbiAgaWYgKG9wdGlvbnMpIHtcbiAgICAoeyBjb29raWVzLCBpc1NpbmdsZXRvbiA9IHRydWUsIGNvb2tpZU9wdGlvbnMsIC4uLnVzZXJEZWZpbmVkQ2xpZW50T3B0aW9ucyB9ID0gb3B0aW9ucyk7XG4gIH1cbiAgY29uc3QgY29va2llQ2xpZW50T3B0aW9ucyA9IHtcbiAgICBnbG9iYWw6IHtcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgXCJYLUNsaWVudC1JbmZvXCI6IGAke1wic3VwYWJhc2Utc3NyXCJ9LyR7XCIwLjEuMFwifWBcbiAgICAgIH1cbiAgICB9LFxuICAgIGF1dGg6IHtcbiAgICAgIGZsb3dUeXBlOiBcInBrY2VcIixcbiAgICAgIGF1dG9SZWZyZXNoVG9rZW46IGlzQnJvd3NlcigpLFxuICAgICAgZGV0ZWN0U2Vzc2lvbkluVXJsOiBpc0Jyb3dzZXIoKSxcbiAgICAgIHBlcnNpc3RTZXNzaW9uOiB0cnVlLFxuICAgICAgc3RvcmFnZToge1xuICAgICAgICAvLyB0aGlzIGNsaWVudCBpcyB1c2VkIG9uIHRoZSBicm93c2VyIHNvIGNvb2tpZXMgY2FuIGJlIHRydXN0ZWRcbiAgICAgICAgaXNTZXJ2ZXI6IGZhbHNlLFxuICAgICAgICBnZXRJdGVtOiBhc3luYyAoa2V5KSA9PiB7XG4gICAgICAgICAgY29uc3QgY2h1bmtlZENvb2tpZSA9IGF3YWl0IGNvbWJpbmVDaHVua3Moa2V5LCBhc3luYyAoY2h1bmtOYW1lKSA9PiB7XG4gICAgICAgICAgICBpZiAodHlwZW9mIGNvb2tpZXMuZ2V0ID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgICAgICAgcmV0dXJuIGF3YWl0IGNvb2tpZXMuZ2V0KGNodW5rTmFtZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaXNCcm93c2VyKCkpIHtcbiAgICAgICAgICAgICAgY29uc3QgY29va2llID0gcGFyc2UyKGRvY3VtZW50LmNvb2tpZSk7XG4gICAgICAgICAgICAgIHJldHVybiBjb29raWVbY2h1bmtOYW1lXTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcbiAgICAgICAgICByZXR1cm4gY2h1bmtlZENvb2tpZTtcbiAgICAgICAgfSxcbiAgICAgICAgc2V0SXRlbTogYXN5bmMgKGtleSwgdmFsdWUpID0+IHtcbiAgICAgICAgICBjb25zdCBjaHVua3MgPSBhd2FpdCBjcmVhdGVDaHVua3Moa2V5LCB2YWx1ZSk7XG4gICAgICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoXG4gICAgICAgICAgICBjaHVua3MubWFwKGFzeW5jIChjaHVuaykgPT4ge1xuICAgICAgICAgICAgICBpZiAodHlwZW9mIGNvb2tpZXMuc2V0ID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgICAgICAgICBhd2FpdCBjb29raWVzLnNldChjaHVuay5uYW1lLCBjaHVuay52YWx1ZSwge1xuICAgICAgICAgICAgICAgICAgLi4uREVGQVVMVF9DT09LSUVfT1BUSU9OUyxcbiAgICAgICAgICAgICAgICAgIC4uLmNvb2tpZU9wdGlvbnMsXG4gICAgICAgICAgICAgICAgICBtYXhBZ2U6IERFRkFVTFRfQ09PS0lFX09QVElPTlMubWF4QWdlXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgaWYgKGlzQnJvd3NlcigpKSB7XG4gICAgICAgICAgICAgICAgICBkb2N1bWVudC5jb29raWUgPSBzZXJpYWxpemUyKGNodW5rLm5hbWUsIGNodW5rLnZhbHVlLCB7XG4gICAgICAgICAgICAgICAgICAgIC4uLkRFRkFVTFRfQ09PS0lFX09QVElPTlMsXG4gICAgICAgICAgICAgICAgICAgIC4uLmNvb2tpZU9wdGlvbnMsXG4gICAgICAgICAgICAgICAgICAgIG1heEFnZTogREVGQVVMVF9DT09LSUVfT1BUSU9OUy5tYXhBZ2VcbiAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSlcbiAgICAgICAgICApO1xuICAgICAgICB9LFxuICAgICAgICByZW1vdmVJdGVtOiBhc3luYyAoa2V5KSA9PiB7XG4gICAgICAgICAgaWYgKHR5cGVvZiBjb29raWVzLnJlbW92ZSA9PT0gXCJmdW5jdGlvblwiICYmIHR5cGVvZiBjb29raWVzLmdldCAhPT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcbiAgICAgICAgICAgICAgXCJSZW1vdmluZyBjaHVua2VkIGNvb2tpZSB3aXRob3V0IGEgYGdldGAgbWV0aG9kIGlzIG5vdCBzdXBwb3J0ZWQuXFxuXFxuXHRXaGVuIHlvdSBjYWxsIHRoZSBgY3JlYXRlQnJvd3NlckNsaWVudGAgZnVuY3Rpb24gZnJvbSB0aGUgYEBzdXBhYmFzZS9zc3JgIHBhY2thZ2UsIG1ha2Ugc3VyZSB5b3UgZGVjbGFyZSBib3RoIGEgYGdldGAgYW5kIGByZW1vdmVgIG1ldGhvZCBvbiB0aGUgYGNvb2tpZXNgIG9iamVjdC5cXG5cXG5odHRwczovL3N1cGFiYXNlLmNvbS9kb2NzL2d1aWRlcy9hdXRoL3NlcnZlci1zaWRlL2NyZWF0aW5nLWEtY2xpZW50XCJcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgfVxuICAgICAgICAgIGF3YWl0IGRlbGV0ZUNodW5rcyhcbiAgICAgICAgICAgIGtleSxcbiAgICAgICAgICAgIGFzeW5jIChjaHVua05hbWUpID0+IHtcbiAgICAgICAgICAgICAgaWYgKHR5cGVvZiBjb29raWVzLmdldCA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGF3YWl0IGNvb2tpZXMuZ2V0KGNodW5rTmFtZSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgaWYgKGlzQnJvd3NlcigpKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZG9jdW1lbnRDb29raWVzID0gcGFyc2UyKGRvY3VtZW50LmNvb2tpZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGRvY3VtZW50Q29va2llc1tjaHVua05hbWVdO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgYXN5bmMgKGNodW5rTmFtZSkgPT4ge1xuICAgICAgICAgICAgICBpZiAodHlwZW9mIGNvb2tpZXMucmVtb3ZlID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgICAgICAgICBhd2FpdCBjb29raWVzLnJlbW92ZShjaHVua05hbWUsIHtcbiAgICAgICAgICAgICAgICAgIC4uLkRFRkFVTFRfQ09PS0lFX09QVElPTlMsXG4gICAgICAgICAgICAgICAgICAuLi5jb29raWVPcHRpb25zLFxuICAgICAgICAgICAgICAgICAgbWF4QWdlOiAwXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgaWYgKGlzQnJvd3NlcigpKSB7XG4gICAgICAgICAgICAgICAgICBkb2N1bWVudC5jb29raWUgPSBzZXJpYWxpemUyKGNodW5rTmFtZSwgXCJcIiwge1xuICAgICAgICAgICAgICAgICAgICAuLi5ERUZBVUxUX0NPT0tJRV9PUFRJT05TLFxuICAgICAgICAgICAgICAgICAgICAuLi5jb29raWVPcHRpb25zLFxuICAgICAgICAgICAgICAgICAgICBtYXhBZ2U6IDBcbiAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH07XG4gIGNvbnN0IGNsaWVudE9wdGlvbnMgPSBtZXJnZURlZXBSaWdodChcbiAgICBjb29raWVDbGllbnRPcHRpb25zLFxuICAgIHVzZXJEZWZpbmVkQ2xpZW50T3B0aW9uc1xuICApO1xuICBpZiAoaXNTaW5nbGV0b24pIHtcbiAgICBjb25zdCBicm93c2VyID0gaXNCcm93c2VyKCk7XG4gICAgaWYgKGJyb3dzZXIgJiYgY2FjaGVkQnJvd3NlckNsaWVudCkge1xuICAgICAgcmV0dXJuIGNhY2hlZEJyb3dzZXJDbGllbnQ7XG4gICAgfVxuICAgIGNvbnN0IGNsaWVudCA9IGNyZWF0ZUNsaWVudChcbiAgICAgIHN1cGFiYXNlVXJsLFxuICAgICAgc3VwYWJhc2VLZXksXG4gICAgICBjbGllbnRPcHRpb25zXG4gICAgKTtcbiAgICBpZiAoYnJvd3Nlcikge1xuICAgICAgY2FjaGVkQnJvd3NlckNsaWVudCA9IGNsaWVudDtcbiAgICB9XG4gICAgcmV0dXJuIGNsaWVudDtcbiAgfVxuICByZXR1cm4gY3JlYXRlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUtleSwgY2xpZW50T3B0aW9ucyk7XG59XG5cbi8vIHNyYy9jcmVhdGVTZXJ2ZXJDbGllbnQudHNcbmltcG9ydCB7IGNyZWF0ZUNsaWVudCBhcyBjcmVhdGVDbGllbnQyIH0gZnJvbSBcIkBzdXBhYmFzZS9zdXBhYmFzZS1qc1wiO1xuaW1wb3J0IHsgbWVyZ2VEZWVwUmlnaHQgYXMgbWVyZ2VEZWVwUmlnaHQyIH0gZnJvbSBcInJhbWRhXCI7XG5mdW5jdGlvbiBjcmVhdGVTZXJ2ZXJDbGllbnQoc3VwYWJhc2VVcmwsIHN1cGFiYXNlS2V5LCBvcHRpb25zKSB7XG4gIGlmICghc3VwYWJhc2VVcmwgfHwgIXN1cGFiYXNlS2V5KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgYFlvdXIgcHJvamVjdCdzIFVSTCBhbmQgS2V5IGFyZSByZXF1aXJlZCB0byBjcmVhdGUgYSBTdXBhYmFzZSBjbGllbnQhXG5cbkNoZWNrIHlvdXIgU3VwYWJhc2UgcHJvamVjdCdzIEFQSSBzZXR0aW5ncyB0byBmaW5kIHRoZXNlIHZhbHVlc1xuXG5odHRwczovL3N1cGFiYXNlLmNvbS9kYXNoYm9hcmQvcHJvamVjdC9fL3NldHRpbmdzL2FwaWBcbiAgICApO1xuICB9XG4gIGNvbnN0IHsgY29va2llcywgY29va2llT3B0aW9ucywgLi4udXNlckRlZmluZWRDbGllbnRPcHRpb25zIH0gPSBvcHRpb25zO1xuICBpZiAoY29va2llT3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogY29va2llT3B0aW9ucy5uYW1lKSB7XG4gICAgdXNlckRlZmluZWRDbGllbnRPcHRpb25zLmF1dGggPSB7XG4gICAgICAuLi51c2VyRGVmaW5lZENsaWVudE9wdGlvbnMuYXV0aCxcbiAgICAgIHN0b3JhZ2VLZXk6IGNvb2tpZU9wdGlvbnMubmFtZVxuICAgIH07XG4gIH1cbiAgY29uc3QgY29va2llQ2xpZW50T3B0aW9ucyA9IHtcbiAgICBnbG9iYWw6IHtcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgXCJYLUNsaWVudC1JbmZvXCI6IGAke1wic3VwYWJhc2Utc3NyXCJ9LyR7XCIwLjEuMFwifWBcbiAgICAgIH1cbiAgICB9LFxuICAgIGF1dGg6IHtcbiAgICAgIGZsb3dUeXBlOiBcInBrY2VcIixcbiAgICAgIGF1dG9SZWZyZXNoVG9rZW46IGlzQnJvd3NlcigpLFxuICAgICAgZGV0ZWN0U2Vzc2lvbkluVXJsOiBpc0Jyb3dzZXIoKSxcbiAgICAgIHBlcnNpc3RTZXNzaW9uOiB0cnVlLFxuICAgICAgc3RvcmFnZToge1xuICAgICAgICAvLyB0byBzaWduYWwgdG8gdGhlIGxpYnJhcmllcyB0aGF0IHRoZXNlIGNvb2tpZXMgYXJlIGNvbWluZyBmcm9tIGEgc2VydmVyIGVudmlyb25tZW50IGFuZCB0aGVpciB2YWx1ZSBzaG91bGQgbm90IGJlIHRydXN0ZWRcbiAgICAgICAgaXNTZXJ2ZXI6IHRydWUsXG4gICAgICAgIGdldEl0ZW06IGFzeW5jIChrZXkpID0+IHtcbiAgICAgICAgICBjb25zdCBjaHVua2VkQ29va2llID0gYXdhaXQgY29tYmluZUNodW5rcyhrZXksIGFzeW5jIChjaHVua05hbWUpID0+IHtcbiAgICAgICAgICAgIGlmICh0eXBlb2YgY29va2llcy5nZXQgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICAgICAgICByZXR1cm4gYXdhaXQgY29va2llcy5nZXQoY2h1bmtOYW1lKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcbiAgICAgICAgICByZXR1cm4gY2h1bmtlZENvb2tpZTtcbiAgICAgICAgfSxcbiAgICAgICAgc2V0SXRlbTogYXN5bmMgKGtleSwgdmFsdWUpID0+IHtcbiAgICAgICAgICBjb25zdCBjaHVua3MgPSBjcmVhdGVDaHVua3Moa2V5LCB2YWx1ZSk7XG4gICAgICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoXG4gICAgICAgICAgICBjaHVua3MubWFwKGFzeW5jIChjaHVuaykgPT4ge1xuICAgICAgICAgICAgICBpZiAodHlwZW9mIGNvb2tpZXMuc2V0ID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgICAgICAgICBhd2FpdCBjb29raWVzLnNldChjaHVuay5uYW1lLCBjaHVuay52YWx1ZSwge1xuICAgICAgICAgICAgICAgICAgLi4uREVGQVVMVF9DT09LSUVfT1BUSU9OUyxcbiAgICAgICAgICAgICAgICAgIC4uLmNvb2tpZU9wdGlvbnMsXG4gICAgICAgICAgICAgICAgICBtYXhBZ2U6IERFRkFVTFRfQ09PS0lFX09QVElPTlMubWF4QWdlXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgKTtcbiAgICAgICAgfSxcbiAgICAgICAgcmVtb3ZlSXRlbTogYXN5bmMgKGtleSkgPT4ge1xuICAgICAgICAgIGlmICh0eXBlb2YgY29va2llcy5yZW1vdmUgPT09IFwiZnVuY3Rpb25cIiAmJiB0eXBlb2YgY29va2llcy5nZXQgIT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICAgICAgY29uc29sZS5sb2coXG4gICAgICAgICAgICAgIFwiUmVtb3ZpbmcgY2h1bmtlZCBjb29raWUgd2l0aG91dCBhIGBnZXRgIG1ldGhvZCBpcyBub3Qgc3VwcG9ydGVkLlxcblxcblx0V2hlbiB5b3UgY2FsbCB0aGUgYGNyZWF0ZVNlcnZlckNsaWVudGAgZnVuY3Rpb24gZnJvbSB0aGUgYEBzdXBhYmFzZS9zc3JgIHBhY2thZ2UsIG1ha2Ugc3VyZSB5b3UgZGVjbGFyZSBib3RoIGEgYGdldGAgYW5kIGByZW1vdmVgIG1ldGhvZCBvbiB0aGUgYGNvb2tpZXNgIG9iamVjdC5cXG5cXG5odHRwczovL3N1cGFiYXNlLmNvbS9kb2NzL2d1aWRlcy9hdXRoL3NlcnZlci1zaWRlL2NyZWF0aW5nLWEtY2xpZW50XCJcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgfVxuICAgICAgICAgIGRlbGV0ZUNodW5rcyhcbiAgICAgICAgICAgIGtleSxcbiAgICAgICAgICAgIGFzeW5jIChjaHVua05hbWUpID0+IHtcbiAgICAgICAgICAgICAgaWYgKHR5cGVvZiBjb29raWVzLmdldCA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGF3YWl0IGNvb2tpZXMuZ2V0KGNodW5rTmFtZSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBhc3luYyAoY2h1bmtOYW1lKSA9PiB7XG4gICAgICAgICAgICAgIGlmICh0eXBlb2YgY29va2llcy5yZW1vdmUgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICAgICAgICAgIHJldHVybiBhd2FpdCBjb29raWVzLnJlbW92ZShjaHVua05hbWUsIHtcbiAgICAgICAgICAgICAgICAgIC4uLkRFRkFVTFRfQ09PS0lFX09QVElPTlMsXG4gICAgICAgICAgICAgICAgICAuLi5jb29raWVPcHRpb25zLFxuICAgICAgICAgICAgICAgICAgbWF4QWdlOiAwXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICApO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9O1xuICBjb25zdCBjbGllbnRPcHRpb25zID0gbWVyZ2VEZWVwUmlnaHQyKFxuICAgIGNvb2tpZUNsaWVudE9wdGlvbnMsXG4gICAgdXNlckRlZmluZWRDbGllbnRPcHRpb25zXG4gICk7XG4gIHJldHVybiBjcmVhdGVDbGllbnQyKHN1cGFiYXNlVXJsLCBzdXBhYmFzZUtleSwgY2xpZW50T3B0aW9ucyk7XG59XG5leHBvcnQge1xuICBERUZBVUxUX0NPT0tJRV9PUFRJT05TLFxuICBjb21iaW5lQ2h1bmtzLFxuICBjcmVhdGVCcm93c2VyQ2xpZW50LFxuICBjcmVhdGVDaHVua3MsXG4gIGNyZWF0ZVNlcnZlckNsaWVudCxcbiAgZGVsZXRlQ2h1bmtzLFxuICBpc0Jyb3dzZXIsXG4gIHBhcnNlLFxuICBzZXJpYWxpemVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsIm1lcmdlRGVlcFJpZ2h0IiwicGFyc2UiLCJzZXJpYWxpemUiLCJpc0Jyb3dzZXIiLCJ3aW5kb3ciLCJkb2N1bWVudCIsIkRFRkFVTFRfQ09PS0lFX09QVElPTlMiLCJwYXRoIiwic2FtZVNpdGUiLCJodHRwT25seSIsIm1heEFnZSIsIk1BWF9DSFVOS19TSVpFIiwiY3JlYXRlQ2h1bmtzIiwia2V5IiwidmFsdWUiLCJjaHVua1NpemUiLCJyZXNvbHZlZENodW5rU2l6ZSIsImVuY29kZWRWYWx1ZSIsImVuY29kZVVSSUNvbXBvbmVudCIsImxlbmd0aCIsIm5hbWUiLCJjaHVua3MiLCJlbmNvZGVkQ2h1bmtIZWFkIiwic2xpY2UiLCJsYXN0RXNjYXBlUG9zIiwibGFzdEluZGV4T2YiLCJ2YWx1ZUhlYWQiLCJkZWNvZGVVUklDb21wb25lbnQiLCJlcnJvciIsIlVSSUVycm9yIiwiYXQiLCJwdXNoIiwibWFwIiwidmFsdWUyIiwiaSIsImNvbWJpbmVDaHVua3MiLCJyZXRyaWV2ZUNodW5rIiwidmFsdWVzIiwiY2h1bmtOYW1lIiwiY2h1bmsiLCJqb2luIiwiZGVsZXRlQ2h1bmtzIiwicmVtb3ZlQ2h1bmsiLCJwYXJzZTIiLCJzZXJpYWxpemUyIiwiY2FjaGVkQnJvd3NlckNsaWVudCIsImNyZWF0ZUJyb3dzZXJDbGllbnQiLCJzdXBhYmFzZVVybCIsInN1cGFiYXNlS2V5Iiwib3B0aW9ucyIsIkVycm9yIiwiY29va2llcyIsImlzU2luZ2xldG9uIiwiY29va2llT3B0aW9ucyIsInVzZXJEZWZpbmVkQ2xpZW50T3B0aW9ucyIsImNvb2tpZUNsaWVudE9wdGlvbnMiLCJnbG9iYWwiLCJoZWFkZXJzIiwiYXV0aCIsImZsb3dUeXBlIiwiYXV0b1JlZnJlc2hUb2tlbiIsImRldGVjdFNlc3Npb25JblVybCIsInBlcnNpc3RTZXNzaW9uIiwic3RvcmFnZSIsImlzU2VydmVyIiwiZ2V0SXRlbSIsImNodW5rZWRDb29raWUiLCJnZXQiLCJjb29raWUiLCJzZXRJdGVtIiwiUHJvbWlzZSIsImFsbCIsInNldCIsInJlbW92ZUl0ZW0iLCJyZW1vdmUiLCJjb25zb2xlIiwibG9nIiwiZG9jdW1lbnRDb29raWVzIiwiY2xpZW50T3B0aW9ucyIsImJyb3dzZXIiLCJjbGllbnQiLCJjcmVhdGVDbGllbnQyIiwibWVyZ2VEZWVwUmlnaHQyIiwiY3JlYXRlU2VydmVyQ2xpZW50Iiwic3RvcmFnZUtleSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+ssr@0.1.0_@supabase+supabase-js@2.49.8/node_modules/@supabase/ssr/dist/index.mjs\n");

/***/ })

};
;