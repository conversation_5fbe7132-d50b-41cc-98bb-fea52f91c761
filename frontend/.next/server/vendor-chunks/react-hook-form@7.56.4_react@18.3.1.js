"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-hook-form@7.56.4_react@18.3.1";
exports.ids = ["vendor-chunks/react-hook-form@7.56.4_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/react-hook-form@7.56.4_react@18.3.1/node_modules/react-hook-form/dist/index.esm.mjs":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-hook-form@7.56.4_react@18.3.1/node_modules/react-hook-form/dist/index.esm.mjs ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Controller: () => (/* binding */ Controller),\n/* harmony export */   Form: () => (/* binding */ Form),\n/* harmony export */   FormProvider: () => (/* binding */ FormProvider),\n/* harmony export */   appendErrors: () => (/* binding */ appendErrors),\n/* harmony export */   createFormControl: () => (/* binding */ createFormControl),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   useController: () => (/* binding */ useController),\n/* harmony export */   useFieldArray: () => (/* binding */ useFieldArray),\n/* harmony export */   useForm: () => (/* binding */ useForm),\n/* harmony export */   useFormContext: () => (/* binding */ useFormContext),\n/* harmony export */   useFormState: () => (/* binding */ useFormState),\n/* harmony export */   useWatch: () => (/* binding */ useWatch)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n\n\nvar isCheckBoxInput = (element)=>element.type === \"checkbox\";\nvar isDateObject = (value1)=>value1 instanceof Date;\nvar isNullOrUndefined = (value1)=>value1 == null;\nconst isObjectType = (value1)=>typeof value1 === \"object\";\nvar isObject = (value1)=>!isNullOrUndefined(value1) && !Array.isArray(value1) && isObjectType(value1) && !isDateObject(value1);\nvar getEventValue = (event)=>isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = (name)=>name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name)=>names.has(getNodeParentName(name));\nvar isPlainObject = (tempObject)=>{\n    const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n    return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty(\"isPrototypeOf\");\n};\nvar isWeb =  false && 0;\nfunction cloneObject(data) {\n    let copy;\n    const isArray = Array.isArray(data);\n    const isFileListInstance = typeof FileList !== \"undefined\" ? data instanceof FileList : false;\n    if (data instanceof Date) {\n        copy = new Date(data);\n    } else if (data instanceof Set) {\n        copy = new Set(data);\n    } else if (!(isWeb && (data instanceof Blob || isFileListInstance)) && (isArray || isObject(data))) {\n        copy = isArray ? [] : {};\n        if (!isArray && !isPlainObject(data)) {\n            copy = data;\n        } else {\n            for(const key in data){\n                if (data.hasOwnProperty(key)) {\n                    copy[key] = cloneObject(data[key]);\n                }\n            }\n        }\n    } else {\n        return data;\n    }\n    return copy;\n}\nvar compact = (value1)=>Array.isArray(value1) ? value1.filter(Boolean) : [];\nvar isUndefined = (val)=>val === undefined;\nvar get = (object, path, defaultValue)=>{\n    if (!path || !isObject(object)) {\n        return defaultValue;\n    }\n    const result = compact(path.split(/[,[\\].]+?/)).reduce((result, key)=>isNullOrUndefined(result) ? result : result[key], object);\n    return isUndefined(result) || result === object ? isUndefined(object[path]) ? defaultValue : object[path] : result;\n};\nvar isBoolean = (value1)=>typeof value1 === \"boolean\";\nvar isKey = (value1)=>/^\\w*$/.test(value1);\nvar stringToPath = (input)=>compact(input.replace(/[\"|']|\\]/g, \"\").split(/\\.|\\[/));\nvar set = (object, path, value1)=>{\n    let index = -1;\n    const tempPath = isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const length = tempPath.length;\n    const lastIndex = length - 1;\n    while(++index < length){\n        const key = tempPath[index];\n        let newValue = value1;\n        if (index !== lastIndex) {\n            const objValue = object[key];\n            newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n        }\n        if (key === \"__proto__\" || key === \"constructor\" || key === \"prototype\") {\n            return;\n        }\n        object[key] = newValue;\n        object = object[key];\n    }\n};\nconst EVENTS = {\n    BLUR: \"blur\",\n    FOCUS_OUT: \"focusout\",\n    CHANGE: \"change\"\n};\nconst VALIDATION_MODE = {\n    onBlur: \"onBlur\",\n    onChange: \"onChange\",\n    onSubmit: \"onSubmit\",\n    onTouched: \"onTouched\",\n    all: \"all\"\n};\nconst INPUT_VALIDATION_RULES = {\n    max: \"max\",\n    min: \"min\",\n    maxLength: \"maxLength\",\n    minLength: \"minLength\",\n    pattern: \"pattern\",\n    required: \"required\",\n    validate: \"validate\"\n};\nconst HookFormContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(null);\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const useFormContext = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */ const FormProvider = (props)=>{\n    const { children, ...data } = props;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(HookFormContext.Provider, {\n        value: data\n    }, children);\n};\nvar getProxyFormState = (formState, control, localProxyFormState, isRoot = true)=>{\n    const result = {\n        defaultValues: control._defaultValues\n    };\n    for(const key in formState){\n        Object.defineProperty(result, key, {\n            get: ()=>{\n                const _key = key;\n                if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n                    control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n                }\n                localProxyFormState && (localProxyFormState[_key] = true);\n                return formState[_key];\n            }\n        });\n    }\n    return result;\n};\nconst useIsomorphicLayoutEffect =  false ? 0 : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFormState(props) {\n    const methods = useFormContext();\n    const { control = methods.control, disabled, name, exact } = props || {};\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._formState);\n    const _localProxyFormState = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        isDirty: false,\n        isLoading: false,\n        dirtyFields: false,\n        touchedFields: false,\n        validatingFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    });\n    useIsomorphicLayoutEffect(()=>control._subscribe({\n            name: name,\n            formState: _localProxyFormState.current,\n            exact,\n            callback: (formState)=>{\n                !disabled && updateFormState({\n                    ...control._formState,\n                    ...formState\n                });\n            }\n        }), [\n        name,\n        disabled,\n        exact\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        _localProxyFormState.current.isValid && control._setValid(true);\n    }, [\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>getProxyFormState(formState, control, _localProxyFormState.current, false), [\n        formState,\n        control\n    ]);\n}\nvar isString = (value1)=>typeof value1 === \"string\";\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue)=>{\n    if (isString(names)) {\n        isGlobal && _names.watch.add(names);\n        return get(formValues, names, defaultValue);\n    }\n    if (Array.isArray(names)) {\n        return names.map((fieldName)=>(isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n    }\n    isGlobal && (_names.watchAll = true);\n    return formValues;\n};\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */ function useWatch(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, defaultValue, disabled, exact } = props || {};\n    const _defaultValue = react__WEBPACK_IMPORTED_MODULE_0__.useRef(defaultValue);\n    const [value1, updateValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getWatch(name, _defaultValue.current));\n    useIsomorphicLayoutEffect(()=>control._subscribe({\n            name: name,\n            formState: {\n                values: true\n            },\n            exact,\n            callback: (formState)=>!disabled && updateValue(generateWatchOutput(name, control._names, formState.values || control._formValues, false, _defaultValue.current))\n        }), [\n        name,\n        control,\n        disabled,\n        exact\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._removeUnmounted());\n    return value1;\n}\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */ function useController(props) {\n    const methods = useFormContext();\n    const { name, disabled, control = methods.control, shouldUnregister } = props;\n    const isArrayField = isNameInFieldArray(control._names.array, name);\n    const value1 = useWatch({\n        control,\n        name,\n        defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n        exact: true\n    });\n    const formState = useFormState({\n        control,\n        name,\n        exact: true\n    });\n    const _props = react__WEBPACK_IMPORTED_MODULE_0__.useRef(props);\n    const _registerProps = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control.register(name, {\n        ...props.rules,\n        value: value1,\n        ...isBoolean(props.disabled) ? {\n            disabled: props.disabled\n        } : {}\n    }));\n    const fieldState = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>Object.defineProperties({}, {\n            invalid: {\n                enumerable: true,\n                get: ()=>!!get(formState.errors, name)\n            },\n            isDirty: {\n                enumerable: true,\n                get: ()=>!!get(formState.dirtyFields, name)\n            },\n            isTouched: {\n                enumerable: true,\n                get: ()=>!!get(formState.touchedFields, name)\n            },\n            isValidating: {\n                enumerable: true,\n                get: ()=>!!get(formState.validatingFields, name)\n            },\n            error: {\n                enumerable: true,\n                get: ()=>get(formState.errors, name)\n            }\n        }), [\n        formState,\n        name\n    ]);\n    const onChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>_registerProps.current.onChange({\n            target: {\n                value: getEventValue(event),\n                name: name\n            },\n            type: EVENTS.CHANGE\n        }), [\n        name\n    ]);\n    const onBlur = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>_registerProps.current.onBlur({\n            target: {\n                value: get(control._formValues, name),\n                name: name\n            },\n            type: EVENTS.BLUR\n        }), [\n        name,\n        control._formValues\n    ]);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((elm)=>{\n        const field = get(control._fields, name);\n        if (field && elm) {\n            field._f.ref = {\n                focus: ()=>elm.focus(),\n                select: ()=>elm.select(),\n                setCustomValidity: (message)=>elm.setCustomValidity(message),\n                reportValidity: ()=>elm.reportValidity()\n            };\n        }\n    }, [\n        control._fields,\n        name\n    ]);\n    const field = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            name,\n            value: value1,\n            ...isBoolean(disabled) || formState.disabled ? {\n                disabled: formState.disabled || disabled\n            } : {},\n            onChange,\n            onBlur,\n            ref\n        }), [\n        name,\n        disabled,\n        formState.disabled,\n        onChange,\n        onBlur,\n        ref,\n        value1\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n        control.register(name, {\n            ..._props.current.rules,\n            ...isBoolean(_props.current.disabled) ? {\n                disabled: _props.current.disabled\n            } : {}\n        });\n        const updateMounted = (name, value1)=>{\n            const field = get(control._fields, name);\n            if (field && field._f) {\n                field._f.mount = value1;\n            }\n        };\n        updateMounted(name, true);\n        if (_shouldUnregisterField) {\n            const value1 = cloneObject(get(control._options.defaultValues, name));\n            set(control._defaultValues, name, value1);\n            if (isUndefined(get(control._formValues, name))) {\n                set(control._formValues, name, value1);\n            }\n        }\n        !isArrayField && control.register(name);\n        return ()=>{\n            (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        isArrayField,\n        shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._setDisabledField({\n            disabled,\n            name\n        });\n    }, [\n        disabled,\n        name,\n        control\n    ]);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            field,\n            formState,\n            fieldState\n        }), [\n        field,\n        formState,\n        fieldState\n    ]);\n}\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */ const Controller = (props)=>props.render(useController(props));\nconst flatten = (obj)=>{\n    const output = {};\n    for (const key of Object.keys(obj)){\n        if (isObjectType(obj[key]) && obj[key] !== null) {\n            const nested = flatten(obj[key]);\n            for (const nestedKey of Object.keys(nested)){\n                output[`${key}.${nestedKey}`] = nested[nestedKey];\n            }\n        } else {\n            output[key] = obj[key];\n        }\n    }\n    return output;\n};\nconst POST_REQUEST = \"post\";\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */ function Form(props) {\n    const methods = useFormContext();\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const { control = methods.control, onSubmit, children, action, method = POST_REQUEST, headers, encType, onError, render, onSuccess, validateStatus, ...rest } = props;\n    const submit = async (event)=>{\n        let hasError = false;\n        let type = \"\";\n        await control.handleSubmit(async (data)=>{\n            const formData = new FormData();\n            let formDataJson = \"\";\n            try {\n                formDataJson = JSON.stringify(data);\n            } catch (_a) {}\n            const flattenFormValues = flatten(control._formValues);\n            for(const key in flattenFormValues){\n                formData.append(key, flattenFormValues[key]);\n            }\n            if (onSubmit) {\n                await onSubmit({\n                    data,\n                    event,\n                    method,\n                    formData,\n                    formDataJson\n                });\n            }\n            if (action) {\n                try {\n                    const shouldStringifySubmissionData = [\n                        headers && headers[\"Content-Type\"],\n                        encType\n                    ].some((value1)=>value1 && value1.includes(\"json\"));\n                    const response = await fetch(String(action), {\n                        method,\n                        headers: {\n                            ...headers,\n                            ...encType ? {\n                                \"Content-Type\": encType\n                            } : {}\n                        },\n                        body: shouldStringifySubmissionData ? formDataJson : formData\n                    });\n                    if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n                        hasError = true;\n                        onError && onError({\n                            response\n                        });\n                        type = String(response.status);\n                    } else {\n                        onSuccess && onSuccess({\n                            response\n                        });\n                    }\n                } catch (error) {\n                    hasError = true;\n                    onError && onError({\n                        error\n                    });\n                }\n            }\n        })(event);\n        if (hasError && props.control) {\n            props.control._subjects.state.next({\n                isSubmitSuccessful: false\n            });\n            props.control.setError(\"root.server\", {\n                type\n            });\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        setMounted(true);\n    }, []);\n    return render ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, render({\n        submit\n    })) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"form\", {\n        noValidate: mounted,\n        action: action,\n        method: method,\n        encType: encType,\n        onSubmit: submit,\n        ...rest\n    }, children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message)=>validateAllFieldCriteria ? {\n        ...errors[name],\n        types: {\n            ...errors[name] && errors[name].types ? errors[name].types : {},\n            [type]: message || true\n        }\n    } : {};\nvar convertToArrayPayload = (value1)=>Array.isArray(value1) ? value1 : [\n        value1\n    ];\nvar createSubject = ()=>{\n    let _observers = [];\n    const next = (value1)=>{\n        for (const observer of _observers){\n            observer.next && observer.next(value1);\n        }\n    };\n    const subscribe = (observer)=>{\n        _observers.push(observer);\n        return {\n            unsubscribe: ()=>{\n                _observers = _observers.filter((o)=>o !== observer);\n            }\n        };\n    };\n    const unsubscribe = ()=>{\n        _observers = [];\n    };\n    return {\n        get observers () {\n            return _observers;\n        },\n        next,\n        subscribe,\n        unsubscribe\n    };\n};\nvar isPrimitive = (value1)=>isNullOrUndefined(value1) || !isObjectType(value1);\nfunction deepEqual(object1, object2) {\n    if (isPrimitive(object1) || isPrimitive(object2)) {\n        return object1 === object2;\n    }\n    if (isDateObject(object1) && isDateObject(object2)) {\n        return object1.getTime() === object2.getTime();\n    }\n    const keys1 = Object.keys(object1);\n    const keys2 = Object.keys(object2);\n    if (keys1.length !== keys2.length) {\n        return false;\n    }\n    for (const key of keys1){\n        const val1 = object1[key];\n        if (!keys2.includes(key)) {\n            return false;\n        }\n        if (key !== \"ref\") {\n            const val2 = object2[key];\n            if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2) : val1 !== val2) {\n                return false;\n            }\n        }\n    }\n    return true;\n}\nvar isEmptyObject = (value1)=>isObject(value1) && !Object.keys(value1).length;\nvar isFileInput = (element)=>element.type === \"file\";\nvar isFunction = (value1)=>typeof value1 === \"function\";\nvar isHTMLElement = (value1)=>{\n    if (!isWeb) {\n        return false;\n    }\n    const owner = value1 ? value1.ownerDocument : 0;\n    return value1 instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMultipleSelect = (element)=>element.type === `select-multiple`;\nvar isRadioInput = (element)=>element.type === \"radio\";\nvar isRadioOrCheckbox = (ref)=>isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = (ref)=>isHTMLElement(ref) && ref.isConnected;\nfunction baseGet(object, updatePath) {\n    const length = updatePath.slice(0, -1).length;\n    let index = 0;\n    while(index < length){\n        object = isUndefined(object) ? index++ : object[updatePath[index++]];\n    }\n    return object;\n}\nfunction isEmptyArray(obj) {\n    for(const key in obj){\n        if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n            return false;\n        }\n    }\n    return true;\n}\nfunction unset(object, path) {\n    const paths = Array.isArray(path) ? path : isKey(path) ? [\n        path\n    ] : stringToPath(path);\n    const childObject = paths.length === 1 ? object : baseGet(object, paths);\n    const index = paths.length - 1;\n    const key = paths[index];\n    if (childObject) {\n        delete childObject[key];\n    }\n    if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n        unset(object, paths.slice(0, -1));\n    }\n    return object;\n}\nvar objectHasFunction = (data)=>{\n    for(const key in data){\n        if (isFunction(data[key])) {\n            return true;\n        }\n    }\n    return false;\n};\nfunction markFieldsDirty(data, fields = {}) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                fields[key] = Array.isArray(data[key]) ? [] : {};\n                markFieldsDirty(data[key], fields[key]);\n            } else if (!isNullOrUndefined(data[key])) {\n                fields[key] = true;\n            }\n        }\n    }\n    return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n    const isParentNodeArray = Array.isArray(data);\n    if (isObject(data) || isParentNodeArray) {\n        for(const key in data){\n            if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n                if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n                    dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : {\n                        ...markFieldsDirty(data[key])\n                    };\n                } else {\n                    getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n                }\n            } else {\n                dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n            }\n        }\n    }\n    return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues)=>getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nconst defaultResult = {\n    value: false,\n    isValid: false\n};\nconst validResult = {\n    value: true,\n    isValid: true\n};\nvar getCheckboxValue = (options)=>{\n    if (Array.isArray(options)) {\n        if (options.length > 1) {\n            const values = options.filter((option)=>option && option.checked && !option.disabled).map((option)=>option.value);\n            return {\n                value: values,\n                isValid: !!values.length\n            };\n        }\n        return options[0].checked && !options[0].disabled ? options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === \"\" ? validResult : {\n            value: options[0].value,\n            isValid: true\n        } : validResult : defaultResult;\n    }\n    return defaultResult;\n};\nvar getFieldValueAs = (value1, { valueAsNumber, valueAsDate, setValueAs })=>isUndefined(value1) ? value1 : valueAsNumber ? value1 === \"\" ? NaN : value1 ? +value1 : value1 : valueAsDate && isString(value1) ? new Date(value1) : setValueAs ? setValueAs(value1) : value1;\nconst defaultReturn = {\n    isValid: false,\n    value: null\n};\nvar getRadioValue = (options)=>Array.isArray(options) ? options.reduce((previous, option)=>option && option.checked && !option.disabled ? {\n            isValid: true,\n            value: option.value\n        } : previous, defaultReturn) : defaultReturn;\nfunction getFieldValue(_f) {\n    const ref = _f.ref;\n    if (isFileInput(ref)) {\n        return ref.files;\n    }\n    if (isRadioInput(ref)) {\n        return getRadioValue(_f.refs).value;\n    }\n    if (isMultipleSelect(ref)) {\n        return [\n            ...ref.selectedOptions\n        ].map(({ value: value1 })=>value1);\n    }\n    if (isCheckBoxInput(ref)) {\n        return getCheckboxValue(_f.refs).value;\n    }\n    return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation)=>{\n    const fields = {};\n    for (const name of fieldsNames){\n        const field = get(_fields, name);\n        field && set(fields, name, field._f);\n    }\n    return {\n        criteriaMode,\n        names: [\n            ...fieldsNames\n        ],\n        fields,\n        shouldUseNativeValidation\n    };\n};\nvar isRegex = (value1)=>value1 instanceof RegExp;\nvar getRuleValue = (rule)=>isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nvar getValidationModes = (mode)=>({\n        isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n        isOnBlur: mode === VALIDATION_MODE.onBlur,\n        isOnChange: mode === VALIDATION_MODE.onChange,\n        isOnAll: mode === VALIDATION_MODE.all,\n        isOnTouch: mode === VALIDATION_MODE.onTouched\n    });\nconst ASYNC_FUNCTION = \"AsyncFunction\";\nvar hasPromiseValidation = (fieldReference)=>!!fieldReference && !!fieldReference.validate && !!(isFunction(fieldReference.validate) && fieldReference.validate.constructor.name === ASYNC_FUNCTION || isObject(fieldReference.validate) && Object.values(fieldReference.validate).find((validateFunction)=>validateFunction.constructor.name === ASYNC_FUNCTION));\nvar hasValidation = (options)=>options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nvar isWatched = (name, _names, isBlurEvent)=>!isBlurEvent && (_names.watchAll || _names.watch.has(name) || [\n        ..._names.watch\n    ].some((watchName)=>name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly)=>{\n    for (const key of fieldsNames || Object.keys(fields)){\n        const field = get(fields, key);\n        if (field) {\n            const { _f, ...currentField } = field;\n            if (_f) {\n                if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n                    return true;\n                } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n                    return true;\n                } else {\n                    if (iterateFieldsByAction(currentField, action)) {\n                        break;\n                    }\n                }\n            } else if (isObject(currentField)) {\n                if (iterateFieldsByAction(currentField, action)) {\n                    break;\n                }\n            }\n        }\n    }\n    return;\n};\nfunction schemaErrorLookup(errors, _fields, name) {\n    const error = get(errors, name);\n    if (error || isKey(name)) {\n        return {\n            error,\n            name\n        };\n    }\n    const names = name.split(\".\");\n    while(names.length){\n        const fieldName = names.join(\".\");\n        const field = get(_fields, fieldName);\n        const foundError = get(errors, fieldName);\n        if (field && !Array.isArray(field) && name !== fieldName) {\n            return {\n                name\n            };\n        }\n        if (foundError && foundError.type) {\n            return {\n                name: fieldName,\n                error: foundError\n            };\n        }\n        names.pop();\n    }\n    return {\n        name\n    };\n}\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot)=>{\n    updateFormState(formStateData);\n    const { name, ...formState } = formStateData;\n    return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find((key)=>_proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar shouldSubscribeByName = (name, signalName, exact)=>!name || !signalName || name === signalName || convertToArrayPayload(name).some((currentName)=>currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode)=>{\n    if (mode.isOnAll) {\n        return false;\n    } else if (!isSubmitted && mode.isOnTouch) {\n        return !(isTouched || isBlurEvent);\n    } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n        return !isBlurEvent;\n    } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n        return isBlurEvent;\n    }\n    return true;\n};\nvar unsetEmptyArray = (ref, name)=>!compact(get(ref, name)).length && unset(ref, name);\nvar updateFieldArrayRootError = (errors, error, name)=>{\n    const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n    set(fieldArrayErrors, \"root\", error[name]);\n    set(errors, name, fieldArrayErrors);\n    return errors;\n};\nvar isMessage = (value1)=>isString(value1);\nfunction getValidateError(result, ref, type = \"validate\") {\n    if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n        return {\n            type,\n            message: isMessage(result) ? result : \"\",\n            ref\n        };\n    }\n}\nvar getValueAndMessage = (validationData)=>isObject(validationData) && !isRegex(validationData) ? validationData : {\n        value: validationData,\n        message: \"\"\n    };\nvar validateField = async (field, disabledFieldNames, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray)=>{\n    const { ref, refs, required, maxLength, minLength, min, max, pattern, validate, name, valueAsNumber, mount } = field._f;\n    const inputValue = get(formValues, name);\n    if (!mount || disabledFieldNames.has(name)) {\n        return {};\n    }\n    const inputRef = refs ? refs[0] : ref;\n    const setCustomValidity = (message)=>{\n        if (shouldUseNativeValidation && inputRef.reportValidity) {\n            inputRef.setCustomValidity(isBoolean(message) ? \"\" : message || \"\");\n            inputRef.reportValidity();\n        }\n    };\n    const error = {};\n    const isRadio = isRadioInput(ref);\n    const isCheckBox = isCheckBoxInput(ref);\n    const isRadioOrCheckbox = isRadio || isCheckBox;\n    const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === \"\" || inputValue === \"\" || Array.isArray(inputValue) && !inputValue.length;\n    const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n    const getMinMaxMessage = (exceedMax, maxLengthMessage, minLengthMessage, maxType = INPUT_VALIDATION_RULES.maxLength, minType = INPUT_VALIDATION_RULES.minLength)=>{\n        const message = exceedMax ? maxLengthMessage : minLengthMessage;\n        error[name] = {\n            type: exceedMax ? maxType : minType,\n            message,\n            ref,\n            ...appendErrorsCurry(exceedMax ? maxType : minType, message)\n        };\n    };\n    if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n        const { value: value1, message } = isMessage(required) ? {\n            value: !!required,\n            message: required\n        } : getValueAndMessage(required);\n        if (value1) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.required,\n                message,\n                ref: inputRef,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n        let exceedMax;\n        let exceedMin;\n        const maxOutput = getValueAndMessage(max);\n        const minOutput = getValueAndMessage(min);\n        if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n            const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n            if (!isNullOrUndefined(maxOutput.value)) {\n                exceedMax = valueNumber > maxOutput.value;\n            }\n            if (!isNullOrUndefined(minOutput.value)) {\n                exceedMin = valueNumber < minOutput.value;\n            }\n        } else {\n            const valueDate = ref.valueAsDate || new Date(inputValue);\n            const convertTimeToDate = (time)=>new Date(new Date().toDateString() + \" \" + time);\n            const isTime = ref.type == \"time\";\n            const isWeek = ref.type == \"week\";\n            if (isString(maxOutput.value) && inputValue) {\n                exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n            }\n            if (isString(minOutput.value) && inputValue) {\n                exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n            }\n        }\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n        const maxLengthOutput = getValueAndMessage(maxLength);\n        const minLengthOutput = getValueAndMessage(minLength);\n        const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n        const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n        if (exceedMax || exceedMin) {\n            getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(error[name].message);\n                return error;\n            }\n        }\n    }\n    if (pattern && !isEmpty && isString(inputValue)) {\n        const { value: patternValue, message } = getValueAndMessage(pattern);\n        if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n            error[name] = {\n                type: INPUT_VALIDATION_RULES.pattern,\n                message,\n                ref,\n                ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message)\n            };\n            if (!validateAllFieldCriteria) {\n                setCustomValidity(message);\n                return error;\n            }\n        }\n    }\n    if (validate) {\n        if (isFunction(validate)) {\n            const result = await validate(inputValue, formValues);\n            const validateError = getValidateError(result, inputRef);\n            if (validateError) {\n                error[name] = {\n                    ...validateError,\n                    ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message)\n                };\n                if (!validateAllFieldCriteria) {\n                    setCustomValidity(validateError.message);\n                    return error;\n                }\n            }\n        } else if (isObject(validate)) {\n            let validationResult = {};\n            for(const key in validate){\n                if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n                    break;\n                }\n                const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n                if (validateError) {\n                    validationResult = {\n                        ...validateError,\n                        ...appendErrorsCurry(key, validateError.message)\n                    };\n                    setCustomValidity(validateError.message);\n                    if (validateAllFieldCriteria) {\n                        error[name] = validationResult;\n                    }\n                }\n            }\n            if (!isEmptyObject(validationResult)) {\n                error[name] = {\n                    ref: inputRef,\n                    ...validationResult\n                };\n                if (!validateAllFieldCriteria) {\n                    return error;\n                }\n            }\n        }\n    }\n    setCustomValidity(true);\n    return error;\n};\nconst defaultOptions = {\n    mode: VALIDATION_MODE.onSubmit,\n    reValidateMode: VALIDATION_MODE.onChange,\n    shouldFocusError: true\n};\nfunction createFormControl(props = {}) {\n    let _options = {\n        ...defaultOptions,\n        ...props\n    };\n    let _formState = {\n        submitCount: 0,\n        isDirty: false,\n        isReady: false,\n        isLoading: isFunction(_options.defaultValues),\n        isValidating: false,\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        touchedFields: {},\n        dirtyFields: {},\n        validatingFields: {},\n        errors: _options.errors || {},\n        disabled: _options.disabled || false\n    };\n    const _fields = {};\n    let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n    let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n    let _state = {\n        action: false,\n        mount: false,\n        watch: false\n    };\n    let _names = {\n        mount: new Set(),\n        disabled: new Set(),\n        unMount: new Set(),\n        array: new Set(),\n        watch: new Set()\n    };\n    let delayErrorCallback;\n    let timer = 0;\n    const _proxyFormState = {\n        isDirty: false,\n        dirtyFields: false,\n        validatingFields: false,\n        touchedFields: false,\n        isValidating: false,\n        isValid: false,\n        errors: false\n    };\n    let _proxySubscribeFormState = {\n        ..._proxyFormState\n    };\n    const _subjects = {\n        array: createSubject(),\n        state: createSubject()\n    };\n    const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n    const debounce = (callback)=>(wait)=>{\n            clearTimeout(timer);\n            timer = setTimeout(callback, wait);\n        };\n    const _setValid = async (shouldUpdateValid)=>{\n        if (!_options.disabled && (_proxyFormState.isValid || _proxySubscribeFormState.isValid || shouldUpdateValid)) {\n            const isValid = _options.resolver ? isEmptyObject((await _runSchema()).errors) : await executeBuiltInValidation(_fields, true);\n            if (isValid !== _formState.isValid) {\n                _subjects.state.next({\n                    isValid\n                });\n            }\n        }\n    };\n    const _updateIsValidating = (names, isValidating)=>{\n        if (!_options.disabled && (_proxyFormState.isValidating || _proxyFormState.validatingFields || _proxySubscribeFormState.isValidating || _proxySubscribeFormState.validatingFields)) {\n            (names || Array.from(_names.mount)).forEach((name)=>{\n                if (name) {\n                    isValidating ? set(_formState.validatingFields, name, isValidating) : unset(_formState.validatingFields, name);\n                }\n            });\n            _subjects.state.next({\n                validatingFields: _formState.validatingFields,\n                isValidating: !isEmptyObject(_formState.validatingFields)\n            });\n        }\n    };\n    const _setFieldArray = (name, values = [], method, args, shouldSetValues = true, shouldUpdateFieldsAndState = true)=>{\n        if (args && method && !_options.disabled) {\n            _state.action = true;\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n                const fieldValues = method(get(_fields, name), args.argA, args.argB);\n                shouldSetValues && set(_fields, name, fieldValues);\n            }\n            if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n                const errors = method(get(_formState.errors, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.errors, name, errors);\n                unsetEmptyArray(_formState.errors, name);\n            }\n            if ((_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n                const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n                shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n            }\n            if (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) {\n                _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n            }\n            _subjects.state.next({\n                name,\n                isDirty: _getDirty(name, values),\n                dirtyFields: _formState.dirtyFields,\n                errors: _formState.errors,\n                isValid: _formState.isValid\n            });\n        } else {\n            set(_formValues, name, values);\n        }\n    };\n    const updateErrors = (name, error)=>{\n        set(_formState.errors, name, error);\n        _subjects.state.next({\n            errors: _formState.errors\n        });\n    };\n    const _setErrors = (errors)=>{\n        _formState.errors = errors;\n        _subjects.state.next({\n            errors: _formState.errors,\n            isValid: false\n        });\n    };\n    const updateValidAndValue = (name, shouldSkipSetValueAs, value1, ref)=>{\n        const field = get(_fields, name);\n        if (field) {\n            const defaultValue = get(_formValues, name, isUndefined(value1) ? get(_defaultValues, name) : value1);\n            isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n            _state.mount && _setValid();\n        }\n    };\n    const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender)=>{\n        let shouldUpdateField = false;\n        let isPreviousDirty = false;\n        const output = {\n            name\n        };\n        if (!_options.disabled) {\n            if (!isBlurEvent || shouldDirty) {\n                if (_proxyFormState.isDirty || _proxySubscribeFormState.isDirty) {\n                    isPreviousDirty = _formState.isDirty;\n                    _formState.isDirty = output.isDirty = _getDirty();\n                    shouldUpdateField = isPreviousDirty !== output.isDirty;\n                }\n                const isCurrentFieldPristine = deepEqual(get(_defaultValues, name), fieldValue);\n                isPreviousDirty = !!get(_formState.dirtyFields, name);\n                isCurrentFieldPristine ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n                output.dirtyFields = _formState.dirtyFields;\n                shouldUpdateField = shouldUpdateField || (_proxyFormState.dirtyFields || _proxySubscribeFormState.dirtyFields) && isPreviousDirty !== !isCurrentFieldPristine;\n            }\n            if (isBlurEvent) {\n                const isPreviousFieldTouched = get(_formState.touchedFields, name);\n                if (!isPreviousFieldTouched) {\n                    set(_formState.touchedFields, name, isBlurEvent);\n                    output.touchedFields = _formState.touchedFields;\n                    shouldUpdateField = shouldUpdateField || (_proxyFormState.touchedFields || _proxySubscribeFormState.touchedFields) && isPreviousFieldTouched !== isBlurEvent;\n                }\n            }\n            shouldUpdateField && shouldRender && _subjects.state.next(output);\n        }\n        return shouldUpdateField ? output : {};\n    };\n    const shouldRenderByError = (name, isValid, error, fieldState)=>{\n        const previousFieldError = get(_formState.errors, name);\n        const shouldUpdateValid = (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isBoolean(isValid) && _formState.isValid !== isValid;\n        if (_options.delayError && error) {\n            delayErrorCallback = debounce(()=>updateErrors(name, error));\n            delayErrorCallback(_options.delayError);\n        } else {\n            clearTimeout(timer);\n            delayErrorCallback = null;\n            error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n        }\n        if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n            const updatedFormState = {\n                ...fieldState,\n                ...shouldUpdateValid && isBoolean(isValid) ? {\n                    isValid\n                } : {},\n                errors: _formState.errors,\n                name\n            };\n            _formState = {\n                ..._formState,\n                ...updatedFormState\n            };\n            _subjects.state.next(updatedFormState);\n        }\n    };\n    const _runSchema = async (name)=>{\n        _updateIsValidating(name, true);\n        const result = await _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n        _updateIsValidating(name);\n        return result;\n    };\n    const executeSchemaAndUpdateState = async (names)=>{\n        const { errors } = await _runSchema(names);\n        if (names) {\n            for (const name of names){\n                const error = get(errors, name);\n                error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n            }\n        } else {\n            _formState.errors = errors;\n        }\n        return errors;\n    };\n    const executeBuiltInValidation = async (fields, shouldOnlyCheckValid, context = {\n        valid: true\n    })=>{\n        for(const name in fields){\n            const field = fields[name];\n            if (field) {\n                const { _f, ...fieldValue } = field;\n                if (_f) {\n                    const isFieldArrayRoot = _names.array.has(_f.name);\n                    const isPromiseFunction = field._f && hasPromiseValidation(field._f);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ], true);\n                    }\n                    const fieldError = await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n                    if (isPromiseFunction && _proxyFormState.validatingFields) {\n                        _updateIsValidating([\n                            name\n                        ]);\n                    }\n                    if (fieldError[_f.name]) {\n                        context.valid = false;\n                        if (shouldOnlyCheckValid) {\n                            break;\n                        }\n                    }\n                    !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n                }\n                !isEmptyObject(fieldValue) && await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context);\n            }\n        }\n        return context.valid;\n    };\n    const _removeUnmounted = ()=>{\n        for (const name of _names.unMount){\n            const field = get(_fields, name);\n            field && (field._f.refs ? field._f.refs.every((ref)=>!live(ref)) : !live(field._f.ref)) && unregister(name);\n        }\n        _names.unMount = new Set();\n    };\n    const _getDirty = (name, data)=>!_options.disabled && (name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n    const _getWatch = (names, defaultValue, isGlobal)=>generateWatchOutput(names, _names, {\n            ..._state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n                [names]: defaultValue\n            } : defaultValue\n        }, isGlobal, defaultValue);\n    const _getFieldArray = (name)=>compact(get(_state.mount ? _formValues : _defaultValues, name, _options.shouldUnregister ? get(_defaultValues, name, []) : []));\n    const setFieldValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        let fieldValue = value1;\n        if (field) {\n            const fieldReference = field._f;\n            if (fieldReference) {\n                !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value1, fieldReference));\n                fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value1) ? \"\" : value1;\n                if (isMultipleSelect(fieldReference.ref)) {\n                    [\n                        ...fieldReference.ref.options\n                    ].forEach((optionRef)=>optionRef.selected = fieldValue.includes(optionRef.value));\n                } else if (fieldReference.refs) {\n                    if (isCheckBoxInput(fieldReference.ref)) {\n                        fieldReference.refs.forEach((checkboxRef)=>{\n                            if (!checkboxRef.defaultChecked || !checkboxRef.disabled) {\n                                if (Array.isArray(fieldValue)) {\n                                    checkboxRef.checked = !!fieldValue.find((data)=>data === checkboxRef.value);\n                                } else {\n                                    checkboxRef.checked = fieldValue === checkboxRef.value || !!fieldValue;\n                                }\n                            }\n                        });\n                    } else {\n                        fieldReference.refs.forEach((radioRef)=>radioRef.checked = radioRef.value === fieldValue);\n                    }\n                } else if (isFileInput(fieldReference.ref)) {\n                    fieldReference.ref.value = \"\";\n                } else {\n                    fieldReference.ref.value = fieldValue;\n                    if (!fieldReference.ref.type) {\n                        _subjects.state.next({\n                            name,\n                            values: cloneObject(_formValues)\n                        });\n                    }\n                }\n            }\n        }\n        (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n        options.shouldValidate && trigger(name);\n    };\n    const setValues = (name, value1, options)=>{\n        for(const fieldKey in value1){\n            if (!value1.hasOwnProperty(fieldKey)) {\n                return;\n            }\n            const fieldValue = value1[fieldKey];\n            const fieldName = `${name}.${fieldKey}`;\n            const field = get(_fields, fieldName);\n            (_names.array.has(name) || isObject(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n        }\n    };\n    const setValue = (name, value1, options = {})=>{\n        const field = get(_fields, name);\n        const isFieldArray = _names.array.has(name);\n        const cloneValue = cloneObject(value1);\n        set(_formValues, name, cloneValue);\n        if (isFieldArray) {\n            _subjects.array.next({\n                name,\n                values: cloneObject(_formValues)\n            });\n            if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields || _proxySubscribeFormState.isDirty || _proxySubscribeFormState.dirtyFields) && options.shouldDirty) {\n                _subjects.state.next({\n                    name,\n                    dirtyFields: getDirtyFields(_defaultValues, _formValues),\n                    isDirty: _getDirty(name, cloneValue)\n                });\n            }\n        } else {\n            field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n        }\n        isWatched(name, _names) && _subjects.state.next({\n            ..._formState\n        });\n        _subjects.state.next({\n            name: _state.mount ? name : undefined,\n            values: cloneObject(_formValues)\n        });\n    };\n    const onChange = async (event)=>{\n        _state.mount = true;\n        const target = event.target;\n        let name = target.name;\n        let isFieldValueUpdated = true;\n        const field = get(_fields, name);\n        const _updateIsFieldValueUpdated = (fieldValue)=>{\n            isFieldValueUpdated = Number.isNaN(fieldValue) || isDateObject(fieldValue) && isNaN(fieldValue.getTime()) || deepEqual(fieldValue, get(_formValues, name, fieldValue));\n        };\n        const validationModeBeforeSubmit = getValidationModes(_options.mode);\n        const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n        if (field) {\n            let error;\n            let isValid;\n            const fieldValue = target.type ? getFieldValue(field._f) : getEventValue(event);\n            const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n            const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n            const watched = isWatched(name, _names, isBlurEvent);\n            set(_formValues, name, fieldValue);\n            if (isBlurEvent) {\n                field._f.onBlur && field._f.onBlur(event);\n                delayErrorCallback && delayErrorCallback(0);\n            } else if (field._f.onChange) {\n                field._f.onChange(event);\n            }\n            const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent);\n            const shouldRender = !isEmptyObject(fieldState) || watched;\n            !isBlurEvent && _subjects.state.next({\n                name,\n                type: event.type,\n                values: cloneObject(_formValues)\n            });\n            if (shouldSkipValidation) {\n                if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                    if (_options.mode === \"onBlur\") {\n                        if (isBlurEvent) {\n                            _setValid();\n                        }\n                    } else if (!isBlurEvent) {\n                        _setValid();\n                    }\n                }\n                return shouldRender && _subjects.state.next({\n                    name,\n                    ...watched ? {} : fieldState\n                });\n            }\n            !isBlurEvent && watched && _subjects.state.next({\n                ..._formState\n            });\n            if (_options.resolver) {\n                const { errors } = await _runSchema([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n                    const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n                    error = errorLookupResult.error;\n                    name = errorLookupResult.name;\n                    isValid = isEmptyObject(errors);\n                }\n            } else {\n                _updateIsValidating([\n                    name\n                ], true);\n                error = (await validateField(field, _names.disabled, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n                _updateIsValidating([\n                    name\n                ]);\n                _updateIsFieldValueUpdated(fieldValue);\n                if (isFieldValueUpdated) {\n                    if (error) {\n                        isValid = false;\n                    } else if (_proxyFormState.isValid || _proxySubscribeFormState.isValid) {\n                        isValid = await executeBuiltInValidation(_fields, true);\n                    }\n                }\n            }\n            if (isFieldValueUpdated) {\n                field._f.deps && trigger(field._f.deps);\n                shouldRenderByError(name, isValid, error, fieldState);\n            }\n        }\n    };\n    const _focusInput = (ref, key)=>{\n        if (get(_formState.errors, key) && ref.focus) {\n            ref.focus();\n            return 1;\n        }\n        return;\n    };\n    const trigger = async (name, options = {})=>{\n        let isValid;\n        let validationResult;\n        const fieldNames = convertToArrayPayload(name);\n        if (_options.resolver) {\n            const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n            isValid = isEmptyObject(errors);\n            validationResult = name ? !fieldNames.some((name)=>get(errors, name)) : isValid;\n        } else if (name) {\n            validationResult = (await Promise.all(fieldNames.map(async (fieldName)=>{\n                const field = get(_fields, fieldName);\n                return await executeBuiltInValidation(field && field._f ? {\n                    [fieldName]: field\n                } : field);\n            }))).every(Boolean);\n            !(!validationResult && !_formState.isValid) && _setValid();\n        } else {\n            validationResult = isValid = await executeBuiltInValidation(_fields);\n        }\n        _subjects.state.next({\n            ...!isString(name) || (_proxyFormState.isValid || _proxySubscribeFormState.isValid) && isValid !== _formState.isValid ? {} : {\n                name\n            },\n            ..._options.resolver || !name ? {\n                isValid\n            } : {},\n            errors: _formState.errors\n        });\n        options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n        return validationResult;\n    };\n    const getValues = (fieldNames)=>{\n        const values = {\n            ..._state.mount ? _formValues : _defaultValues\n        };\n        return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map((name)=>get(values, name));\n    };\n    const getFieldState = (name, formState)=>({\n            invalid: !!get((formState || _formState).errors, name),\n            isDirty: !!get((formState || _formState).dirtyFields, name),\n            error: get((formState || _formState).errors, name),\n            isValidating: !!get(_formState.validatingFields, name),\n            isTouched: !!get((formState || _formState).touchedFields, name)\n        });\n    const clearErrors = (name)=>{\n        name && convertToArrayPayload(name).forEach((inputName)=>unset(_formState.errors, inputName));\n        _subjects.state.next({\n            errors: name ? _formState.errors : {}\n        });\n    };\n    const setError = (name, error, options)=>{\n        const ref = (get(_fields, name, {\n            _f: {}\n        })._f || {}).ref;\n        const currentError = get(_formState.errors, name) || {};\n        // Don't override existing error messages elsewhere in the object tree.\n        const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n        set(_formState.errors, name, {\n            ...restOfErrorTree,\n            ...error,\n            ref\n        });\n        _subjects.state.next({\n            name,\n            errors: _formState.errors,\n            isValid: false\n        });\n        options && options.shouldFocus && ref && ref.focus && ref.focus();\n    };\n    const watch = (name, defaultValue)=>isFunction(name) ? _subjects.state.subscribe({\n            next: (payload)=>name(_getWatch(undefined, defaultValue), payload)\n        }) : _getWatch(name, defaultValue, true);\n    const _subscribe = (props)=>_subjects.state.subscribe({\n            next: (formState)=>{\n                if (shouldSubscribeByName(props.name, formState.name, props.exact) && shouldRenderFormState(formState, props.formState || _proxyFormState, _setFormState, props.reRenderRoot)) {\n                    props.callback({\n                        values: {\n                            ..._formValues\n                        },\n                        ..._formState,\n                        ...formState\n                    });\n                }\n            }\n        }).unsubscribe;\n    const subscribe = (props)=>{\n        _state.mount = true;\n        _proxySubscribeFormState = {\n            ..._proxySubscribeFormState,\n            ...props.formState\n        };\n        return _subscribe({\n            ...props,\n            formState: _proxySubscribeFormState\n        });\n    };\n    const unregister = (name, options = {})=>{\n        for (const fieldName of name ? convertToArrayPayload(name) : _names.mount){\n            _names.mount.delete(fieldName);\n            _names.array.delete(fieldName);\n            if (!options.keepValue) {\n                unset(_fields, fieldName);\n                unset(_formValues, fieldName);\n            }\n            !options.keepError && unset(_formState.errors, fieldName);\n            !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n            !options.keepTouched && unset(_formState.touchedFields, fieldName);\n            !options.keepIsValidating && unset(_formState.validatingFields, fieldName);\n            !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n        }\n        _subjects.state.next({\n            values: cloneObject(_formValues)\n        });\n        _subjects.state.next({\n            ..._formState,\n            ...!options.keepDirty ? {} : {\n                isDirty: _getDirty()\n            }\n        });\n        !options.keepIsValid && _setValid();\n    };\n    const _setDisabledField = ({ disabled, name })=>{\n        if (isBoolean(disabled) && _state.mount || !!disabled || _names.disabled.has(name)) {\n            disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n        }\n    };\n    const register = (name, options = {})=>{\n        let field = get(_fields, name);\n        const disabledIsDefined = isBoolean(options.disabled) || isBoolean(_options.disabled);\n        set(_fields, name, {\n            ...field || {},\n            _f: {\n                ...field && field._f ? field._f : {\n                    ref: {\n                        name\n                    }\n                },\n                name,\n                mount: true,\n                ...options\n            }\n        });\n        _names.mount.add(name);\n        if (field) {\n            _setDisabledField({\n                disabled: isBoolean(options.disabled) ? options.disabled : _options.disabled,\n                name\n            });\n        } else {\n            updateValidAndValue(name, true, options.value);\n        }\n        return {\n            ...disabledIsDefined ? {\n                disabled: options.disabled || _options.disabled\n            } : {},\n            ..._options.progressive ? {\n                required: !!options.required,\n                min: getRuleValue(options.min),\n                max: getRuleValue(options.max),\n                minLength: getRuleValue(options.minLength),\n                maxLength: getRuleValue(options.maxLength),\n                pattern: getRuleValue(options.pattern)\n            } : {},\n            name,\n            onChange,\n            onBlur: onChange,\n            ref: (ref)=>{\n                if (ref) {\n                    register(name, options);\n                    field = get(_fields, name);\n                    const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll(\"input,select,textarea\")[0] || ref : ref : ref;\n                    const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n                    const refs = field._f.refs || [];\n                    if (radioOrCheckbox ? refs.find((option)=>option === fieldRef) : fieldRef === field._f.ref) {\n                        return;\n                    }\n                    set(_fields, name, {\n                        _f: {\n                            ...field._f,\n                            ...radioOrCheckbox ? {\n                                refs: [\n                                    ...refs.filter(live),\n                                    fieldRef,\n                                    ...Array.isArray(get(_defaultValues, name)) ? [\n                                        {}\n                                    ] : []\n                                ],\n                                ref: {\n                                    type: fieldRef.type,\n                                    name\n                                }\n                            } : {\n                                ref: fieldRef\n                            }\n                        }\n                    });\n                    updateValidAndValue(name, false, undefined, fieldRef);\n                } else {\n                    field = get(_fields, name, {});\n                    if (field._f) {\n                        field._f.mount = false;\n                    }\n                    (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n                }\n            }\n        };\n    };\n    const _focusError = ()=>_options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n    const _disableForm = (disabled)=>{\n        if (isBoolean(disabled)) {\n            _subjects.state.next({\n                disabled\n            });\n            iterateFieldsByAction(_fields, (ref, name)=>{\n                const currentField = get(_fields, name);\n                if (currentField) {\n                    ref.disabled = currentField._f.disabled || disabled;\n                    if (Array.isArray(currentField._f.refs)) {\n                        currentField._f.refs.forEach((inputRef)=>{\n                            inputRef.disabled = currentField._f.disabled || disabled;\n                        });\n                    }\n                }\n            }, 0, false);\n        }\n    };\n    const handleSubmit = (onValid, onInvalid)=>async (e)=>{\n            let onValidError = undefined;\n            if (e) {\n                e.preventDefault && e.preventDefault();\n                e.persist && e.persist();\n            }\n            let fieldValues = cloneObject(_formValues);\n            _subjects.state.next({\n                isSubmitting: true\n            });\n            if (_options.resolver) {\n                const { errors, values } = await _runSchema();\n                _formState.errors = errors;\n                fieldValues = values;\n            } else {\n                await executeBuiltInValidation(_fields);\n            }\n            if (_names.disabled.size) {\n                for (const name of _names.disabled){\n                    set(fieldValues, name, undefined);\n                }\n            }\n            unset(_formState.errors, \"root\");\n            if (isEmptyObject(_formState.errors)) {\n                _subjects.state.next({\n                    errors: {}\n                });\n                try {\n                    await onValid(fieldValues, e);\n                } catch (error) {\n                    onValidError = error;\n                }\n            } else {\n                if (onInvalid) {\n                    await onInvalid({\n                        ..._formState.errors\n                    }, e);\n                }\n                _focusError();\n                setTimeout(_focusError);\n            }\n            _subjects.state.next({\n                isSubmitted: true,\n                isSubmitting: false,\n                isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n                submitCount: _formState.submitCount + 1,\n                errors: _formState.errors\n            });\n            if (onValidError) {\n                throw onValidError;\n            }\n        };\n    const resetField = (name, options = {})=>{\n        if (get(_fields, name)) {\n            if (isUndefined(options.defaultValue)) {\n                setValue(name, cloneObject(get(_defaultValues, name)));\n            } else {\n                setValue(name, options.defaultValue);\n                set(_defaultValues, name, cloneObject(options.defaultValue));\n            }\n            if (!options.keepTouched) {\n                unset(_formState.touchedFields, name);\n            }\n            if (!options.keepDirty) {\n                unset(_formState.dirtyFields, name);\n                _formState.isDirty = options.defaultValue ? _getDirty(name, cloneObject(get(_defaultValues, name))) : _getDirty();\n            }\n            if (!options.keepError) {\n                unset(_formState.errors, name);\n                _proxyFormState.isValid && _setValid();\n            }\n            _subjects.state.next({\n                ..._formState\n            });\n        }\n    };\n    const _reset = (formValues, keepStateOptions = {})=>{\n        const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n        const cloneUpdatedValues = cloneObject(updatedValues);\n        const isEmptyResetValues = isEmptyObject(formValues);\n        const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n        if (!keepStateOptions.keepDefaultValues) {\n            _defaultValues = updatedValues;\n        }\n        if (!keepStateOptions.keepValues) {\n            if (keepStateOptions.keepDirtyValues) {\n                const fieldsToCheck = new Set([\n                    ..._names.mount,\n                    ...Object.keys(getDirtyFields(_defaultValues, _formValues))\n                ]);\n                for (const fieldName of Array.from(fieldsToCheck)){\n                    get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n                }\n            } else {\n                if (isWeb && isUndefined(formValues)) {\n                    for (const name of _names.mount){\n                        const field = get(_fields, name);\n                        if (field && field._f) {\n                            const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n                            if (isHTMLElement(fieldReference)) {\n                                const form = fieldReference.closest(\"form\");\n                                if (form) {\n                                    form.reset();\n                                    break;\n                                }\n                            }\n                        }\n                    }\n                }\n                for (const fieldName of _names.mount){\n                    setValue(fieldName, get(values, fieldName));\n                }\n            }\n            _formValues = cloneObject(values);\n            _subjects.array.next({\n                values: {\n                    ...values\n                }\n            });\n            _subjects.state.next({\n                values: {\n                    ...values\n                }\n            });\n        }\n        _names = {\n            mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n            unMount: new Set(),\n            array: new Set(),\n            disabled: new Set(),\n            watch: new Set(),\n            watchAll: false,\n            focus: \"\"\n        };\n        _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid || !!keepStateOptions.keepDirtyValues;\n        _state.watch = !!_options.shouldUnregister;\n        _subjects.state.next({\n            submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n            isDirty: isEmptyResetValues ? false : keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n            isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n            dirtyFields: isEmptyResetValues ? {} : keepStateOptions.keepDirtyValues ? keepStateOptions.keepDefaultValues && _formValues ? getDirtyFields(_defaultValues, _formValues) : _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : keepStateOptions.keepDirty ? _formState.dirtyFields : {},\n            touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n            errors: keepStateOptions.keepErrors ? _formState.errors : {},\n            isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n            isSubmitting: false\n        });\n    };\n    const reset = (formValues, keepStateOptions)=>_reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n    const setFocus = (name, options = {})=>{\n        const field = get(_fields, name);\n        const fieldReference = field && field._f;\n        if (fieldReference) {\n            const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n            if (fieldRef.focus) {\n                fieldRef.focus();\n                options.shouldSelect && isFunction(fieldRef.select) && fieldRef.select();\n            }\n        }\n    };\n    const _setFormState = (updatedFormState)=>{\n        _formState = {\n            ..._formState,\n            ...updatedFormState\n        };\n    };\n    const _resetDefaultValues = ()=>isFunction(_options.defaultValues) && _options.defaultValues().then((values)=>{\n            reset(values, _options.resetOptions);\n            _subjects.state.next({\n                isLoading: false\n            });\n        });\n    const methods = {\n        control: {\n            register,\n            unregister,\n            getFieldState,\n            handleSubmit,\n            setError,\n            _subscribe,\n            _runSchema,\n            _getWatch,\n            _getDirty,\n            _setValid,\n            _setFieldArray,\n            _setDisabledField,\n            _setErrors,\n            _getFieldArray,\n            _reset,\n            _resetDefaultValues,\n            _removeUnmounted,\n            _disableForm,\n            _subjects,\n            _proxyFormState,\n            get _fields () {\n                return _fields;\n            },\n            get _formValues () {\n                return _formValues;\n            },\n            get _state () {\n                return _state;\n            },\n            set _state (value){\n                _state = value;\n            },\n            get _defaultValues () {\n                return _defaultValues;\n            },\n            get _names () {\n                return _names;\n            },\n            set _names (value){\n                _names = value;\n            },\n            get _formState () {\n                return _formState;\n            },\n            get _options () {\n                return _options;\n            },\n            set _options (value){\n                _options = {\n                    ..._options,\n                    ...value\n                };\n            }\n        },\n        subscribe,\n        trigger,\n        register,\n        handleSubmit,\n        watch,\n        setValue,\n        getValues,\n        reset,\n        resetField,\n        clearErrors,\n        unregister,\n        setError,\n        setFocus,\n        getFieldState\n    };\n    return {\n        ...methods,\n        formControl: methods\n    };\n}\nvar generateId = ()=>{\n    const d = typeof performance === \"undefined\" ? Date.now() : performance.now() * 1000;\n    return \"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx\".replace(/[xy]/g, (c)=>{\n        const r = (Math.random() * 16 + d) % 16 | 0;\n        return (c == \"x\" ? r : r & 0x3 | 0x8).toString(16);\n    });\n};\nvar getFocusFieldName = (name, index, options = {})=>options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.` : \"\";\nvar appendAt = (data, value1)=>[\n        ...data,\n        ...convertToArrayPayload(value1)\n    ];\nvar fillEmptyArray = (value1)=>Array.isArray(value1) ? value1.map(()=>undefined) : undefined;\nfunction insert(data, index, value1) {\n    return [\n        ...data.slice(0, index),\n        ...convertToArrayPayload(value1),\n        ...data.slice(index)\n    ];\n}\nvar moveArrayAt = (data, from, to)=>{\n    if (!Array.isArray(data)) {\n        return [];\n    }\n    if (isUndefined(data[to])) {\n        data[to] = undefined;\n    }\n    data.splice(to, 0, data.splice(from, 1)[0]);\n    return data;\n};\nvar prependAt = (data, value1)=>[\n        ...convertToArrayPayload(value1),\n        ...convertToArrayPayload(data)\n    ];\nfunction removeAtIndexes(data, indexes) {\n    let i = 0;\n    const temp = [\n        ...data\n    ];\n    for (const index of indexes){\n        temp.splice(index - i, 1);\n        i++;\n    }\n    return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index)=>isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b)=>a - b));\nvar swapArrayAt = (data, indexA, indexB)=>{\n    [data[indexA], data[indexB]] = [\n        data[indexB],\n        data[indexA]\n    ];\n};\nvar updateAt = (fieldValues, index, value1)=>{\n    fieldValues[index] = value1;\n    return fieldValues;\n};\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */ function useFieldArray(props) {\n    const methods = useFormContext();\n    const { control = methods.control, name, keyName = \"id\", shouldUnregister, rules } = props;\n    const [fields, setFields] = react__WEBPACK_IMPORTED_MODULE_0__.useState(control._getFieldArray(name));\n    const ids = react__WEBPACK_IMPORTED_MODULE_0__.useRef(control._getFieldArray(name).map(generateId));\n    const _fieldIds = react__WEBPACK_IMPORTED_MODULE_0__.useRef(fields);\n    const _name = react__WEBPACK_IMPORTED_MODULE_0__.useRef(name);\n    const _actioned = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    _name.current = name;\n    _fieldIds.current = fields;\n    control._names.array.add(name);\n    rules && control.register(name, rules);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._subjects.array.subscribe({\n            next: ({ values, name: fieldArrayName })=>{\n                if (fieldArrayName === _name.current || !fieldArrayName) {\n                    const fieldValues = get(values, _name.current);\n                    if (Array.isArray(fieldValues)) {\n                        setFields(fieldValues);\n                        ids.current = fieldValues.map(generateId);\n                    }\n                }\n            }\n        }).unsubscribe, [\n        control\n    ]);\n    const updateValues = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((updatedFieldArrayValues)=>{\n        _actioned.current = true;\n        control._setFieldArray(name, updatedFieldArrayValues);\n    }, [\n        control,\n        name\n    ]);\n    const append = (value1, options)=>{\n        const appendValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n        control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n        ids.current = appendAt(ids.current, appendValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, appendAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const prepend = (value1, options)=>{\n        const prependValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n        control._names.focus = getFocusFieldName(name, 0, options);\n        ids.current = prependAt(ids.current, prependValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, prependAt, {\n            argA: fillEmptyArray(value1)\n        });\n    };\n    const remove = (index)=>{\n        const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n        ids.current = removeArrayAt(ids.current, index);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        !Array.isArray(get(control._fields, name)) && set(control._fields, name, undefined);\n        control._setFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n            argA: index\n        });\n    };\n    const insert$1 = (index, value1, options)=>{\n        const insertValue = convertToArrayPayload(cloneObject(value1));\n        const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n        control._names.focus = getFocusFieldName(name, index, options);\n        ids.current = insert(ids.current, index, insertValue.map(generateId));\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, insert, {\n            argA: index,\n            argB: fillEmptyArray(value1)\n        });\n    };\n    const swap = (indexA, indexB)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n        swapArrayAt(ids.current, indexA, indexB);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n            argA: indexA,\n            argB: indexB\n        }, false);\n    };\n    const move = (from, to)=>{\n        const updatedFieldArrayValues = control._getFieldArray(name);\n        moveArrayAt(updatedFieldArrayValues, from, to);\n        moveArrayAt(ids.current, from, to);\n        updateValues(updatedFieldArrayValues);\n        setFields(updatedFieldArrayValues);\n        control._setFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n            argA: from,\n            argB: to\n        }, false);\n    };\n    const update = (index, value1)=>{\n        const updateValue = cloneObject(value1);\n        const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n        ids.current = [\n            ...updatedFieldArrayValues\n        ].map((item, i)=>!item || i === index ? generateId() : ids.current[i]);\n        updateValues(updatedFieldArrayValues);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._setFieldArray(name, updatedFieldArrayValues, updateAt, {\n            argA: index,\n            argB: updateValue\n        }, true, false);\n    };\n    const replace = (value1)=>{\n        const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value1));\n        ids.current = updatedFieldArrayValues.map(generateId);\n        updateValues([\n            ...updatedFieldArrayValues\n        ]);\n        setFields([\n            ...updatedFieldArrayValues\n        ]);\n        control._setFieldArray(name, [\n            ...updatedFieldArrayValues\n        ], (data)=>data, {}, true, false);\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        control._state.action = false;\n        isWatched(name, control._names) && control._subjects.state.next({\n            ...control._formState\n        });\n        if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted) && !getValidationModes(control._options.reValidateMode).isOnSubmit) {\n            if (control._options.resolver) {\n                control._runSchema([\n                    name\n                ]).then((result)=>{\n                    const error = get(result.errors, name);\n                    const existingError = get(control._formState.errors, name);\n                    if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n                        error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n                        control._subjects.state.next({\n                            errors: control._formState.errors\n                        });\n                    }\n                });\n            } else {\n                const field = get(control._fields, name);\n                if (field && field._f && !(getValidationModes(control._options.reValidateMode).isOnSubmit && getValidationModes(control._options.mode).isOnSubmit)) {\n                    validateField(field, control._names.disabled, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then((error)=>!isEmptyObject(error) && control._subjects.state.next({\n                            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n                        }));\n                }\n            }\n        }\n        control._subjects.state.next({\n            name,\n            values: cloneObject(control._formValues)\n        });\n        control._names.focus && iterateFieldsByAction(control._fields, (ref, key)=>{\n            if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n                ref.focus();\n                return 1;\n            }\n            return;\n        });\n        control._names.focus = \"\";\n        control._setValid();\n        _actioned.current = false;\n    }, [\n        fields,\n        name,\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        !get(control._formValues, name) && control._setFieldArray(name);\n        return ()=>{\n            const updateMounted = (name, value1)=>{\n                const field = get(control._fields, name);\n                if (field && field._f) {\n                    field._f.mount = value1;\n                }\n            };\n            control._options.shouldUnregister || shouldUnregister ? control.unregister(name) : updateMounted(name, false);\n        };\n    }, [\n        name,\n        control,\n        keyName,\n        shouldUnregister\n    ]);\n    return {\n        swap: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(swap, [\n            updateValues,\n            name,\n            control\n        ]),\n        move: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(move, [\n            updateValues,\n            name,\n            control\n        ]),\n        prepend: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(prepend, [\n            updateValues,\n            name,\n            control\n        ]),\n        append: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(append, [\n            updateValues,\n            name,\n            control\n        ]),\n        remove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(remove, [\n            updateValues,\n            name,\n            control\n        ]),\n        insert: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(insert$1, [\n            updateValues,\n            name,\n            control\n        ]),\n        update: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(update, [\n            updateValues,\n            name,\n            control\n        ]),\n        replace: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(replace, [\n            updateValues,\n            name,\n            control\n        ]),\n        fields: react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>fields.map((field, index)=>({\n                    ...field,\n                    [keyName]: ids.current[index] || generateId()\n                })), [\n            fields,\n            keyName\n        ])\n    };\n}\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */ function useForm(props = {}) {\n    const _formControl = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const _values = react__WEBPACK_IMPORTED_MODULE_0__.useRef(undefined);\n    const [formState, updateFormState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        isDirty: false,\n        isValidating: false,\n        isLoading: isFunction(props.defaultValues),\n        isSubmitted: false,\n        isSubmitting: false,\n        isSubmitSuccessful: false,\n        isValid: false,\n        submitCount: 0,\n        dirtyFields: {},\n        touchedFields: {},\n        validatingFields: {},\n        errors: props.errors || {},\n        disabled: props.disabled || false,\n        isReady: false,\n        defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n    });\n    if (!_formControl.current) {\n        _formControl.current = {\n            ...props.formControl ? props.formControl : createFormControl(props),\n            formState\n        };\n        if (props.formControl && props.defaultValues && !isFunction(props.defaultValues)) {\n            props.formControl.reset(props.defaultValues, props.resetOptions);\n        }\n    }\n    const control = _formControl.current.control;\n    control._options = props;\n    useIsomorphicLayoutEffect(()=>{\n        const sub = control._subscribe({\n            formState: control._proxyFormState,\n            callback: ()=>updateFormState({\n                    ...control._formState\n                }),\n            reRenderRoot: true\n        });\n        updateFormState((data)=>({\n                ...data,\n                isReady: true\n            }));\n        control._formState.isReady = true;\n        return sub;\n    }, [\n        control\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>control._disableForm(props.disabled), [\n        control,\n        props.disabled\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.mode) {\n            control._options.mode = props.mode;\n        }\n        if (props.reValidateMode) {\n            control._options.reValidateMode = props.reValidateMode;\n        }\n        if (props.errors && !isEmptyObject(props.errors)) {\n            control._setErrors(props.errors);\n        }\n    }, [\n        control,\n        props.errors,\n        props.mode,\n        props.reValidateMode\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        props.shouldUnregister && control._subjects.state.next({\n            values: control._getWatch()\n        });\n    }, [\n        control,\n        props.shouldUnregister\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (control._proxyFormState.isDirty) {\n            const isDirty = control._getDirty();\n            if (isDirty !== formState.isDirty) {\n                control._subjects.state.next({\n                    isDirty\n                });\n            }\n        }\n    }, [\n        control,\n        formState.isDirty\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (props.values && !deepEqual(props.values, _values.current)) {\n            control._reset(props.values, control._options.resetOptions);\n            _values.current = props.values;\n            updateFormState((state)=>({\n                    ...state\n                }));\n        } else {\n            control._resetDefaultValues();\n        }\n    }, [\n        control,\n        props.values\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!control._state.mount) {\n            control._setValid();\n            control._state.mount = true;\n        }\n        if (control._state.watch) {\n            control._state.watch = false;\n            control._subjects.state.next({\n                ...control._formState\n            });\n        }\n        control._removeUnmounted();\n    });\n    _formControl.current.formState = getProxyFormState(formState, control);\n    return _formControl.current;\n}\n //# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtaG9vay1mb3JtQDcuNTYuNF9yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL3JlYWN0LWhvb2stZm9ybS9kaXN0L2luZGV4LmVzbS5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBK0I7QUFDSTtBQUVuQyxJQUFJRSxrQkFBa0IsQ0FBQ0MsVUFBWUEsUUFBUUMsSUFBSSxLQUFLO0FBRXBELElBQUlDLGVBQWUsQ0FBQ0MsU0FBVUEsa0JBQWlCQztBQUUvQyxJQUFJQyxvQkFBb0IsQ0FBQ0YsU0FBVUEsVUFBUztBQUU1QyxNQUFNRyxlQUFlLENBQUNILFNBQVUsT0FBT0EsV0FBVTtBQUNqRCxJQUFJSSxXQUFXLENBQUNKLFNBQVUsQ0FBQ0Usa0JBQWtCRixXQUN6QyxDQUFDSyxNQUFNQyxPQUFPLENBQUNOLFdBQ2ZHLGFBQWFILFdBQ2IsQ0FBQ0QsYUFBYUM7QUFFbEIsSUFBSU8sZ0JBQWdCLENBQUNDLFFBQVVKLFNBQVNJLFVBQVVBLE1BQU1DLE1BQU0sR0FDeERiLGdCQUFnQlksTUFBTUMsTUFBTSxJQUN4QkQsTUFBTUMsTUFBTSxDQUFDQyxPQUFPLEdBQ3BCRixNQUFNQyxNQUFNLENBQUNULEtBQUssR0FDdEJRO0FBRU4sSUFBSUcsb0JBQW9CLENBQUNDLE9BQVNBLEtBQUtDLFNBQVMsQ0FBQyxHQUFHRCxLQUFLRSxNQUFNLENBQUMsbUJBQW1CRjtBQUVuRixJQUFJRyxxQkFBcUIsQ0FBQ0MsT0FBT0osT0FBU0ksTUFBTUMsR0FBRyxDQUFDTixrQkFBa0JDO0FBRXRFLElBQUlNLGdCQUFnQixDQUFDQztJQUNqQixNQUFNQyxnQkFBZ0JELFdBQVdFLFdBQVcsSUFBSUYsV0FBV0UsV0FBVyxDQUFDQyxTQUFTO0lBQ2hGLE9BQVFsQixTQUFTZ0Isa0JBQWtCQSxjQUFjRyxjQUFjLENBQUM7QUFDcEU7QUFFQSxJQUFJQyxRQUFRLE1BQ3NCLElBQzlCLENBQW9CO0FBRXhCLFNBQVNJLFlBQVlDLElBQUk7SUFDckIsSUFBSUM7SUFDSixNQUFNeEIsVUFBVUQsTUFBTUMsT0FBTyxDQUFDdUI7SUFDOUIsTUFBTUUscUJBQXFCLE9BQU9DLGFBQWEsY0FBY0gsZ0JBQWdCRyxXQUFXO0lBQ3hGLElBQUlILGdCQUFnQjVCLE1BQU07UUFDdEI2QixPQUFPLElBQUk3QixLQUFLNEI7SUFDcEIsT0FDSyxJQUFJQSxnQkFBZ0JJLEtBQUs7UUFDMUJILE9BQU8sSUFBSUcsSUFBSUo7SUFDbkIsT0FDSyxJQUFJLENBQUVMLENBQUFBLFNBQVVLLENBQUFBLGdCQUFnQkssUUFBUUgsa0JBQWlCLENBQUMsS0FDMUR6QixDQUFBQSxXQUFXRixTQUFTeUIsS0FBSSxHQUFJO1FBQzdCQyxPQUFPeEIsVUFBVSxFQUFFLEdBQUcsQ0FBQztRQUN2QixJQUFJLENBQUNBLFdBQVcsQ0FBQ1ksY0FBY1csT0FBTztZQUNsQ0MsT0FBT0Q7UUFDWCxPQUNLO1lBQ0QsSUFBSyxNQUFNTSxPQUFPTixLQUFNO2dCQUNwQixJQUFJQSxLQUFLTixjQUFjLENBQUNZLE1BQU07b0JBQzFCTCxJQUFJLENBQUNLLElBQUksR0FBR1AsWUFBWUMsSUFBSSxDQUFDTSxJQUFJO2dCQUNyQztZQUNKO1FBQ0o7SUFDSixPQUNLO1FBQ0QsT0FBT047SUFDWDtJQUNBLE9BQU9DO0FBQ1g7QUFFQSxJQUFJTSxVQUFVLENBQUNwQyxTQUFVSyxNQUFNQyxPQUFPLENBQUNOLFVBQVNBLE9BQU1xQyxNQUFNLENBQUNDLFdBQVcsRUFBRTtBQUUxRSxJQUFJQyxjQUFjLENBQUNDLE1BQVFBLFFBQVFDO0FBRW5DLElBQUlDLE1BQU0sQ0FBQ0MsUUFBUUMsTUFBTUM7SUFDckIsSUFBSSxDQUFDRCxRQUFRLENBQUN4QyxTQUFTdUMsU0FBUztRQUM1QixPQUFPRTtJQUNYO0lBQ0EsTUFBTUMsU0FBU1YsUUFBUVEsS0FBS0csS0FBSyxDQUFDLGNBQWNDLE1BQU0sQ0FBQyxDQUFDRixRQUFRWCxNQUFRakMsa0JBQWtCNEMsVUFBVUEsU0FBU0EsTUFBTSxDQUFDWCxJQUFJLEVBQUVRO0lBQzFILE9BQU9KLFlBQVlPLFdBQVdBLFdBQVdILFNBQ25DSixZQUFZSSxNQUFNLENBQUNDLEtBQUssSUFDcEJDLGVBQ0FGLE1BQU0sQ0FBQ0MsS0FBSyxHQUNoQkU7QUFDVjtBQUVBLElBQUlHLFlBQVksQ0FBQ2pELFNBQVUsT0FBT0EsV0FBVTtBQUU1QyxJQUFJa0QsUUFBUSxDQUFDbEQsU0FBVSxRQUFRbUQsSUFBSSxDQUFDbkQ7QUFFcEMsSUFBSW9ELGVBQWUsQ0FBQ0MsUUFBVWpCLFFBQVFpQixNQUFNQyxPQUFPLENBQUMsYUFBYSxJQUFJUCxLQUFLLENBQUM7QUFFM0UsSUFBSVEsTUFBTSxDQUFDWixRQUFRQyxNQUFNNUM7SUFDckIsSUFBSXdELFFBQVEsQ0FBQztJQUNiLE1BQU1DLFdBQVdQLE1BQU1OLFFBQVE7UUFBQ0E7S0FBSyxHQUFHUSxhQUFhUjtJQUNyRCxNQUFNYyxTQUFTRCxTQUFTQyxNQUFNO0lBQzlCLE1BQU1DLFlBQVlELFNBQVM7SUFDM0IsTUFBTyxFQUFFRixRQUFRRSxPQUFRO1FBQ3JCLE1BQU12QixNQUFNc0IsUUFBUSxDQUFDRCxNQUFNO1FBQzNCLElBQUlJLFdBQVc1RDtRQUNmLElBQUl3RCxVQUFVRyxXQUFXO1lBQ3JCLE1BQU1FLFdBQVdsQixNQUFNLENBQUNSLElBQUk7WUFDNUJ5QixXQUNJeEQsU0FBU3lELGFBQWF4RCxNQUFNQyxPQUFPLENBQUN1RCxZQUM5QkEsV0FDQSxDQUFDQyxNQUFNLENBQUNMLFFBQVEsQ0FBQ0QsUUFBUSxFQUFFLElBQ3ZCLEVBQUUsR0FDRixDQUFDO1FBQ25CO1FBQ0EsSUFBSXJCLFFBQVEsZUFBZUEsUUFBUSxpQkFBaUJBLFFBQVEsYUFBYTtZQUNyRTtRQUNKO1FBQ0FRLE1BQU0sQ0FBQ1IsSUFBSSxHQUFHeUI7UUFDZGpCLFNBQVNBLE1BQU0sQ0FBQ1IsSUFBSTtJQUN4QjtBQUNKO0FBRUEsTUFBTTRCLFNBQVM7SUFDWEMsTUFBTTtJQUNOQyxXQUFXO0lBQ1hDLFFBQVE7QUFDWjtBQUNBLE1BQU1DLGtCQUFrQjtJQUNwQkMsUUFBUTtJQUNSQyxVQUFVO0lBQ1ZDLFVBQVU7SUFDVkMsV0FBVztJQUNYQyxLQUFLO0FBQ1Q7QUFDQSxNQUFNQyx5QkFBeUI7SUFDM0JDLEtBQUs7SUFDTEMsS0FBSztJQUNMQyxXQUFXO0lBQ1hDLFdBQVc7SUFDWEMsU0FBUztJQUNUQyxVQUFVO0lBQ1ZDLFVBQVU7QUFDZDtBQUVBLE1BQU1DLGdDQUFrQnRGLGdEQUE0QixDQUFDO0FBQ3JEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQTZCQyxHQUNELE1BQU13RixpQkFBaUIsSUFBTXhGLDZDQUF5QixDQUFDc0Y7QUFDdkQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBNkJDLEdBQ0QsTUFBTUksZUFBZSxDQUFDQztJQUNsQixNQUFNLEVBQUVDLFFBQVEsRUFBRSxHQUFHMUQsTUFBTSxHQUFHeUQ7SUFDOUIscUJBQVEzRixnREFBNEIsQ0FBQ3NGLGdCQUFnQlEsUUFBUSxFQUFFO1FBQUV6RixPQUFPNkI7SUFBSyxHQUFHMEQ7QUFDcEY7QUFFQSxJQUFJRyxvQkFBb0IsQ0FBQ0MsV0FBV0MsU0FBU0MscUJBQXFCQyxTQUFTLElBQUk7SUFDM0UsTUFBTWhELFNBQVM7UUFDWGlELGVBQWVILFFBQVFJLGNBQWM7SUFDekM7SUFDQSxJQUFLLE1BQU03RCxPQUFPd0QsVUFBVztRQUN6Qk0sT0FBT0MsY0FBYyxDQUFDcEQsUUFBUVgsS0FBSztZQUMvQk8sS0FBSztnQkFDRCxNQUFNeUQsT0FBT2hFO2dCQUNiLElBQUl5RCxRQUFRUSxlQUFlLENBQUNELEtBQUssS0FBS2hDLGdCQUFnQkssR0FBRyxFQUFFO29CQUN2RG9CLFFBQVFRLGVBQWUsQ0FBQ0QsS0FBSyxHQUFHLENBQUNMLFVBQVUzQixnQkFBZ0JLLEdBQUc7Z0JBQ2xFO2dCQUNBcUIsdUJBQXdCQSxDQUFBQSxtQkFBbUIsQ0FBQ00sS0FBSyxHQUFHLElBQUc7Z0JBQ3ZELE9BQU9SLFNBQVMsQ0FBQ1EsS0FBSztZQUMxQjtRQUNKO0lBQ0o7SUFDQSxPQUFPckQ7QUFDWDtBQUVBLE1BQU11RCw0QkFBNEIsTUFBa0IsR0FBYzNHLENBQXFCLEdBQUdBLDRDQUFlO0FBRXpHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQTZCQyxHQUNELFNBQVM4RyxhQUFhbEIsS0FBSztJQUN2QixNQUFNbUIsVUFBVXRCO0lBQ2hCLE1BQU0sRUFBRVMsVUFBVWEsUUFBUWIsT0FBTyxFQUFFYyxRQUFRLEVBQUU5RixJQUFJLEVBQUUrRixLQUFLLEVBQUUsR0FBR3JCLFNBQVMsQ0FBQztJQUN2RSxNQUFNLENBQUNLLFdBQVdpQixnQkFBZ0IsR0FBR2pILDJDQUF1QixDQUFDaUcsUUFBUWtCLFVBQVU7SUFDL0UsTUFBTUMsdUJBQXVCcEgseUNBQXFCLENBQUM7UUFDL0NzSCxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsYUFBYTtRQUNiQyxlQUFlO1FBQ2ZDLGtCQUFrQjtRQUNsQkMsY0FBYztRQUNkQyxTQUFTO1FBQ1RDLFFBQVE7SUFDWjtJQUNBbkIsMEJBQTBCLElBQU1ULFFBQVE2QixVQUFVLENBQUM7WUFDL0M3RyxNQUFNQTtZQUNOK0UsV0FBV29CLHFCQUFxQlcsT0FBTztZQUN2Q2Y7WUFDQWdCLFVBQVUsQ0FBQ2hDO2dCQUNQLENBQUNlLFlBQ0dFLGdCQUFnQjtvQkFDWixHQUFHaEIsUUFBUWtCLFVBQVU7b0JBQ3JCLEdBQUduQixTQUFTO2dCQUNoQjtZQUNSO1FBQ0osSUFBSTtRQUFDL0U7UUFBTThGO1FBQVVDO0tBQU07SUFDM0JoSCw0Q0FBd0IsQ0FBQztRQUNyQm9ILHFCQUFxQlcsT0FBTyxDQUFDSCxPQUFPLElBQUkzQixRQUFRZ0MsU0FBUyxDQUFDO0lBQzlELEdBQUc7UUFBQ2hDO0tBQVE7SUFDWixPQUFPakcsMENBQXNCLENBQUMsSUFBTStGLGtCQUFrQkMsV0FBV0MsU0FBU21CLHFCQUFxQlcsT0FBTyxFQUFFLFFBQVE7UUFBQy9CO1FBQVdDO0tBQVE7QUFDeEk7QUFFQSxJQUFJa0MsV0FBVyxDQUFDOUgsU0FBVSxPQUFPQSxXQUFVO0FBRTNDLElBQUkrSCxzQkFBc0IsQ0FBQy9HLE9BQU9nSCxRQUFRQyxZQUFZQyxVQUFVckY7SUFDNUQsSUFBSWlGLFNBQVM5RyxRQUFRO1FBQ2pCa0gsWUFBWUYsT0FBT0csS0FBSyxDQUFDQyxHQUFHLENBQUNwSDtRQUM3QixPQUFPMEIsSUFBSXVGLFlBQVlqSCxPQUFPNkI7SUFDbEM7SUFDQSxJQUFJeEMsTUFBTUMsT0FBTyxDQUFDVSxRQUFRO1FBQ3RCLE9BQU9BLE1BQU1xSCxHQUFHLENBQUMsQ0FBQ0MsWUFBZUosQ0FBQUEsWUFBWUYsT0FBT0csS0FBSyxDQUFDQyxHQUFHLENBQUNFLFlBQVk1RixJQUFJdUYsWUFBWUssVUFBUztJQUN2RztJQUNBSixZQUFhRixDQUFBQSxPQUFPTyxRQUFRLEdBQUcsSUFBRztJQUNsQyxPQUFPTjtBQUNYO0FBRUE7Ozs7Ozs7Ozs7Ozs7OztDQWVDLEdBQ0QsU0FBU08sU0FBU2xELEtBQUs7SUFDbkIsTUFBTW1CLFVBQVV0QjtJQUNoQixNQUFNLEVBQUVTLFVBQVVhLFFBQVFiLE9BQU8sRUFBRWhGLElBQUksRUFBRWlDLFlBQVksRUFBRTZELFFBQVEsRUFBRUMsS0FBSyxFQUFHLEdBQUdyQixTQUFTLENBQUM7SUFDdEYsTUFBTW1ELGdCQUFnQjlJLHlDQUFxQixDQUFDa0Q7SUFDNUMsTUFBTSxDQUFDN0MsUUFBTzBJLFlBQVksR0FBRy9JLDJDQUF1QixDQUFDaUcsUUFBUStDLFNBQVMsQ0FBQy9ILE1BQU02SCxjQUFjZixPQUFPO0lBQ2xHckIsMEJBQTBCLElBQU1ULFFBQVE2QixVQUFVLENBQUM7WUFDL0M3RyxNQUFNQTtZQUNOK0UsV0FBVztnQkFDUGlELFFBQVE7WUFDWjtZQUNBakM7WUFDQWdCLFVBQVUsQ0FBQ2hDLFlBQWMsQ0FBQ2UsWUFDdEJnQyxZQUFZWCxvQkFBb0JuSCxNQUFNZ0YsUUFBUW9DLE1BQU0sRUFBRXJDLFVBQVVpRCxNQUFNLElBQUloRCxRQUFRaUQsV0FBVyxFQUFFLE9BQU9KLGNBQWNmLE9BQU87UUFDbkksSUFBSTtRQUFDOUc7UUFBTWdGO1FBQVNjO1FBQVVDO0tBQU07SUFDcENoSCw0Q0FBd0IsQ0FBQyxJQUFNaUcsUUFBUWtELGdCQUFnQjtJQUN2RCxPQUFPOUk7QUFDWDtBQUVBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQXVCQyxHQUNELFNBQVMrSSxjQUFjekQsS0FBSztJQUN4QixNQUFNbUIsVUFBVXRCO0lBQ2hCLE1BQU0sRUFBRXZFLElBQUksRUFBRThGLFFBQVEsRUFBRWQsVUFBVWEsUUFBUWIsT0FBTyxFQUFFb0QsZ0JBQWdCLEVBQUUsR0FBRzFEO0lBQ3hFLE1BQU0yRCxlQUFlbEksbUJBQW1CNkUsUUFBUW9DLE1BQU0sQ0FBQ2tCLEtBQUssRUFBRXRJO0lBQzlELE1BQU1aLFNBQVF3SSxTQUFTO1FBQ25CNUM7UUFDQWhGO1FBQ0FpQyxjQUFjSCxJQUFJa0QsUUFBUWlELFdBQVcsRUFBRWpJLE1BQU04QixJQUFJa0QsUUFBUUksY0FBYyxFQUFFcEYsTUFBTTBFLE1BQU16QyxZQUFZO1FBQ2pHOEQsT0FBTztJQUNYO0lBQ0EsTUFBTWhCLFlBQVlhLGFBQWE7UUFDM0JaO1FBQ0FoRjtRQUNBK0YsT0FBTztJQUNYO0lBQ0EsTUFBTXdDLFNBQVN4Six5Q0FBcUIsQ0FBQzJGO0lBQ3JDLE1BQU04RCxpQkFBaUJ6Six5Q0FBcUIsQ0FBQ2lHLFFBQVF5RCxRQUFRLENBQUN6SSxNQUFNO1FBQ2hFLEdBQUcwRSxNQUFNZ0UsS0FBSztRQUNkdEosT0FBQUE7UUFDQSxHQUFJaUQsVUFBVXFDLE1BQU1vQixRQUFRLElBQUk7WUFBRUEsVUFBVXBCLE1BQU1vQixRQUFRO1FBQUMsSUFBSSxDQUFDLENBQUM7SUFDckU7SUFDQSxNQUFNNkMsYUFBYTVKLDBDQUFzQixDQUFDLElBQU1zRyxPQUFPdUQsZ0JBQWdCLENBQUMsQ0FBQyxHQUFHO1lBQ3hFQyxTQUFTO2dCQUNMQyxZQUFZO2dCQUNaaEgsS0FBSyxJQUFNLENBQUMsQ0FBQ0EsSUFBSWlELFVBQVU2QixNQUFNLEVBQUU1RztZQUN2QztZQUNBcUcsU0FBUztnQkFDTHlDLFlBQVk7Z0JBQ1poSCxLQUFLLElBQU0sQ0FBQyxDQUFDQSxJQUFJaUQsVUFBVXdCLFdBQVcsRUFBRXZHO1lBQzVDO1lBQ0ErSSxXQUFXO2dCQUNQRCxZQUFZO2dCQUNaaEgsS0FBSyxJQUFNLENBQUMsQ0FBQ0EsSUFBSWlELFVBQVV5QixhQUFhLEVBQUV4RztZQUM5QztZQUNBMEcsY0FBYztnQkFDVm9DLFlBQVk7Z0JBQ1poSCxLQUFLLElBQU0sQ0FBQyxDQUFDQSxJQUFJaUQsVUFBVTBCLGdCQUFnQixFQUFFekc7WUFDakQ7WUFDQWdKLE9BQU87Z0JBQ0hGLFlBQVk7Z0JBQ1poSCxLQUFLLElBQU1BLElBQUlpRCxVQUFVNkIsTUFBTSxFQUFFNUc7WUFDckM7UUFDSixJQUFJO1FBQUMrRTtRQUFXL0U7S0FBSztJQUNyQixNQUFNeUQsV0FBVzFFLDhDQUEwQixDQUFDLENBQUNhLFFBQVU0SSxlQUFlMUIsT0FBTyxDQUFDckQsUUFBUSxDQUFDO1lBQ25GNUQsUUFBUTtnQkFDSlQsT0FBT08sY0FBY0M7Z0JBQ3JCSSxNQUFNQTtZQUNWO1lBQ0FkLE1BQU1pRSxPQUFPRyxNQUFNO1FBQ3ZCLElBQUk7UUFBQ3REO0tBQUs7SUFDVixNQUFNd0QsU0FBU3pFLDhDQUEwQixDQUFDLElBQU15SixlQUFlMUIsT0FBTyxDQUFDdEQsTUFBTSxDQUFDO1lBQzFFM0QsUUFBUTtnQkFDSlQsT0FBTzBDLElBQUlrRCxRQUFRaUQsV0FBVyxFQUFFakk7Z0JBQ2hDQSxNQUFNQTtZQUNWO1lBQ0FkLE1BQU1pRSxPQUFPQyxJQUFJO1FBQ3JCLElBQUk7UUFBQ3BEO1FBQU1nRixRQUFRaUQsV0FBVztLQUFDO0lBQy9CLE1BQU1pQixNQUFNbkssOENBQTBCLENBQUMsQ0FBQ29LO1FBQ3BDLE1BQU1DLFFBQVF0SCxJQUFJa0QsUUFBUXFFLE9BQU8sRUFBRXJKO1FBQ25DLElBQUlvSixTQUFTRCxLQUFLO1lBQ2RDLE1BQU1FLEVBQUUsQ0FBQ0osR0FBRyxHQUFHO2dCQUNYSyxPQUFPLElBQU1KLElBQUlJLEtBQUs7Z0JBQ3RCQyxRQUFRLElBQU1MLElBQUlLLE1BQU07Z0JBQ3hCQyxtQkFBbUIsQ0FBQ0MsVUFBWVAsSUFBSU0saUJBQWlCLENBQUNDO2dCQUN0REMsZ0JBQWdCLElBQU1SLElBQUlRLGNBQWM7WUFDNUM7UUFDSjtJQUNKLEdBQUc7UUFBQzNFLFFBQVFxRSxPQUFPO1FBQUVySjtLQUFLO0lBQzFCLE1BQU1vSixRQUFRckssMENBQXNCLENBQUMsSUFBTztZQUN4Q2lCO1lBQ0FaLE9BQUFBO1lBQ0EsR0FBSWlELFVBQVV5RCxhQUFhZixVQUFVZSxRQUFRLEdBQ3ZDO2dCQUFFQSxVQUFVZixVQUFVZSxRQUFRLElBQUlBO1lBQVMsSUFDM0MsQ0FBQyxDQUFDO1lBQ1JyQztZQUNBRDtZQUNBMEY7UUFDSixJQUFJO1FBQUNsSjtRQUFNOEY7UUFBVWYsVUFBVWUsUUFBUTtRQUFFckM7UUFBVUQ7UUFBUTBGO1FBQUs5SjtLQUFNO0lBQ3RFTCw0Q0FBd0IsQ0FBQztRQUNyQixNQUFNNksseUJBQXlCNUUsUUFBUTZFLFFBQVEsQ0FBQ3pCLGdCQUFnQixJQUFJQTtRQUNwRXBELFFBQVF5RCxRQUFRLENBQUN6SSxNQUFNO1lBQ25CLEdBQUd1SSxPQUFPekIsT0FBTyxDQUFDNEIsS0FBSztZQUN2QixHQUFJckcsVUFBVWtHLE9BQU96QixPQUFPLENBQUNoQixRQUFRLElBQy9CO2dCQUFFQSxVQUFVeUMsT0FBT3pCLE9BQU8sQ0FBQ2hCLFFBQVE7WUFBQyxJQUNwQyxDQUFDLENBQUM7UUFDWjtRQUNBLE1BQU1nRSxnQkFBZ0IsQ0FBQzlKLE1BQU1aO1lBQ3pCLE1BQU1nSyxRQUFRdEgsSUFBSWtELFFBQVFxRSxPQUFPLEVBQUVySjtZQUNuQyxJQUFJb0osU0FBU0EsTUFBTUUsRUFBRSxFQUFFO2dCQUNuQkYsTUFBTUUsRUFBRSxDQUFDUyxLQUFLLEdBQUczSztZQUNyQjtRQUNKO1FBQ0EwSyxjQUFjOUosTUFBTTtRQUNwQixJQUFJNEosd0JBQXdCO1lBQ3hCLE1BQU14SyxTQUFRNEIsWUFBWWMsSUFBSWtELFFBQVE2RSxRQUFRLENBQUMxRSxhQUFhLEVBQUVuRjtZQUM5RDJDLElBQUlxQyxRQUFRSSxjQUFjLEVBQUVwRixNQUFNWjtZQUNsQyxJQUFJdUMsWUFBWUcsSUFBSWtELFFBQVFpRCxXQUFXLEVBQUVqSSxRQUFRO2dCQUM3QzJDLElBQUlxQyxRQUFRaUQsV0FBVyxFQUFFakksTUFBTVo7WUFDbkM7UUFDSjtRQUNBLENBQUNpSixnQkFBZ0JyRCxRQUFReUQsUUFBUSxDQUFDekk7UUFDbEMsT0FBTztZQUNGcUksQ0FBQUEsZUFDS3VCLDBCQUEwQixDQUFDNUUsUUFBUWdGLE1BQU0sQ0FBQ0MsTUFBTSxHQUNoREwsc0JBQXFCLElBQ3JCNUUsUUFBUWtGLFVBQVUsQ0FBQ2xLLFFBQ25COEosY0FBYzlKLE1BQU07UUFDOUI7SUFDSixHQUFHO1FBQUNBO1FBQU1nRjtRQUFTcUQ7UUFBY0Q7S0FBaUI7SUFDbERySiw0Q0FBd0IsQ0FBQztRQUNyQmlHLFFBQVFtRixpQkFBaUIsQ0FBQztZQUN0QnJFO1lBQ0E5RjtRQUNKO0lBQ0osR0FBRztRQUFDOEY7UUFBVTlGO1FBQU1nRjtLQUFRO0lBQzVCLE9BQU9qRywwQ0FBc0IsQ0FBQyxJQUFPO1lBQ2pDcUs7WUFDQXJFO1lBQ0E0RDtRQUNKLElBQUk7UUFBQ1M7UUFBT3JFO1FBQVc0RDtLQUFXO0FBQ3RDO0FBRUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBeUNDLEdBQ0QsTUFBTXlCLGFBQWEsQ0FBQzFGLFFBQVVBLE1BQU0yRixNQUFNLENBQUNsQyxjQUFjekQ7QUFFekQsTUFBTTRGLFVBQVUsQ0FBQ0M7SUFDYixNQUFNQyxTQUFTLENBQUM7SUFDaEIsS0FBSyxNQUFNakosT0FBTzhELE9BQU9vRixJQUFJLENBQUNGLEtBQU07UUFDaEMsSUFBSWhMLGFBQWFnTCxHQUFHLENBQUNoSixJQUFJLEtBQUtnSixHQUFHLENBQUNoSixJQUFJLEtBQUssTUFBTTtZQUM3QyxNQUFNbUosU0FBU0osUUFBUUMsR0FBRyxDQUFDaEosSUFBSTtZQUMvQixLQUFLLE1BQU1vSixhQUFhdEYsT0FBT29GLElBQUksQ0FBQ0MsUUFBUztnQkFDekNGLE1BQU0sQ0FBQyxDQUFDLEVBQUVqSixJQUFJLENBQUMsRUFBRW9KLFVBQVUsQ0FBQyxDQUFDLEdBQUdELE1BQU0sQ0FBQ0MsVUFBVTtZQUNyRDtRQUNKLE9BQ0s7WUFDREgsTUFBTSxDQUFDakosSUFBSSxHQUFHZ0osR0FBRyxDQUFDaEosSUFBSTtRQUMxQjtJQUNKO0lBQ0EsT0FBT2lKO0FBQ1g7QUFFQSxNQUFNSSxlQUFlO0FBQ3JCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Q0FxQkMsR0FDRCxTQUFTQyxLQUFLbkcsS0FBSztJQUNmLE1BQU1tQixVQUFVdEI7SUFDaEIsTUFBTSxDQUFDdUcsU0FBU0MsV0FBVyxHQUFHaE0sMkNBQXVCLENBQUM7SUFDdEQsTUFBTSxFQUFFaUcsVUFBVWEsUUFBUWIsT0FBTyxFQUFFdEIsUUFBUSxFQUFFaUIsUUFBUSxFQUFFc0YsTUFBTSxFQUFFZSxTQUFTSixZQUFZLEVBQUVLLE9BQU8sRUFBRUMsT0FBTyxFQUFFQyxPQUFPLEVBQUVkLE1BQU0sRUFBRWUsU0FBUyxFQUFFQyxjQUFjLEVBQUUsR0FBR0MsTUFBTSxHQUFHNUc7SUFDaEssTUFBTTZHLFNBQVMsT0FBTzNMO1FBQ2xCLElBQUk0TCxXQUFXO1FBQ2YsSUFBSXRNLE9BQU87UUFDWCxNQUFNOEYsUUFBUXlHLFlBQVksQ0FBQyxPQUFPeEs7WUFDOUIsTUFBTXlLLFdBQVcsSUFBSUM7WUFDckIsSUFBSUMsZUFBZTtZQUNuQixJQUFJO2dCQUNBQSxlQUFlQyxLQUFLQyxTQUFTLENBQUM3SztZQUNsQyxFQUNBLE9BQU84SyxJQUFJLENBQUU7WUFDYixNQUFNQyxvQkFBb0IxQixRQUFRdEYsUUFBUWlELFdBQVc7WUFDckQsSUFBSyxNQUFNMUcsT0FBT3lLLGtCQUFtQjtnQkFDakNOLFNBQVNPLE1BQU0sQ0FBQzFLLEtBQUt5SyxpQkFBaUIsQ0FBQ3pLLElBQUk7WUFDL0M7WUFDQSxJQUFJbUMsVUFBVTtnQkFDVixNQUFNQSxTQUFTO29CQUNYekM7b0JBQ0FyQjtvQkFDQW9MO29CQUNBVTtvQkFDQUU7Z0JBQ0o7WUFDSjtZQUNBLElBQUkzQixRQUFRO2dCQUNSLElBQUk7b0JBQ0EsTUFBTWlDLGdDQUFnQzt3QkFDbENqQixXQUFXQSxPQUFPLENBQUMsZUFBZTt3QkFDbENDO3FCQUNILENBQUNpQixJQUFJLENBQUMsQ0FBQy9NLFNBQVVBLFVBQVNBLE9BQU1nTixRQUFRLENBQUM7b0JBQzFDLE1BQU1DLFdBQVcsTUFBTUMsTUFBTUMsT0FBT3RDLFNBQVM7d0JBQ3pDZTt3QkFDQUMsU0FBUzs0QkFDTCxHQUFHQSxPQUFPOzRCQUNWLEdBQUlDLFVBQVU7Z0NBQUUsZ0JBQWdCQTs0QkFBUSxJQUFJLENBQUMsQ0FBQzt3QkFDbEQ7d0JBQ0FzQixNQUFNTixnQ0FBZ0NOLGVBQWVGO29CQUN6RDtvQkFDQSxJQUFJVyxZQUNDaEIsQ0FBQUEsaUJBQ0ssQ0FBQ0EsZUFBZWdCLFNBQVNJLE1BQU0sSUFDL0JKLFNBQVNJLE1BQU0sR0FBRyxPQUFPSixTQUFTSSxNQUFNLElBQUksR0FBRSxHQUFJO3dCQUN4RGpCLFdBQVc7d0JBQ1hMLFdBQVdBLFFBQVE7NEJBQUVrQjt3QkFBUzt3QkFDOUJuTixPQUFPcU4sT0FBT0YsU0FBU0ksTUFBTTtvQkFDakMsT0FDSzt3QkFDRHJCLGFBQWFBLFVBQVU7NEJBQUVpQjt3QkFBUztvQkFDdEM7Z0JBQ0osRUFDQSxPQUFPckQsT0FBTztvQkFDVndDLFdBQVc7b0JBQ1hMLFdBQVdBLFFBQVE7d0JBQUVuQztvQkFBTTtnQkFDL0I7WUFDSjtRQUNKLEdBQUdwSjtRQUNILElBQUk0TCxZQUFZOUcsTUFBTU0sT0FBTyxFQUFFO1lBQzNCTixNQUFNTSxPQUFPLENBQUMwSCxTQUFTLENBQUNDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUMvQkMsb0JBQW9CO1lBQ3hCO1lBQ0FuSSxNQUFNTSxPQUFPLENBQUM4SCxRQUFRLENBQUMsZUFBZTtnQkFDbEM1TjtZQUNKO1FBQ0o7SUFDSjtJQUNBSCw0Q0FBd0IsQ0FBQztRQUNyQmdNLFdBQVc7SUFDZixHQUFHLEVBQUU7SUFDTCxPQUFPVix1QkFBVXRMLGdEQUE0QixDQUFDQSwyQ0FBdUIsRUFBRSxNQUFNc0wsT0FBTztRQUNoRmtCO0lBQ0osb0JBQVF4TSxnREFBNEIsQ0FBQyxRQUFRO1FBQUVpTyxZQUFZbEM7UUFBU2IsUUFBUUE7UUFBUWUsUUFBUUE7UUFBUUUsU0FBU0E7UUFBU3hILFVBQVU2SDtRQUFRLEdBQUdELElBQUk7SUFBQyxHQUFHM0c7QUFDdko7QUFFQSxJQUFJc0ksZUFBZSxDQUFDak4sTUFBTWtOLDBCQUEwQnRHLFFBQVExSCxNQUFNd0ssVUFBWXdELDJCQUN4RTtRQUNFLEdBQUd0RyxNQUFNLENBQUM1RyxLQUFLO1FBQ2ZtTixPQUFPO1lBQ0gsR0FBSXZHLE1BQU0sQ0FBQzVHLEtBQUssSUFBSTRHLE1BQU0sQ0FBQzVHLEtBQUssQ0FBQ21OLEtBQUssR0FBR3ZHLE1BQU0sQ0FBQzVHLEtBQUssQ0FBQ21OLEtBQUssR0FBRyxDQUFDLENBQUM7WUFDaEUsQ0FBQ2pPLEtBQUssRUFBRXdLLFdBQVc7UUFDdkI7SUFDSixJQUNFLENBQUM7QUFFUCxJQUFJMEQsd0JBQXdCLENBQUNoTyxTQUFXSyxNQUFNQyxPQUFPLENBQUNOLFVBQVNBLFNBQVE7UUFBQ0E7S0FBTTtBQUU5RSxJQUFJaU8sZ0JBQWdCO0lBQ2hCLElBQUlDLGFBQWEsRUFBRTtJQUNuQixNQUFNVixPQUFPLENBQUN4TjtRQUNWLEtBQUssTUFBTW1PLFlBQVlELFdBQVk7WUFDL0JDLFNBQVNYLElBQUksSUFBSVcsU0FBU1gsSUFBSSxDQUFDeE47UUFDbkM7SUFDSjtJQUNBLE1BQU1vTyxZQUFZLENBQUNEO1FBQ2ZELFdBQVdHLElBQUksQ0FBQ0Y7UUFDaEIsT0FBTztZQUNIRyxhQUFhO2dCQUNUSixhQUFhQSxXQUFXN0wsTUFBTSxDQUFDLENBQUNrTSxJQUFNQSxNQUFNSjtZQUNoRDtRQUNKO0lBQ0o7SUFDQSxNQUFNRyxjQUFjO1FBQ2hCSixhQUFhLEVBQUU7SUFDbkI7SUFDQSxPQUFPO1FBQ0gsSUFBSU0sYUFBWTtZQUNaLE9BQU9OO1FBQ1g7UUFDQVY7UUFDQVk7UUFDQUU7SUFDSjtBQUNKO0FBRUEsSUFBSUcsY0FBYyxDQUFDek8sU0FBVUUsa0JBQWtCRixXQUFVLENBQUNHLGFBQWFIO0FBRXZFLFNBQVMwTyxVQUFVQyxPQUFPLEVBQUVDLE9BQU87SUFDL0IsSUFBSUgsWUFBWUUsWUFBWUYsWUFBWUcsVUFBVTtRQUM5QyxPQUFPRCxZQUFZQztJQUN2QjtJQUNBLElBQUk3TyxhQUFhNE8sWUFBWTVPLGFBQWE2TyxVQUFVO1FBQ2hELE9BQU9ELFFBQVFFLE9BQU8sT0FBT0QsUUFBUUMsT0FBTztJQUNoRDtJQUNBLE1BQU1DLFFBQVE3SSxPQUFPb0YsSUFBSSxDQUFDc0Q7SUFDMUIsTUFBTUksUUFBUTlJLE9BQU9vRixJQUFJLENBQUN1RDtJQUMxQixJQUFJRSxNQUFNcEwsTUFBTSxLQUFLcUwsTUFBTXJMLE1BQU0sRUFBRTtRQUMvQixPQUFPO0lBQ1g7SUFDQSxLQUFLLE1BQU12QixPQUFPMk0sTUFBTztRQUNyQixNQUFNRSxPQUFPTCxPQUFPLENBQUN4TSxJQUFJO1FBQ3pCLElBQUksQ0FBQzRNLE1BQU0vQixRQUFRLENBQUM3SyxNQUFNO1lBQ3RCLE9BQU87UUFDWDtRQUNBLElBQUlBLFFBQVEsT0FBTztZQUNmLE1BQU04TSxPQUFPTCxPQUFPLENBQUN6TSxJQUFJO1lBQ3pCLElBQUksYUFBYzZNLFNBQVNqUCxhQUFha1AsU0FDbkM3TyxTQUFTNE8sU0FBUzVPLFNBQVM2TyxTQUMzQjVPLE1BQU1DLE9BQU8sQ0FBQzBPLFNBQVMzTyxNQUFNQyxPQUFPLENBQUMyTyxRQUNwQyxDQUFDUCxVQUFVTSxNQUFNQyxRQUNqQkQsU0FBU0MsTUFBTTtnQkFDakIsT0FBTztZQUNYO1FBQ0o7SUFDSjtJQUNBLE9BQU87QUFDWDtBQUVBLElBQUlDLGdCQUFnQixDQUFDbFAsU0FBVUksU0FBU0osV0FBVSxDQUFDaUcsT0FBT29GLElBQUksQ0FBQ3JMLFFBQU8wRCxNQUFNO0FBRTVFLElBQUl5TCxjQUFjLENBQUN0UCxVQUFZQSxRQUFRQyxJQUFJLEtBQUs7QUFFaEQsSUFBSXNQLGFBQWEsQ0FBQ3BQLFNBQVUsT0FBT0EsV0FBVTtBQUU3QyxJQUFJcVAsZ0JBQWdCLENBQUNyUDtJQUNqQixJQUFJLENBQUN3QixPQUFPO1FBQ1IsT0FBTztJQUNYO0lBQ0EsTUFBTThOLFFBQVF0UCxTQUFRQSxPQUFNdVAsYUFBYSxHQUFHO0lBQzVDLE9BQVF2UCxrQkFDSHNQLENBQUFBLFNBQVNBLE1BQU1FLFdBQVcsR0FBR0YsTUFBTUUsV0FBVyxDQUFDOU4sV0FBVyxHQUFHQSxXQUFVO0FBQ2hGO0FBRUEsSUFBSStOLG1CQUFtQixDQUFDNVAsVUFBWUEsUUFBUUMsSUFBSSxLQUFLLENBQUMsZUFBZSxDQUFDO0FBRXRFLElBQUk0UCxlQUFlLENBQUM3UCxVQUFZQSxRQUFRQyxJQUFJLEtBQUs7QUFFakQsSUFBSTZQLG9CQUFvQixDQUFDN0YsTUFBUTRGLGFBQWE1RixRQUFRbEssZ0JBQWdCa0s7QUFFdEUsSUFBSThGLE9BQU8sQ0FBQzlGLE1BQVF1RixjQUFjdkYsUUFBUUEsSUFBSStGLFdBQVc7QUFFekQsU0FBU0MsUUFBUW5OLE1BQU0sRUFBRW9OLFVBQVU7SUFDL0IsTUFBTXJNLFNBQVNxTSxXQUFXQyxLQUFLLENBQUMsR0FBRyxDQUFDLEdBQUd0TSxNQUFNO0lBQzdDLElBQUlGLFFBQVE7SUFDWixNQUFPQSxRQUFRRSxPQUFRO1FBQ25CZixTQUFTSixZQUFZSSxVQUFVYSxVQUFVYixNQUFNLENBQUNvTixVQUFVLENBQUN2TSxRQUFRLENBQUM7SUFDeEU7SUFDQSxPQUFPYjtBQUNYO0FBQ0EsU0FBU3NOLGFBQWE5RSxHQUFHO0lBQ3JCLElBQUssTUFBTWhKLE9BQU9nSixJQUFLO1FBQ25CLElBQUlBLElBQUk1SixjQUFjLENBQUNZLFFBQVEsQ0FBQ0ksWUFBWTRJLEdBQUcsQ0FBQ2hKLElBQUksR0FBRztZQUNuRCxPQUFPO1FBQ1g7SUFDSjtJQUNBLE9BQU87QUFDWDtBQUNBLFNBQVMrTixNQUFNdk4sTUFBTSxFQUFFQyxJQUFJO0lBQ3ZCLE1BQU11TixRQUFROVAsTUFBTUMsT0FBTyxDQUFDc0MsUUFDdEJBLE9BQ0FNLE1BQU1OLFFBQ0Y7UUFBQ0E7S0FBSyxHQUNOUSxhQUFhUjtJQUN2QixNQUFNd04sY0FBY0QsTUFBTXpNLE1BQU0sS0FBSyxJQUFJZixTQUFTbU4sUUFBUW5OLFFBQVF3TjtJQUNsRSxNQUFNM00sUUFBUTJNLE1BQU16TSxNQUFNLEdBQUc7SUFDN0IsTUFBTXZCLE1BQU1nTyxLQUFLLENBQUMzTSxNQUFNO0lBQ3hCLElBQUk0TSxhQUFhO1FBQ2IsT0FBT0EsV0FBVyxDQUFDak8sSUFBSTtJQUMzQjtJQUNBLElBQUlxQixVQUFVLEtBQ1QsVUFBVTRNLGdCQUFnQmxCLGNBQWNrQixnQkFDcEMvUCxNQUFNQyxPQUFPLENBQUM4UCxnQkFBZ0JILGFBQWFHLFlBQVksR0FBSTtRQUNoRUYsTUFBTXZOLFFBQVF3TixNQUFNSCxLQUFLLENBQUMsR0FBRyxDQUFDO0lBQ2xDO0lBQ0EsT0FBT3JOO0FBQ1g7QUFFQSxJQUFJME4sb0JBQW9CLENBQUN4TztJQUNyQixJQUFLLE1BQU1NLE9BQU9OLEtBQU07UUFDcEIsSUFBSXVOLFdBQVd2TixJQUFJLENBQUNNLElBQUksR0FBRztZQUN2QixPQUFPO1FBQ1g7SUFDSjtJQUNBLE9BQU87QUFDWDtBQUVBLFNBQVNtTyxnQkFBZ0J6TyxJQUFJLEVBQUUwTyxTQUFTLENBQUMsQ0FBQztJQUN0QyxNQUFNQyxvQkFBb0JuUSxNQUFNQyxPQUFPLENBQUN1QjtJQUN4QyxJQUFJekIsU0FBU3lCLFNBQVMyTyxtQkFBbUI7UUFDckMsSUFBSyxNQUFNck8sT0FBT04sS0FBTTtZQUNwQixJQUFJeEIsTUFBTUMsT0FBTyxDQUFDdUIsSUFBSSxDQUFDTSxJQUFJLEtBQ3RCL0IsU0FBU3lCLElBQUksQ0FBQ00sSUFBSSxLQUFLLENBQUNrTyxrQkFBa0J4TyxJQUFJLENBQUNNLElBQUksR0FBSTtnQkFDeERvTyxNQUFNLENBQUNwTyxJQUFJLEdBQUc5QixNQUFNQyxPQUFPLENBQUN1QixJQUFJLENBQUNNLElBQUksSUFBSSxFQUFFLEdBQUcsQ0FBQztnQkFDL0NtTyxnQkFBZ0J6TyxJQUFJLENBQUNNLElBQUksRUFBRW9PLE1BQU0sQ0FBQ3BPLElBQUk7WUFDMUMsT0FDSyxJQUFJLENBQUNqQyxrQkFBa0IyQixJQUFJLENBQUNNLElBQUksR0FBRztnQkFDcENvTyxNQUFNLENBQUNwTyxJQUFJLEdBQUc7WUFDbEI7UUFDSjtJQUNKO0lBQ0EsT0FBT29PO0FBQ1g7QUFDQSxTQUFTRSxnQ0FBZ0M1TyxJQUFJLEVBQUVvRyxVQUFVLEVBQUV5SSxxQkFBcUI7SUFDNUUsTUFBTUYsb0JBQW9CblEsTUFBTUMsT0FBTyxDQUFDdUI7SUFDeEMsSUFBSXpCLFNBQVN5QixTQUFTMk8sbUJBQW1CO1FBQ3JDLElBQUssTUFBTXJPLE9BQU9OLEtBQU07WUFDcEIsSUFBSXhCLE1BQU1DLE9BQU8sQ0FBQ3VCLElBQUksQ0FBQ00sSUFBSSxLQUN0Qi9CLFNBQVN5QixJQUFJLENBQUNNLElBQUksS0FBSyxDQUFDa08sa0JBQWtCeE8sSUFBSSxDQUFDTSxJQUFJLEdBQUk7Z0JBQ3hELElBQUlJLFlBQVkwRixlQUNad0csWUFBWWlDLHFCQUFxQixDQUFDdk8sSUFBSSxHQUFHO29CQUN6Q3VPLHFCQUFxQixDQUFDdk8sSUFBSSxHQUFHOUIsTUFBTUMsT0FBTyxDQUFDdUIsSUFBSSxDQUFDTSxJQUFJLElBQzlDbU8sZ0JBQWdCek8sSUFBSSxDQUFDTSxJQUFJLEVBQUUsRUFBRSxJQUM3Qjt3QkFBRSxHQUFHbU8sZ0JBQWdCek8sSUFBSSxDQUFDTSxJQUFJLENBQUM7b0JBQUM7Z0JBQzFDLE9BQ0s7b0JBQ0RzTyxnQ0FBZ0M1TyxJQUFJLENBQUNNLElBQUksRUFBRWpDLGtCQUFrQitILGNBQWMsQ0FBQyxJQUFJQSxVQUFVLENBQUM5RixJQUFJLEVBQUV1TyxxQkFBcUIsQ0FBQ3ZPLElBQUk7Z0JBQy9IO1lBQ0osT0FDSztnQkFDRHVPLHFCQUFxQixDQUFDdk8sSUFBSSxHQUFHLENBQUN1TSxVQUFVN00sSUFBSSxDQUFDTSxJQUFJLEVBQUU4RixVQUFVLENBQUM5RixJQUFJO1lBQ3RFO1FBQ0o7SUFDSjtJQUNBLE9BQU91TztBQUNYO0FBQ0EsSUFBSUMsaUJBQWlCLENBQUM1SyxlQUFla0MsYUFBZXdJLGdDQUFnQzFLLGVBQWVrQyxZQUFZcUksZ0JBQWdCckk7QUFFL0gsTUFBTTJJLGdCQUFnQjtJQUNsQjVRLE9BQU87SUFDUHVILFNBQVM7QUFDYjtBQUNBLE1BQU1zSixjQUFjO0lBQUU3USxPQUFPO0lBQU11SCxTQUFTO0FBQUs7QUFDakQsSUFBSXVKLG1CQUFtQixDQUFDQztJQUNwQixJQUFJMVEsTUFBTUMsT0FBTyxDQUFDeVEsVUFBVTtRQUN4QixJQUFJQSxRQUFRck4sTUFBTSxHQUFHLEdBQUc7WUFDcEIsTUFBTWtGLFNBQVNtSSxRQUNWMU8sTUFBTSxDQUFDLENBQUMyTyxTQUFXQSxVQUFVQSxPQUFPdFEsT0FBTyxJQUFJLENBQUNzUSxPQUFPdEssUUFBUSxFQUMvRDJCLEdBQUcsQ0FBQyxDQUFDMkksU0FBV0EsT0FBT2hSLEtBQUs7WUFDakMsT0FBTztnQkFBRUEsT0FBTzRJO2dCQUFRckIsU0FBUyxDQUFDLENBQUNxQixPQUFPbEYsTUFBTTtZQUFDO1FBQ3JEO1FBQ0EsT0FBT3FOLE9BQU8sQ0FBQyxFQUFFLENBQUNyUSxPQUFPLElBQUksQ0FBQ3FRLE9BQU8sQ0FBQyxFQUFFLENBQUNySyxRQUFRLEdBRXpDcUssT0FBTyxDQUFDLEVBQUUsQ0FBQ0UsVUFBVSxJQUFJLENBQUMxTyxZQUFZd08sT0FBTyxDQUFDLEVBQUUsQ0FBQ0UsVUFBVSxDQUFDalIsS0FBSyxJQUMzRHVDLFlBQVl3TyxPQUFPLENBQUMsRUFBRSxDQUFDL1EsS0FBSyxLQUFLK1EsT0FBTyxDQUFDLEVBQUUsQ0FBQy9RLEtBQUssS0FBSyxLQUNsRDZRLGNBQ0E7WUFBRTdRLE9BQU8rUSxPQUFPLENBQUMsRUFBRSxDQUFDL1EsS0FBSztZQUFFdUgsU0FBUztRQUFLLElBQzdDc0osY0FDUkQ7SUFDVjtJQUNBLE9BQU9BO0FBQ1g7QUFFQSxJQUFJTSxrQkFBa0IsQ0FBQ2xSLFFBQU8sRUFBRW1SLGFBQWEsRUFBRUMsV0FBVyxFQUFFQyxVQUFVLEVBQUUsR0FBSzlPLFlBQVl2QyxVQUNuRkEsU0FDQW1SLGdCQUNJblIsV0FBVSxLQUNOc1IsTUFDQXRSLFNBQ0ksQ0FBQ0EsU0FDREEsU0FDUm9SLGVBQWV0SixTQUFTOUgsVUFDcEIsSUFBSUMsS0FBS0QsVUFDVHFSLGFBQ0lBLFdBQVdyUixVQUNYQTtBQUVsQixNQUFNdVIsZ0JBQWdCO0lBQ2xCaEssU0FBUztJQUNUdkgsT0FBTztBQUNYO0FBQ0EsSUFBSXdSLGdCQUFnQixDQUFDVCxVQUFZMVEsTUFBTUMsT0FBTyxDQUFDeVEsV0FDekNBLFFBQVEvTixNQUFNLENBQUMsQ0FBQ3lPLFVBQVVULFNBQVdBLFVBQVVBLE9BQU90USxPQUFPLElBQUksQ0FBQ3NRLE9BQU90SyxRQUFRLEdBQzdFO1lBQ0VhLFNBQVM7WUFDVHZILE9BQU9nUixPQUFPaFIsS0FBSztRQUN2QixJQUNFeVIsVUFBVUYsaUJBQ2RBO0FBRU4sU0FBU0csY0FBY3hILEVBQUU7SUFDckIsTUFBTUosTUFBTUksR0FBR0osR0FBRztJQUNsQixJQUFJcUYsWUFBWXJGLE1BQU07UUFDbEIsT0FBT0EsSUFBSTZILEtBQUs7SUFDcEI7SUFDQSxJQUFJakMsYUFBYTVGLE1BQU07UUFDbkIsT0FBTzBILGNBQWN0SCxHQUFHMEgsSUFBSSxFQUFFNVIsS0FBSztJQUN2QztJQUNBLElBQUl5UCxpQkFBaUIzRixNQUFNO1FBQ3ZCLE9BQU87ZUFBSUEsSUFBSStILGVBQWU7U0FBQyxDQUFDeEosR0FBRyxDQUFDLENBQUMsRUFBRXJJLE9BQUFBLE1BQUssRUFBRSxHQUFLQTtJQUN2RDtJQUNBLElBQUlKLGdCQUFnQmtLLE1BQU07UUFDdEIsT0FBT2dILGlCQUFpQjVHLEdBQUcwSCxJQUFJLEVBQUU1UixLQUFLO0lBQzFDO0lBQ0EsT0FBT2tSLGdCQUFnQjNPLFlBQVl1SCxJQUFJOUosS0FBSyxJQUFJa0ssR0FBR0osR0FBRyxDQUFDOUosS0FBSyxHQUFHOEosSUFBSTlKLEtBQUssRUFBRWtLO0FBQzlFO0FBRUEsSUFBSTRILHFCQUFxQixDQUFDQyxhQUFhOUgsU0FBUytILGNBQWNDO0lBQzFELE1BQU0xQixTQUFTLENBQUM7SUFDaEIsS0FBSyxNQUFNM1AsUUFBUW1SLFlBQWE7UUFDNUIsTUFBTS9ILFFBQVF0SCxJQUFJdUgsU0FBU3JKO1FBQzNCb0osU0FBU3pHLElBQUlnTixRQUFRM1AsTUFBTW9KLE1BQU1FLEVBQUU7SUFDdkM7SUFDQSxPQUFPO1FBQ0g4SDtRQUNBaFIsT0FBTztlQUFJK1E7U0FBWTtRQUN2QnhCO1FBQ0EwQjtJQUNKO0FBQ0o7QUFFQSxJQUFJQyxVQUFVLENBQUNsUyxTQUFVQSxrQkFBaUJtUztBQUUxQyxJQUFJQyxlQUFlLENBQUNDLE9BQVM5UCxZQUFZOFAsUUFDbkNBLE9BQ0FILFFBQVFHLFFBQ0pBLEtBQUtDLE1BQU0sR0FDWGxTLFNBQVNpUyxRQUNMSCxRQUFRRyxLQUFLclMsS0FBSyxJQUNkcVMsS0FBS3JTLEtBQUssQ0FBQ3NTLE1BQU0sR0FDakJELEtBQUtyUyxLQUFLLEdBQ2RxUztBQUVkLElBQUlFLHFCQUFxQixDQUFDQyxPQUFVO1FBQ2hDQyxZQUFZLENBQUNELFFBQVFBLFNBQVNyTyxnQkFBZ0JHLFFBQVE7UUFDdERvTyxVQUFVRixTQUFTck8sZ0JBQWdCQyxNQUFNO1FBQ3pDdU8sWUFBWUgsU0FBU3JPLGdCQUFnQkUsUUFBUTtRQUM3Q3VPLFNBQVNKLFNBQVNyTyxnQkFBZ0JLLEdBQUc7UUFDckNxTyxXQUFXTCxTQUFTck8sZ0JBQWdCSSxTQUFTO0lBQ2pEO0FBRUEsTUFBTXVPLGlCQUFpQjtBQUN2QixJQUFJQyx1QkFBdUIsQ0FBQ0MsaUJBQW1CLENBQUMsQ0FBQ0Esa0JBQzdDLENBQUMsQ0FBQ0EsZUFBZWhPLFFBQVEsSUFDekIsQ0FBQyxDQUFFLFlBQVlnTyxlQUFlaE8sUUFBUSxLQUNsQ2dPLGVBQWVoTyxRQUFRLENBQUMzRCxXQUFXLENBQUNULElBQUksS0FBS2tTLGtCQUM1QzFTLFNBQVM0UyxlQUFlaE8sUUFBUSxLQUM3QmlCLE9BQU8yQyxNQUFNLENBQUNvSyxlQUFlaE8sUUFBUSxFQUFFaU8sSUFBSSxDQUFDLENBQUNDLG1CQUFxQkEsaUJBQWlCN1IsV0FBVyxDQUFDVCxJQUFJLEtBQUtrUyxlQUFlO0FBRW5JLElBQUlLLGdCQUFnQixDQUFDcEMsVUFBWUEsUUFBUXBHLEtBQUssSUFDekNvRyxDQUFBQSxRQUFRaE0sUUFBUSxJQUNiZ00sUUFBUXBNLEdBQUcsSUFDWG9NLFFBQVFyTSxHQUFHLElBQ1hxTSxRQUFRbk0sU0FBUyxJQUNqQm1NLFFBQVFsTSxTQUFTLElBQ2pCa00sUUFBUWpNLE9BQU8sSUFDZmlNLFFBQVEvTCxRQUFRO0FBRXhCLElBQUlvTyxZQUFZLENBQUN4UyxNQUFNb0gsUUFBUXFMLGNBQWdCLENBQUNBLGVBQzNDckwsQ0FBQUEsT0FBT08sUUFBUSxJQUNaUCxPQUFPRyxLQUFLLENBQUNsSCxHQUFHLENBQUNMLFNBQ2pCO1dBQUlvSCxPQUFPRyxLQUFLO0tBQUMsQ0FBQzRFLElBQUksQ0FBQyxDQUFDdUcsWUFBYzFTLEtBQUsyUyxVQUFVLENBQUNELGNBQ2xELFNBQVNuUSxJQUFJLENBQUN2QyxLQUFLb1AsS0FBSyxDQUFDc0QsVUFBVTVQLE1BQU0sR0FBRTtBQUV2RCxNQUFNOFAsd0JBQXdCLENBQUNqRCxRQUFRMUYsUUFBUWtILGFBQWEwQjtJQUN4RCxLQUFLLE1BQU10UixPQUFPNFAsZUFBZTlMLE9BQU9vRixJQUFJLENBQUNrRixRQUFTO1FBQ2xELE1BQU12RyxRQUFRdEgsSUFBSTZOLFFBQVFwTztRQUMxQixJQUFJNkgsT0FBTztZQUNQLE1BQU0sRUFBRUUsRUFBRSxFQUFFLEdBQUd3SixjQUFjLEdBQUcxSjtZQUNoQyxJQUFJRSxJQUFJO2dCQUNKLElBQUlBLEdBQUcwSCxJQUFJLElBQUkxSCxHQUFHMEgsSUFBSSxDQUFDLEVBQUUsSUFBSS9HLE9BQU9YLEdBQUcwSCxJQUFJLENBQUMsRUFBRSxFQUFFelAsUUFBUSxDQUFDc1IsWUFBWTtvQkFDakUsT0FBTztnQkFDWCxPQUNLLElBQUl2SixHQUFHSixHQUFHLElBQUllLE9BQU9YLEdBQUdKLEdBQUcsRUFBRUksR0FBR3RKLElBQUksS0FBSyxDQUFDNlMsWUFBWTtvQkFDdkQsT0FBTztnQkFDWCxPQUNLO29CQUNELElBQUlELHNCQUFzQkUsY0FBYzdJLFNBQVM7d0JBQzdDO29CQUNKO2dCQUNKO1lBQ0osT0FDSyxJQUFJekssU0FBU3NULGVBQWU7Z0JBQzdCLElBQUlGLHNCQUFzQkUsY0FBYzdJLFNBQVM7b0JBQzdDO2dCQUNKO1lBQ0o7UUFDSjtJQUNKO0lBQ0E7QUFDSjtBQUVBLFNBQVM4SSxrQkFBa0JuTSxNQUFNLEVBQUV5QyxPQUFPLEVBQUVySixJQUFJO0lBQzVDLE1BQU1nSixRQUFRbEgsSUFBSThFLFFBQVE1RztJQUMxQixJQUFJZ0osU0FBUzFHLE1BQU10QyxPQUFPO1FBQ3RCLE9BQU87WUFDSGdKO1lBQ0FoSjtRQUNKO0lBQ0o7SUFDQSxNQUFNSSxRQUFRSixLQUFLbUMsS0FBSyxDQUFDO0lBQ3pCLE1BQU8vQixNQUFNMEMsTUFBTSxDQUFFO1FBQ2pCLE1BQU00RSxZQUFZdEgsTUFBTTRTLElBQUksQ0FBQztRQUM3QixNQUFNNUosUUFBUXRILElBQUl1SCxTQUFTM0I7UUFDM0IsTUFBTXVMLGFBQWFuUixJQUFJOEUsUUFBUWM7UUFDL0IsSUFBSTBCLFNBQVMsQ0FBQzNKLE1BQU1DLE9BQU8sQ0FBQzBKLFVBQVVwSixTQUFTMEgsV0FBVztZQUN0RCxPQUFPO2dCQUFFMUg7WUFBSztRQUNsQjtRQUNBLElBQUlpVCxjQUFjQSxXQUFXL1QsSUFBSSxFQUFFO1lBQy9CLE9BQU87Z0JBQ0hjLE1BQU0wSDtnQkFDTnNCLE9BQU9pSztZQUNYO1FBQ0o7UUFDQTdTLE1BQU04UyxHQUFHO0lBQ2I7SUFDQSxPQUFPO1FBQ0hsVDtJQUNKO0FBQ0o7QUFFQSxJQUFJbVQsd0JBQXdCLENBQUNDLGVBQWU1TixpQkFBaUJRLGlCQUFpQmQ7SUFDMUVjLGdCQUFnQm9OO0lBQ2hCLE1BQU0sRUFBRXBULElBQUksRUFBRSxHQUFHK0UsV0FBVyxHQUFHcU87SUFDL0IsT0FBUTlFLGNBQWN2SixjQUNsQk0sT0FBT29GLElBQUksQ0FBQzFGLFdBQVdqQyxNQUFNLElBQUl1QyxPQUFPb0YsSUFBSSxDQUFDakYsaUJBQWlCMUMsTUFBTSxJQUNwRXVDLE9BQU9vRixJQUFJLENBQUMxRixXQUFXc04sSUFBSSxDQUFDLENBQUM5USxNQUFRaUUsZUFBZSxDQUFDakUsSUFBSSxLQUNwRCxFQUFDMkQsVUFBVTNCLGdCQUFnQkssR0FBRztBQUMzQztBQUVBLElBQUl5UCx3QkFBd0IsQ0FBQ3JULE1BQU1zVCxZQUFZdk4sUUFBVSxDQUFDL0YsUUFDdEQsQ0FBQ3NULGNBQ0R0VCxTQUFTc1QsY0FDVGxHLHNCQUFzQnBOLE1BQU1tTSxJQUFJLENBQUMsQ0FBQ29ILGNBQWdCQSxlQUM3Q3hOLENBQUFBLFFBQ0t3TixnQkFBZ0JELGFBQ2hCQyxZQUFZWixVQUFVLENBQUNXLGVBQ3JCQSxXQUFXWCxVQUFVLENBQUNZLFlBQVc7QUFFakQsSUFBSUMsaUJBQWlCLENBQUNmLGFBQWExSixXQUFXMEssYUFBYUMsZ0JBQWdCOUI7SUFDdkUsSUFBSUEsS0FBS0ksT0FBTyxFQUFFO1FBQ2QsT0FBTztJQUNYLE9BQ0ssSUFBSSxDQUFDeUIsZUFBZTdCLEtBQUtLLFNBQVMsRUFBRTtRQUNyQyxPQUFPLENBQUVsSixDQUFBQSxhQUFhMEosV0FBVTtJQUNwQyxPQUNLLElBQUlnQixjQUFjQyxlQUFlNUIsUUFBUSxHQUFHRixLQUFLRSxRQUFRLEVBQUU7UUFDNUQsT0FBTyxDQUFDVztJQUNaLE9BQ0ssSUFBSWdCLGNBQWNDLGVBQWUzQixVQUFVLEdBQUdILEtBQUtHLFVBQVUsRUFBRTtRQUNoRSxPQUFPVTtJQUNYO0lBQ0EsT0FBTztBQUNYO0FBRUEsSUFBSWtCLGtCQUFrQixDQUFDekssS0FBS2xKLE9BQVMsQ0FBQ3dCLFFBQVFNLElBQUlvSCxLQUFLbEosT0FBTzhDLE1BQU0sSUFBSXdNLE1BQU1wRyxLQUFLbEo7QUFFbkYsSUFBSTRULDRCQUE0QixDQUFDaE4sUUFBUW9DLE9BQU9oSjtJQUM1QyxNQUFNNlQsbUJBQW1Cekcsc0JBQXNCdEwsSUFBSThFLFFBQVE1RztJQUMzRDJDLElBQUlrUixrQkFBa0IsUUFBUTdLLEtBQUssQ0FBQ2hKLEtBQUs7SUFDekMyQyxJQUFJaUUsUUFBUTVHLE1BQU02VDtJQUNsQixPQUFPak47QUFDWDtBQUVBLElBQUlrTixZQUFZLENBQUMxVSxTQUFVOEgsU0FBUzlIO0FBRXBDLFNBQVMyVSxpQkFBaUI3UixNQUFNLEVBQUVnSCxHQUFHLEVBQUVoSyxPQUFPLFVBQVU7SUFDcEQsSUFBSTRVLFVBQVU1UixXQUNUekMsTUFBTUMsT0FBTyxDQUFDd0MsV0FBV0EsT0FBTzhSLEtBQUssQ0FBQ0YsY0FDdEN6UixVQUFVSCxXQUFXLENBQUNBLFFBQVM7UUFDaEMsT0FBTztZQUNIaEQ7WUFDQXdLLFNBQVNvSyxVQUFVNVIsVUFBVUEsU0FBUztZQUN0Q2dIO1FBQ0o7SUFDSjtBQUNKO0FBRUEsSUFBSStLLHFCQUFxQixDQUFDQyxpQkFBbUIxVSxTQUFTMFUsbUJBQW1CLENBQUM1QyxRQUFRNEMsa0JBQzVFQSxpQkFDQTtRQUNFOVUsT0FBTzhVO1FBQ1B4SyxTQUFTO0lBQ2I7QUFFSixJQUFJeUssZ0JBQWdCLE9BQU8vSyxPQUFPZ0wsb0JBQW9CL00sWUFBWTZGLDBCQUEwQm1FLDJCQUEyQmdEO0lBQ25ILE1BQU0sRUFBRW5MLEdBQUcsRUFBRThILElBQUksRUFBRTdNLFFBQVEsRUFBRUgsU0FBUyxFQUFFQyxTQUFTLEVBQUVGLEdBQUcsRUFBRUQsR0FBRyxFQUFFSSxPQUFPLEVBQUVFLFFBQVEsRUFBRXBFLElBQUksRUFBRXVRLGFBQWEsRUFBRXhHLEtBQUssRUFBRyxHQUFHWCxNQUFNRSxFQUFFO0lBQ3hILE1BQU1nTCxhQUFheFMsSUFBSXVGLFlBQVlySDtJQUNuQyxJQUFJLENBQUMrSixTQUFTcUssbUJBQW1CL1QsR0FBRyxDQUFDTCxPQUFPO1FBQ3hDLE9BQU8sQ0FBQztJQUNaO0lBQ0EsTUFBTXVVLFdBQVd2RCxPQUFPQSxJQUFJLENBQUMsRUFBRSxHQUFHOUg7SUFDbEMsTUFBTU8sb0JBQW9CLENBQUNDO1FBQ3ZCLElBQUkySCw2QkFBNkJrRCxTQUFTNUssY0FBYyxFQUFFO1lBQ3RENEssU0FBUzlLLGlCQUFpQixDQUFDcEgsVUFBVXFILFdBQVcsS0FBS0EsV0FBVztZQUNoRTZLLFNBQVM1SyxjQUFjO1FBQzNCO0lBQ0o7SUFDQSxNQUFNWCxRQUFRLENBQUM7SUFDZixNQUFNd0wsVUFBVTFGLGFBQWE1RjtJQUM3QixNQUFNdUwsYUFBYXpWLGdCQUFnQmtLO0lBQ25DLE1BQU02RixvQkFBb0J5RixXQUFXQztJQUNyQyxNQUFNQyxVQUFVLENBQUVuRSxpQkFBaUJoQyxZQUFZckYsSUFBRyxLQUM5Q3ZILFlBQVl1SCxJQUFJOUosS0FBSyxLQUNyQnVDLFlBQVkyUyxlQUNYN0YsY0FBY3ZGLFFBQVFBLElBQUk5SixLQUFLLEtBQUssTUFDckNrVixlQUFlLE1BQ2Q3VSxNQUFNQyxPQUFPLENBQUM0VSxlQUFlLENBQUNBLFdBQVd4UixNQUFNO0lBQ3BELE1BQU02UixvQkFBb0IxSCxhQUFhMkgsSUFBSSxDQUFDLE1BQU01VSxNQUFNa04sMEJBQTBCbEU7SUFDbEYsTUFBTTZMLG1CQUFtQixDQUFDQyxXQUFXQyxrQkFBa0JDLGtCQUFrQkMsVUFBVXBSLHVCQUF1QkcsU0FBUyxFQUFFa1IsVUFBVXJSLHVCQUF1QkksU0FBUztRQUMzSixNQUFNeUYsVUFBVW9MLFlBQVlDLG1CQUFtQkM7UUFDL0NoTSxLQUFLLENBQUNoSixLQUFLLEdBQUc7WUFDVmQsTUFBTTRWLFlBQVlHLFVBQVVDO1lBQzVCeEw7WUFDQVI7WUFDQSxHQUFHeUwsa0JBQWtCRyxZQUFZRyxVQUFVQyxTQUFTeEwsUUFBUTtRQUNoRTtJQUNKO0lBQ0EsSUFBSTJLLGVBQ0UsQ0FBQzVVLE1BQU1DLE9BQU8sQ0FBQzRVLGVBQWUsQ0FBQ0EsV0FBV3hSLE1BQU0sR0FDaERxQixZQUNHLEVBQUU0SyxxQkFBc0IyRixDQUFBQSxXQUFXcFYsa0JBQWtCZ1YsV0FBVSxLQUMzRGpTLFVBQVVpUyxlQUFlLENBQUNBLGNBQzFCRyxjQUFjLENBQUN2RSxpQkFBaUJjLE1BQU1ySyxPQUFPLElBQzdDNk4sV0FBVyxDQUFDNUQsY0FBY0ksTUFBTXJLLE9BQU8sR0FBSTtRQUNwRCxNQUFNLEVBQUV2SCxPQUFBQSxNQUFLLEVBQUVzSyxPQUFPLEVBQUUsR0FBR29LLFVBQVUzUCxZQUMvQjtZQUFFL0UsT0FBTyxDQUFDLENBQUMrRTtZQUFVdUYsU0FBU3ZGO1FBQVMsSUFDdkM4UCxtQkFBbUI5UDtRQUN6QixJQUFJL0UsUUFBTztZQUNQNEosS0FBSyxDQUFDaEosS0FBSyxHQUFHO2dCQUNWZCxNQUFNMkUsdUJBQXVCTSxRQUFRO2dCQUNyQ3VGO2dCQUNBUixLQUFLcUw7Z0JBQ0wsR0FBR0ksa0JBQWtCOVEsdUJBQXVCTSxRQUFRLEVBQUV1RixRQUFRO1lBQ2xFO1lBQ0EsSUFBSSxDQUFDd0QsMEJBQTBCO2dCQUMzQnpELGtCQUFrQkM7Z0JBQ2xCLE9BQU9WO1lBQ1g7UUFDSjtJQUNKO0lBQ0EsSUFBSSxDQUFDMEwsV0FBWSxFQUFDcFYsa0JBQWtCeUUsUUFBUSxDQUFDekUsa0JBQWtCd0UsSUFBRyxHQUFJO1FBQ2xFLElBQUlnUjtRQUNKLElBQUlLO1FBQ0osTUFBTUMsWUFBWW5CLG1CQUFtQm5RO1FBQ3JDLE1BQU11UixZQUFZcEIsbUJBQW1CbFE7UUFDckMsSUFBSSxDQUFDekUsa0JBQWtCZ1YsZUFBZSxDQUFDcFIsTUFBTW9SLGFBQWE7WUFDdEQsTUFBTWdCLGNBQWNwTSxJQUFJcUgsYUFBYSxJQUNoQytELENBQUFBLGFBQWEsQ0FBQ0EsYUFBYUEsVUFBUztZQUN6QyxJQUFJLENBQUNoVixrQkFBa0I4VixVQUFVaFcsS0FBSyxHQUFHO2dCQUNyQzBWLFlBQVlRLGNBQWNGLFVBQVVoVyxLQUFLO1lBQzdDO1lBQ0EsSUFBSSxDQUFDRSxrQkFBa0IrVixVQUFValcsS0FBSyxHQUFHO2dCQUNyQytWLFlBQVlHLGNBQWNELFVBQVVqVyxLQUFLO1lBQzdDO1FBQ0osT0FDSztZQUNELE1BQU1tVyxZQUFZck0sSUFBSXNILFdBQVcsSUFBSSxJQUFJblIsS0FBS2lWO1lBQzlDLE1BQU1rQixvQkFBb0IsQ0FBQ0MsT0FBUyxJQUFJcFcsS0FBSyxJQUFJQSxPQUFPcVcsWUFBWSxLQUFLLE1BQU1EO1lBQy9FLE1BQU1FLFNBQVN6TSxJQUFJaEssSUFBSSxJQUFJO1lBQzNCLE1BQU0wVyxTQUFTMU0sSUFBSWhLLElBQUksSUFBSTtZQUMzQixJQUFJZ0ksU0FBU2tPLFVBQVVoVyxLQUFLLEtBQUtrVixZQUFZO2dCQUN6Q1EsWUFBWWEsU0FDTkgsa0JBQWtCbEIsY0FBY2tCLGtCQUFrQkosVUFBVWhXLEtBQUssSUFDakV3VyxTQUNJdEIsYUFBYWMsVUFBVWhXLEtBQUssR0FDNUJtVyxZQUFZLElBQUlsVyxLQUFLK1YsVUFBVWhXLEtBQUs7WUFDbEQ7WUFDQSxJQUFJOEgsU0FBU21PLFVBQVVqVyxLQUFLLEtBQUtrVixZQUFZO2dCQUN6Q2EsWUFBWVEsU0FDTkgsa0JBQWtCbEIsY0FBY2tCLGtCQUFrQkgsVUFBVWpXLEtBQUssSUFDakV3VyxTQUNJdEIsYUFBYWUsVUFBVWpXLEtBQUssR0FDNUJtVyxZQUFZLElBQUlsVyxLQUFLZ1csVUFBVWpXLEtBQUs7WUFDbEQ7UUFDSjtRQUNBLElBQUkwVixhQUFhSyxXQUFXO1lBQ3hCTixpQkFBaUIsQ0FBQyxDQUFDQyxXQUFXTSxVQUFVMUwsT0FBTyxFQUFFMkwsVUFBVTNMLE9BQU8sRUFBRTdGLHVCQUF1QkMsR0FBRyxFQUFFRCx1QkFBdUJFLEdBQUc7WUFDMUgsSUFBSSxDQUFDbUosMEJBQTBCO2dCQUMzQnpELGtCQUFrQlQsS0FBSyxDQUFDaEosS0FBSyxDQUFDMEosT0FBTztnQkFDckMsT0FBT1Y7WUFDWDtRQUNKO0lBQ0o7SUFDQSxJQUFJLENBQUNoRixhQUFhQyxTQUFRLEtBQ3RCLENBQUN5USxXQUNBeE4sQ0FBQUEsU0FBU29OLGVBQWdCRCxnQkFBZ0I1VSxNQUFNQyxPQUFPLENBQUM0VSxXQUFXLEdBQUk7UUFDdkUsTUFBTXVCLGtCQUFrQjVCLG1CQUFtQmpRO1FBQzNDLE1BQU04UixrQkFBa0I3QixtQkFBbUJoUTtRQUMzQyxNQUFNNlEsWUFBWSxDQUFDeFYsa0JBQWtCdVcsZ0JBQWdCelcsS0FBSyxLQUN0RGtWLFdBQVd4UixNQUFNLEdBQUcsQ0FBQytTLGdCQUFnQnpXLEtBQUs7UUFDOUMsTUFBTStWLFlBQVksQ0FBQzdWLGtCQUFrQndXLGdCQUFnQjFXLEtBQUssS0FDdERrVixXQUFXeFIsTUFBTSxHQUFHLENBQUNnVCxnQkFBZ0IxVyxLQUFLO1FBQzlDLElBQUkwVixhQUFhSyxXQUFXO1lBQ3hCTixpQkFBaUJDLFdBQVdlLGdCQUFnQm5NLE9BQU8sRUFBRW9NLGdCQUFnQnBNLE9BQU87WUFDNUUsSUFBSSxDQUFDd0QsMEJBQTBCO2dCQUMzQnpELGtCQUFrQlQsS0FBSyxDQUFDaEosS0FBSyxDQUFDMEosT0FBTztnQkFDckMsT0FBT1Y7WUFDWDtRQUNKO0lBQ0o7SUFDQSxJQUFJOUUsV0FBVyxDQUFDd1EsV0FBV3hOLFNBQVNvTixhQUFhO1FBQzdDLE1BQU0sRUFBRWxWLE9BQU8yVyxZQUFZLEVBQUVyTSxPQUFPLEVBQUUsR0FBR3VLLG1CQUFtQi9QO1FBQzVELElBQUlvTixRQUFReUUsaUJBQWlCLENBQUN6QixXQUFXMEIsS0FBSyxDQUFDRCxlQUFlO1lBQzFEL00sS0FBSyxDQUFDaEosS0FBSyxHQUFHO2dCQUNWZCxNQUFNMkUsdUJBQXVCSyxPQUFPO2dCQUNwQ3dGO2dCQUNBUjtnQkFDQSxHQUFHeUwsa0JBQWtCOVEsdUJBQXVCSyxPQUFPLEVBQUV3RixRQUFRO1lBQ2pFO1lBQ0EsSUFBSSxDQUFDd0QsMEJBQTBCO2dCQUMzQnpELGtCQUFrQkM7Z0JBQ2xCLE9BQU9WO1lBQ1g7UUFDSjtJQUNKO0lBQ0EsSUFBSTVFLFVBQVU7UUFDVixJQUFJb0ssV0FBV3BLLFdBQVc7WUFDdEIsTUFBTWxDLFNBQVMsTUFBTWtDLFNBQVNrUSxZQUFZak47WUFDMUMsTUFBTTRPLGdCQUFnQmxDLGlCQUFpQjdSLFFBQVFxUztZQUMvQyxJQUFJMEIsZUFBZTtnQkFDZmpOLEtBQUssQ0FBQ2hKLEtBQUssR0FBRztvQkFDVixHQUFHaVcsYUFBYTtvQkFDaEIsR0FBR3RCLGtCQUFrQjlRLHVCQUF1Qk8sUUFBUSxFQUFFNlIsY0FBY3ZNLE9BQU8sQ0FBQztnQkFDaEY7Z0JBQ0EsSUFBSSxDQUFDd0QsMEJBQTBCO29CQUMzQnpELGtCQUFrQndNLGNBQWN2TSxPQUFPO29CQUN2QyxPQUFPVjtnQkFDWDtZQUNKO1FBQ0osT0FDSyxJQUFJeEosU0FBUzRFLFdBQVc7WUFDekIsSUFBSThSLG1CQUFtQixDQUFDO1lBQ3hCLElBQUssTUFBTTNVLE9BQU82QyxTQUFVO2dCQUN4QixJQUFJLENBQUNrSyxjQUFjNEgscUJBQXFCLENBQUNoSiwwQkFBMEI7b0JBQy9EO2dCQUNKO2dCQUNBLE1BQU0rSSxnQkFBZ0JsQyxpQkFBaUIsTUFBTTNQLFFBQVEsQ0FBQzdDLElBQUksQ0FBQytTLFlBQVlqTixhQUFha04sVUFBVWhUO2dCQUM5RixJQUFJMFUsZUFBZTtvQkFDZkMsbUJBQW1CO3dCQUNmLEdBQUdELGFBQWE7d0JBQ2hCLEdBQUd0QixrQkFBa0JwVCxLQUFLMFUsY0FBY3ZNLE9BQU8sQ0FBQztvQkFDcEQ7b0JBQ0FELGtCQUFrQndNLGNBQWN2TSxPQUFPO29CQUN2QyxJQUFJd0QsMEJBQTBCO3dCQUMxQmxFLEtBQUssQ0FBQ2hKLEtBQUssR0FBR2tXO29CQUNsQjtnQkFDSjtZQUNKO1lBQ0EsSUFBSSxDQUFDNUgsY0FBYzRILG1CQUFtQjtnQkFDbENsTixLQUFLLENBQUNoSixLQUFLLEdBQUc7b0JBQ1ZrSixLQUFLcUw7b0JBQ0wsR0FBRzJCLGdCQUFnQjtnQkFDdkI7Z0JBQ0EsSUFBSSxDQUFDaEosMEJBQTBCO29CQUMzQixPQUFPbEU7Z0JBQ1g7WUFDSjtRQUNKO0lBQ0o7SUFDQVMsa0JBQWtCO0lBQ2xCLE9BQU9UO0FBQ1g7QUFFQSxNQUFNbU4saUJBQWlCO0lBQ25CdkUsTUFBTXJPLGdCQUFnQkcsUUFBUTtJQUM5QmdRLGdCQUFnQm5RLGdCQUFnQkUsUUFBUTtJQUN4QzJTLGtCQUFrQjtBQUN0QjtBQUNBLFNBQVNDLGtCQUFrQjNSLFFBQVEsQ0FBQyxDQUFDO0lBQ2pDLElBQUltRixXQUFXO1FBQ1gsR0FBR3NNLGNBQWM7UUFDakIsR0FBR3pSLEtBQUs7SUFDWjtJQUNBLElBQUl3QixhQUFhO1FBQ2JvUSxhQUFhO1FBQ2JqUSxTQUFTO1FBQ1RrUSxTQUFTO1FBQ1RqUSxXQUFXa0ksV0FBVzNFLFNBQVMxRSxhQUFhO1FBQzVDdUIsY0FBYztRQUNkK00sYUFBYTtRQUNiK0MsY0FBYztRQUNkM0osb0JBQW9CO1FBQ3BCbEcsU0FBUztRQUNUSCxlQUFlLENBQUM7UUFDaEJELGFBQWEsQ0FBQztRQUNkRSxrQkFBa0IsQ0FBQztRQUNuQkcsUUFBUWlELFNBQVNqRCxNQUFNLElBQUksQ0FBQztRQUM1QmQsVUFBVStELFNBQVMvRCxRQUFRLElBQUk7SUFDbkM7SUFDQSxNQUFNdUQsVUFBVSxDQUFDO0lBQ2pCLElBQUlqRSxpQkFBaUI1RixTQUFTcUssU0FBUzFFLGFBQWEsS0FBSzNGLFNBQVNxSyxTQUFTN0IsTUFBTSxJQUMzRWhILFlBQVk2SSxTQUFTMUUsYUFBYSxJQUFJMEUsU0FBUzdCLE1BQU0sS0FBSyxDQUFDLElBQzNELENBQUM7SUFDUCxJQUFJQyxjQUFjNEIsU0FBU3pCLGdCQUFnQixHQUNyQyxDQUFDLElBQ0RwSCxZQUFZb0U7SUFDbEIsSUFBSTRFLFNBQVM7UUFDVEMsUUFBUTtRQUNSRixPQUFPO1FBQ1B4QyxPQUFPO0lBQ1g7SUFDQSxJQUFJSCxTQUFTO1FBQ1QyQyxPQUFPLElBQUkxSTtRQUNYeUUsVUFBVSxJQUFJekU7UUFDZG9WLFNBQVMsSUFBSXBWO1FBQ2JpSCxPQUFPLElBQUlqSDtRQUNYa0csT0FBTyxJQUFJbEc7SUFDZjtJQUNBLElBQUlxVjtJQUNKLElBQUlDLFFBQVE7SUFDWixNQUFNblIsa0JBQWtCO1FBQ3BCYSxTQUFTO1FBQ1RFLGFBQWE7UUFDYkUsa0JBQWtCO1FBQ2xCRCxlQUFlO1FBQ2ZFLGNBQWM7UUFDZEMsU0FBUztRQUNUQyxRQUFRO0lBQ1o7SUFDQSxJQUFJZ1EsMkJBQTJCO1FBQzNCLEdBQUdwUixlQUFlO0lBQ3RCO0lBQ0EsTUFBTWtILFlBQVk7UUFDZHBFLE9BQU8rRTtRQUNQVixPQUFPVTtJQUNYO0lBQ0EsTUFBTXdKLG1DQUFtQ2hOLFNBQVN1SCxZQUFZLEtBQUs3TixnQkFBZ0JLLEdBQUc7SUFDdEYsTUFBTWtULFdBQVcsQ0FBQy9QLFdBQWEsQ0FBQ2dRO1lBQzVCQyxhQUFhTDtZQUNiQSxRQUFRTSxXQUFXbFEsVUFBVWdRO1FBQ2pDO0lBQ0EsTUFBTS9QLFlBQVksT0FBT2tRO1FBQ3JCLElBQUksQ0FBQ3JOLFNBQVMvRCxRQUFRLElBQ2pCTixDQUFBQSxnQkFBZ0JtQixPQUFPLElBQ3BCaVEseUJBQXlCalEsT0FBTyxJQUNoQ3VRLGlCQUFnQixHQUFJO1lBQ3hCLE1BQU12USxVQUFVa0QsU0FBU3NOLFFBQVEsR0FDM0I3SSxjQUFjLENBQUMsTUFBTThJLFlBQVcsRUFBR3hRLE1BQU0sSUFDekMsTUFBTXlRLHlCQUF5QmhPLFNBQVM7WUFDOUMsSUFBSTFDLFlBQVlULFdBQVdTLE9BQU8sRUFBRTtnQkFDaEMrRixVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztvQkFDakJqRztnQkFDSjtZQUNKO1FBQ0o7SUFDSjtJQUNBLE1BQU0yUSxzQkFBc0IsQ0FBQ2xYLE9BQU9zRztRQUNoQyxJQUFJLENBQUNtRCxTQUFTL0QsUUFBUSxJQUNqQk4sQ0FBQUEsZ0JBQWdCa0IsWUFBWSxJQUN6QmxCLGdCQUFnQmlCLGdCQUFnQixJQUNoQ21RLHlCQUF5QmxRLFlBQVksSUFDckNrUSx5QkFBeUJuUSxnQkFBZ0IsR0FBRztZQUMvQ3JHLENBQUFBLFNBQVNYLE1BQU04WCxJQUFJLENBQUNuUSxPQUFPMkMsS0FBSyxHQUFHeU4sT0FBTyxDQUFDLENBQUN4WDtnQkFDekMsSUFBSUEsTUFBTTtvQkFDTjBHLGVBQ00vRCxJQUFJdUQsV0FBV08sZ0JBQWdCLEVBQUV6RyxNQUFNMEcsZ0JBQ3ZDNEksTUFBTXBKLFdBQVdPLGdCQUFnQixFQUFFekc7Z0JBQzdDO1lBQ0o7WUFDQTBNLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUNqQm5HLGtCQUFrQlAsV0FBV08sZ0JBQWdCO2dCQUM3Q0MsY0FBYyxDQUFDNEgsY0FBY3BJLFdBQVdPLGdCQUFnQjtZQUM1RDtRQUNKO0lBQ0o7SUFDQSxNQUFNZ1IsaUJBQWlCLENBQUN6WCxNQUFNZ0ksU0FBUyxFQUFFLEVBQUVnRCxRQUFRME0sTUFBTUMsa0JBQWtCLElBQUksRUFBRUMsNkJBQTZCLElBQUk7UUFDOUcsSUFBSUYsUUFBUTFNLFVBQVUsQ0FBQ25CLFNBQVMvRCxRQUFRLEVBQUU7WUFDdENrRSxPQUFPQyxNQUFNLEdBQUc7WUFDaEIsSUFBSTJOLDhCQUE4Qm5ZLE1BQU1DLE9BQU8sQ0FBQ29DLElBQUl1SCxTQUFTckosUUFBUTtnQkFDakUsTUFBTTZYLGNBQWM3TSxPQUFPbEosSUFBSXVILFNBQVNySixPQUFPMFgsS0FBS0ksSUFBSSxFQUFFSixLQUFLSyxJQUFJO2dCQUNuRUosbUJBQW1CaFYsSUFBSTBHLFNBQVNySixNQUFNNlg7WUFDMUM7WUFDQSxJQUFJRCw4QkFDQW5ZLE1BQU1DLE9BQU8sQ0FBQ29DLElBQUlvRSxXQUFXVSxNQUFNLEVBQUU1RyxRQUFRO2dCQUM3QyxNQUFNNEcsU0FBU29FLE9BQU9sSixJQUFJb0UsV0FBV1UsTUFBTSxFQUFFNUcsT0FBTzBYLEtBQUtJLElBQUksRUFBRUosS0FBS0ssSUFBSTtnQkFDeEVKLG1CQUFtQmhWLElBQUl1RCxXQUFXVSxNQUFNLEVBQUU1RyxNQUFNNEc7Z0JBQ2hEK00sZ0JBQWdCek4sV0FBV1UsTUFBTSxFQUFFNUc7WUFDdkM7WUFDQSxJQUFJLENBQUN3RixnQkFBZ0JnQixhQUFhLElBQzlCb1EseUJBQXlCcFEsYUFBYSxLQUN0Q29SLDhCQUNBblksTUFBTUMsT0FBTyxDQUFDb0MsSUFBSW9FLFdBQVdNLGFBQWEsRUFBRXhHLFFBQVE7Z0JBQ3BELE1BQU13RyxnQkFBZ0J3RSxPQUFPbEosSUFBSW9FLFdBQVdNLGFBQWEsRUFBRXhHLE9BQU8wWCxLQUFLSSxJQUFJLEVBQUVKLEtBQUtLLElBQUk7Z0JBQ3RGSixtQkFBbUJoVixJQUFJdUQsV0FBV00sYUFBYSxFQUFFeEcsTUFBTXdHO1lBQzNEO1lBQ0EsSUFBSWhCLGdCQUFnQmUsV0FBVyxJQUFJcVEseUJBQXlCclEsV0FBVyxFQUFFO2dCQUNyRUwsV0FBV0ssV0FBVyxHQUFHd0osZUFBZTNLLGdCQUFnQjZDO1lBQzVEO1lBQ0F5RSxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztnQkFDakI1TTtnQkFDQXFHLFNBQVMyUixVQUFVaFksTUFBTWdJO2dCQUN6QnpCLGFBQWFMLFdBQVdLLFdBQVc7Z0JBQ25DSyxRQUFRVixXQUFXVSxNQUFNO2dCQUN6QkQsU0FBU1QsV0FBV1MsT0FBTztZQUMvQjtRQUNKLE9BQ0s7WUFDRGhFLElBQUlzRixhQUFhakksTUFBTWdJO1FBQzNCO0lBQ0o7SUFDQSxNQUFNaVEsZUFBZSxDQUFDalksTUFBTWdKO1FBQ3hCckcsSUFBSXVELFdBQVdVLE1BQU0sRUFBRTVHLE1BQU1nSjtRQUM3QjBELFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQ2pCaEcsUUFBUVYsV0FBV1UsTUFBTTtRQUM3QjtJQUNKO0lBQ0EsTUFBTXNSLGFBQWEsQ0FBQ3RSO1FBQ2hCVixXQUFXVSxNQUFNLEdBQUdBO1FBQ3BCOEYsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDakJoRyxRQUFRVixXQUFXVSxNQUFNO1lBQ3pCRCxTQUFTO1FBQ2I7SUFDSjtJQUNBLE1BQU13UixzQkFBc0IsQ0FBQ25ZLE1BQU1vWSxzQkFBc0JoWixRQUFPOEo7UUFDNUQsTUFBTUUsUUFBUXRILElBQUl1SCxTQUFTcko7UUFDM0IsSUFBSW9KLE9BQU87WUFDUCxNQUFNbkgsZUFBZUgsSUFBSW1HLGFBQWFqSSxNQUFNMkIsWUFBWXZDLFVBQVMwQyxJQUFJc0QsZ0JBQWdCcEYsUUFBUVo7WUFDN0Z1QyxZQUFZTSxpQkFDUGlILE9BQU9BLElBQUltUCxjQUFjLElBQzFCRCx1QkFDRXpWLElBQUlzRixhQUFhakksTUFBTW9ZLHVCQUF1Qm5XLGVBQWU2TyxjQUFjMUgsTUFBTUUsRUFBRSxLQUNuRmdQLGNBQWN0WSxNQUFNaUM7WUFDMUIrSCxPQUFPRCxLQUFLLElBQUkvQztRQUNwQjtJQUNKO0lBQ0EsTUFBTXVSLHNCQUFzQixDQUFDdlksTUFBTXdZLFlBQVkvRixhQUFhZ0csYUFBYUM7UUFDckUsSUFBSUMsb0JBQW9CO1FBQ3hCLElBQUlDLGtCQUFrQjtRQUN0QixNQUFNcE8sU0FBUztZQUNYeEs7UUFDSjtRQUNBLElBQUksQ0FBQzZKLFNBQVMvRCxRQUFRLEVBQUU7WUFDcEIsSUFBSSxDQUFDMk0sZUFBZWdHLGFBQWE7Z0JBQzdCLElBQUlqVCxnQkFBZ0JhLE9BQU8sSUFBSXVRLHlCQUF5QnZRLE9BQU8sRUFBRTtvQkFDN0R1UyxrQkFBa0IxUyxXQUFXRyxPQUFPO29CQUNwQ0gsV0FBV0csT0FBTyxHQUFHbUUsT0FBT25FLE9BQU8sR0FBRzJSO29CQUN0Q1csb0JBQW9CQyxvQkFBb0JwTyxPQUFPbkUsT0FBTztnQkFDMUQ7Z0JBQ0EsTUFBTXdTLHlCQUF5Qi9LLFVBQVVoTSxJQUFJc0QsZ0JBQWdCcEYsT0FBT3dZO2dCQUNwRUksa0JBQWtCLENBQUMsQ0FBQzlXLElBQUlvRSxXQUFXSyxXQUFXLEVBQUV2RztnQkFDaEQ2WSx5QkFDTXZKLE1BQU1wSixXQUFXSyxXQUFXLEVBQUV2RyxRQUM5QjJDLElBQUl1RCxXQUFXSyxXQUFXLEVBQUV2RyxNQUFNO2dCQUN4Q3dLLE9BQU9qRSxXQUFXLEdBQUdMLFdBQVdLLFdBQVc7Z0JBQzNDb1Msb0JBQ0lBLHFCQUNLLENBQUNuVCxnQkFBZ0JlLFdBQVcsSUFDekJxUSx5QkFBeUJyUSxXQUFXLEtBQ3BDcVMsb0JBQW9CLENBQUNDO1lBQ3JDO1lBQ0EsSUFBSXBHLGFBQWE7Z0JBQ2IsTUFBTXFHLHlCQUF5QmhYLElBQUlvRSxXQUFXTSxhQUFhLEVBQUV4RztnQkFDN0QsSUFBSSxDQUFDOFksd0JBQXdCO29CQUN6Qm5XLElBQUl1RCxXQUFXTSxhQUFhLEVBQUV4RyxNQUFNeVM7b0JBQ3BDakksT0FBT2hFLGFBQWEsR0FBR04sV0FBV00sYUFBYTtvQkFDL0NtUyxvQkFDSUEscUJBQ0ssQ0FBQ25ULGdCQUFnQmdCLGFBQWEsSUFDM0JvUSx5QkFBeUJwUSxhQUFhLEtBQ3RDc1MsMkJBQTJCckc7Z0JBQzNDO1lBQ0o7WUFDQWtHLHFCQUFxQkQsZ0JBQWdCaE0sVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUNwQztRQUM5RDtRQUNBLE9BQU9tTyxvQkFBb0JuTyxTQUFTLENBQUM7SUFDekM7SUFDQSxNQUFNdU8sc0JBQXNCLENBQUMvWSxNQUFNMkcsU0FBU3FDLE9BQU9MO1FBQy9DLE1BQU1xUSxxQkFBcUJsWCxJQUFJb0UsV0FBV1UsTUFBTSxFQUFFNUc7UUFDbEQsTUFBTWtYLG9CQUFvQixDQUFDMVIsZ0JBQWdCbUIsT0FBTyxJQUFJaVEseUJBQXlCalEsT0FBTyxLQUNsRnRFLFVBQVVzRSxZQUNWVCxXQUFXUyxPQUFPLEtBQUtBO1FBQzNCLElBQUlrRCxTQUFTb1AsVUFBVSxJQUFJalEsT0FBTztZQUM5QjBOLHFCQUFxQkksU0FBUyxJQUFNbUIsYUFBYWpZLE1BQU1nSjtZQUN2RDBOLG1CQUFtQjdNLFNBQVNvUCxVQUFVO1FBQzFDLE9BQ0s7WUFDRGpDLGFBQWFMO1lBQ2JELHFCQUFxQjtZQUNyQjFOLFFBQ01yRyxJQUFJdUQsV0FBV1UsTUFBTSxFQUFFNUcsTUFBTWdKLFNBQzdCc0csTUFBTXBKLFdBQVdVLE1BQU0sRUFBRTVHO1FBQ25DO1FBQ0EsSUFBSSxDQUFDZ0osUUFBUSxDQUFDOEUsVUFBVWtMLG9CQUFvQmhRLFNBQVNnUSxrQkFBaUIsS0FDbEUsQ0FBQzFLLGNBQWMzRixlQUNmdU8sbUJBQW1CO1lBQ25CLE1BQU1nQyxtQkFBbUI7Z0JBQ3JCLEdBQUd2USxVQUFVO2dCQUNiLEdBQUl1TyxxQkFBcUI3VSxVQUFVc0UsV0FBVztvQkFBRUE7Z0JBQVEsSUFBSSxDQUFDLENBQUM7Z0JBQzlEQyxRQUFRVixXQUFXVSxNQUFNO2dCQUN6QjVHO1lBQ0o7WUFDQWtHLGFBQWE7Z0JBQ1QsR0FBR0EsVUFBVTtnQkFDYixHQUFHZ1QsZ0JBQWdCO1lBQ3ZCO1lBQ0F4TSxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQ3NNO1FBQ3pCO0lBQ0o7SUFDQSxNQUFNOUIsYUFBYSxPQUFPcFg7UUFDdEJzWCxvQkFBb0J0WCxNQUFNO1FBQzFCLE1BQU1rQyxTQUFTLE1BQU0ySCxTQUFTc04sUUFBUSxDQUFDbFAsYUFBYTRCLFNBQVNzUCxPQUFPLEVBQUVqSSxtQkFBbUJsUixRQUFRb0gsT0FBTzJDLEtBQUssRUFBRVYsU0FBU1EsU0FBU3VILFlBQVksRUFBRXZILFNBQVN3SCx5QkFBeUI7UUFDakxpRyxvQkFBb0J0WDtRQUNwQixPQUFPa0M7SUFDWDtJQUNBLE1BQU1rWCw4QkFBOEIsT0FBT2haO1FBQ3ZDLE1BQU0sRUFBRXdHLE1BQU0sRUFBRSxHQUFHLE1BQU13USxXQUFXaFg7UUFDcEMsSUFBSUEsT0FBTztZQUNQLEtBQUssTUFBTUosUUFBUUksTUFBTztnQkFDdEIsTUFBTTRJLFFBQVFsSCxJQUFJOEUsUUFBUTVHO2dCQUMxQmdKLFFBQ01yRyxJQUFJdUQsV0FBV1UsTUFBTSxFQUFFNUcsTUFBTWdKLFNBQzdCc0csTUFBTXBKLFdBQVdVLE1BQU0sRUFBRTVHO1lBQ25DO1FBQ0osT0FDSztZQUNEa0csV0FBV1UsTUFBTSxHQUFHQTtRQUN4QjtRQUNBLE9BQU9BO0lBQ1g7SUFDQSxNQUFNeVEsMkJBQTJCLE9BQU8xSCxRQUFRMEosc0JBQXNCRixVQUFVO1FBQzVFRyxPQUFPO0lBQ1gsQ0FBQztRQUNHLElBQUssTUFBTXRaLFFBQVEyUCxPQUFRO1lBQ3ZCLE1BQU12RyxRQUFRdUcsTUFBTSxDQUFDM1AsS0FBSztZQUMxQixJQUFJb0osT0FBTztnQkFDUCxNQUFNLEVBQUVFLEVBQUUsRUFBRSxHQUFHa1AsWUFBWSxHQUFHcFA7Z0JBQzlCLElBQUlFLElBQUk7b0JBQ0osTUFBTWlRLG1CQUFtQm5TLE9BQU9rQixLQUFLLENBQUNqSSxHQUFHLENBQUNpSixHQUFHdEosSUFBSTtvQkFDakQsTUFBTXdaLG9CQUFvQnBRLE1BQU1FLEVBQUUsSUFBSTZJLHFCQUFxQi9JLE1BQU1FLEVBQUU7b0JBQ25FLElBQUlrUSxxQkFBcUJoVSxnQkFBZ0JpQixnQkFBZ0IsRUFBRTt3QkFDdkQ2USxvQkFBb0I7NEJBQUN0WDt5QkFBSyxFQUFFO29CQUNoQztvQkFDQSxNQUFNeVosYUFBYSxNQUFNdEYsY0FBYy9LLE9BQU9oQyxPQUFPdEIsUUFBUSxFQUFFbUMsYUFBYTRPLGtDQUFrQ2hOLFNBQVN3SCx5QkFBeUIsSUFBSSxDQUFDZ0ksc0JBQXNCRTtvQkFDM0ssSUFBSUMscUJBQXFCaFUsZ0JBQWdCaUIsZ0JBQWdCLEVBQUU7d0JBQ3ZENlEsb0JBQW9COzRCQUFDdFg7eUJBQUs7b0JBQzlCO29CQUNBLElBQUl5WixVQUFVLENBQUNuUSxHQUFHdEosSUFBSSxDQUFDLEVBQUU7d0JBQ3JCbVosUUFBUUcsS0FBSyxHQUFHO3dCQUNoQixJQUFJRCxzQkFBc0I7NEJBQ3RCO3dCQUNKO29CQUNKO29CQUNBLENBQUNBLHdCQUNJdlgsQ0FBQUEsSUFBSTJYLFlBQVluUSxHQUFHdEosSUFBSSxJQUNsQnVaLG1CQUNJM0YsMEJBQTBCMU4sV0FBV1UsTUFBTSxFQUFFNlMsWUFBWW5RLEdBQUd0SixJQUFJLElBQ2hFMkMsSUFBSXVELFdBQVdVLE1BQU0sRUFBRTBDLEdBQUd0SixJQUFJLEVBQUV5WixVQUFVLENBQUNuUSxHQUFHdEosSUFBSSxDQUFDLElBQ3ZEc1AsTUFBTXBKLFdBQVdVLE1BQU0sRUFBRTBDLEdBQUd0SixJQUFJO2dCQUM5QztnQkFDQSxDQUFDc08sY0FBY2tLLGVBQ1YsTUFBTW5CLHlCQUF5Qm1CLFlBQVlhLHNCQUFzQkY7WUFDMUU7UUFDSjtRQUNBLE9BQU9BLFFBQVFHLEtBQUs7SUFDeEI7SUFDQSxNQUFNcFIsbUJBQW1CO1FBQ3JCLEtBQUssTUFBTWxJLFFBQVFvSCxPQUFPcVAsT0FBTyxDQUFFO1lBQy9CLE1BQU1yTixRQUFRdEgsSUFBSXVILFNBQVNySjtZQUMzQm9KLFNBQ0tBLENBQUFBLE1BQU1FLEVBQUUsQ0FBQzBILElBQUksR0FDUjVILE1BQU1FLEVBQUUsQ0FBQzBILElBQUksQ0FBQ2dELEtBQUssQ0FBQyxDQUFDOUssTUFBUSxDQUFDOEYsS0FBSzlGLFFBQ25DLENBQUM4RixLQUFLNUYsTUFBTUUsRUFBRSxDQUFDSixHQUFHLE1BQ3hCZ0IsV0FBV2xLO1FBQ25CO1FBQ0FvSCxPQUFPcVAsT0FBTyxHQUFHLElBQUlwVjtJQUN6QjtJQUNBLE1BQU0yVyxZQUFZLENBQUNoWSxNQUFNaUIsT0FBUyxDQUFDNEksU0FBUy9ELFFBQVEsSUFDL0M5RixDQUFBQSxRQUFRaUIsUUFBUTBCLElBQUlzRixhQUFhakksTUFBTWlCLE9BQ3BDLENBQUM2TSxVQUFVNEwsYUFBYXRVLGVBQWM7SUFDOUMsTUFBTTJDLFlBQVksQ0FBQzNILE9BQU82QixjQUFjcUYsV0FBYUgsb0JBQW9CL0csT0FBT2dILFFBQVE7WUFDcEYsR0FBSTRDLE9BQU9ELEtBQUssR0FDVjlCLGNBQ0F0RyxZQUFZTSxnQkFDUm1ELGlCQUNBOEIsU0FBUzlHLFNBQ0w7Z0JBQUUsQ0FBQ0EsTUFBTSxFQUFFNkI7WUFBYSxJQUN4QkEsWUFBWTtRQUM5QixHQUFHcUYsVUFBVXJGO0lBQ2IsTUFBTTBYLGlCQUFpQixDQUFDM1osT0FBU3dCLFFBQVFNLElBQUlrSSxPQUFPRCxLQUFLLEdBQUc5QixjQUFjN0MsZ0JBQWdCcEYsTUFBTTZKLFNBQVN6QixnQkFBZ0IsR0FBR3RHLElBQUlzRCxnQkFBZ0JwRixNQUFNLEVBQUUsSUFBSSxFQUFFO0lBQzlKLE1BQU1zWSxnQkFBZ0IsQ0FBQ3RZLE1BQU1aLFFBQU8rUSxVQUFVLENBQUMsQ0FBQztRQUM1QyxNQUFNL0csUUFBUXRILElBQUl1SCxTQUFTcko7UUFDM0IsSUFBSXdZLGFBQWFwWjtRQUNqQixJQUFJZ0ssT0FBTztZQUNQLE1BQU1nSixpQkFBaUJoSixNQUFNRSxFQUFFO1lBQy9CLElBQUk4SSxnQkFBZ0I7Z0JBQ2hCLENBQUNBLGVBQWV0TSxRQUFRLElBQ3BCbkQsSUFBSXNGLGFBQWFqSSxNQUFNc1EsZ0JBQWdCbFIsUUFBT2dUO2dCQUNsRG9HLGFBQ0kvSixjQUFjMkQsZUFBZWxKLEdBQUcsS0FBSzVKLGtCQUFrQkYsVUFDakQsS0FDQUE7Z0JBQ1YsSUFBSXlQLGlCQUFpQnVELGVBQWVsSixHQUFHLEdBQUc7b0JBQ3RDOzJCQUFJa0osZUFBZWxKLEdBQUcsQ0FBQ2lILE9BQU87cUJBQUMsQ0FBQ3FILE9BQU8sQ0FBQyxDQUFDb0MsWUFBZUEsVUFBVUMsUUFBUSxHQUFHckIsV0FBV3BNLFFBQVEsQ0FBQ3dOLFVBQVV4YSxLQUFLO2dCQUNwSCxPQUNLLElBQUlnVCxlQUFlcEIsSUFBSSxFQUFFO29CQUMxQixJQUFJaFMsZ0JBQWdCb1QsZUFBZWxKLEdBQUcsR0FBRzt3QkFDckNrSixlQUFlcEIsSUFBSSxDQUFDd0csT0FBTyxDQUFDLENBQUNzQzs0QkFDekIsSUFBSSxDQUFDQSxZQUFZekIsY0FBYyxJQUFJLENBQUN5QixZQUFZaFUsUUFBUSxFQUFFO2dDQUN0RCxJQUFJckcsTUFBTUMsT0FBTyxDQUFDOFksYUFBYTtvQ0FDM0JzQixZQUFZaGEsT0FBTyxHQUFHLENBQUMsQ0FBQzBZLFdBQVduRyxJQUFJLENBQUMsQ0FBQ3BSLE9BQVNBLFNBQVM2WSxZQUFZMWEsS0FBSztnQ0FDaEYsT0FDSztvQ0FDRDBhLFlBQVloYSxPQUFPLEdBQ2YwWSxlQUFlc0IsWUFBWTFhLEtBQUssSUFBSSxDQUFDLENBQUNvWjtnQ0FDOUM7NEJBQ0o7d0JBQ0o7b0JBQ0osT0FDSzt3QkFDRHBHLGVBQWVwQixJQUFJLENBQUN3RyxPQUFPLENBQUMsQ0FBQ3VDLFdBQWNBLFNBQVNqYSxPQUFPLEdBQUdpYSxTQUFTM2EsS0FBSyxLQUFLb1o7b0JBQ3JGO2dCQUNKLE9BQ0ssSUFBSWpLLFlBQVk2RCxlQUFlbEosR0FBRyxHQUFHO29CQUN0Q2tKLGVBQWVsSixHQUFHLENBQUM5SixLQUFLLEdBQUc7Z0JBQy9CLE9BQ0s7b0JBQ0RnVCxlQUFlbEosR0FBRyxDQUFDOUosS0FBSyxHQUFHb1o7b0JBQzNCLElBQUksQ0FBQ3BHLGVBQWVsSixHQUFHLENBQUNoSyxJQUFJLEVBQUU7d0JBQzFCd04sVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7NEJBQ2pCNU07NEJBQ0FnSSxRQUFRaEgsWUFBWWlIO3dCQUN4QjtvQkFDSjtnQkFDSjtZQUNKO1FBQ0o7UUFDQ2tJLENBQUFBLFFBQVFzSSxXQUFXLElBQUl0SSxRQUFRNkosV0FBVyxLQUN2Q3pCLG9CQUFvQnZZLE1BQU13WSxZQUFZckksUUFBUTZKLFdBQVcsRUFBRTdKLFFBQVFzSSxXQUFXLEVBQUU7UUFDcEZ0SSxRQUFROEosY0FBYyxJQUFJQyxRQUFRbGE7SUFDdEM7SUFDQSxNQUFNbWEsWUFBWSxDQUFDbmEsTUFBTVosUUFBTytRO1FBQzVCLElBQUssTUFBTWlLLFlBQVloYixPQUFPO1lBQzFCLElBQUksQ0FBQ0EsT0FBTXVCLGNBQWMsQ0FBQ3laLFdBQVc7Z0JBQ2pDO1lBQ0o7WUFDQSxNQUFNNUIsYUFBYXBaLE1BQUssQ0FBQ2diLFNBQVM7WUFDbEMsTUFBTTFTLFlBQVksQ0FBQyxFQUFFMUgsS0FBSyxDQUFDLEVBQUVvYSxTQUFTLENBQUM7WUFDdkMsTUFBTWhSLFFBQVF0SCxJQUFJdUgsU0FBUzNCO1lBQzFCTixDQUFBQSxPQUFPa0IsS0FBSyxDQUFDakksR0FBRyxDQUFDTCxTQUNkUixTQUFTZ1osZUFDUnBQLFNBQVMsQ0FBQ0EsTUFBTUUsRUFBRSxLQUNuQixDQUFDbkssYUFBYXFaLGNBQ1oyQixVQUFVelMsV0FBVzhRLFlBQVlySSxXQUNqQ21JLGNBQWM1USxXQUFXOFEsWUFBWXJJO1FBQy9DO0lBQ0o7SUFDQSxNQUFNa0ssV0FBVyxDQUFDcmEsTUFBTVosUUFBTytRLFVBQVUsQ0FBQyxDQUFDO1FBQ3ZDLE1BQU0vRyxRQUFRdEgsSUFBSXVILFNBQVNySjtRQUMzQixNQUFNcVUsZUFBZWpOLE9BQU9rQixLQUFLLENBQUNqSSxHQUFHLENBQUNMO1FBQ3RDLE1BQU1zYSxhQUFhdFosWUFBWTVCO1FBQy9CdUQsSUFBSXNGLGFBQWFqSSxNQUFNc2E7UUFDdkIsSUFBSWpHLGNBQWM7WUFDZDNILFVBQVVwRSxLQUFLLENBQUNzRSxJQUFJLENBQUM7Z0JBQ2pCNU07Z0JBQ0FnSSxRQUFRaEgsWUFBWWlIO1lBQ3hCO1lBQ0EsSUFBSSxDQUFDekMsZ0JBQWdCYSxPQUFPLElBQ3hCYixnQkFBZ0JlLFdBQVcsSUFDM0JxUSx5QkFBeUJ2USxPQUFPLElBQ2hDdVEseUJBQXlCclEsV0FBVyxLQUNwQzRKLFFBQVFzSSxXQUFXLEVBQUU7Z0JBQ3JCL0wsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7b0JBQ2pCNU07b0JBQ0F1RyxhQUFhd0osZUFBZTNLLGdCQUFnQjZDO29CQUM1QzVCLFNBQVMyUixVQUFVaFksTUFBTXNhO2dCQUM3QjtZQUNKO1FBQ0osT0FDSztZQUNEbFIsU0FBUyxDQUFDQSxNQUFNRSxFQUFFLElBQUksQ0FBQ2hLLGtCQUFrQmdiLGNBQ25DSCxVQUFVbmEsTUFBTXNhLFlBQVluSyxXQUM1Qm1JLGNBQWN0WSxNQUFNc2EsWUFBWW5LO1FBQzFDO1FBQ0FxQyxVQUFVeFMsTUFBTW9ILFdBQVdzRixVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztZQUFFLEdBQUcxRyxVQUFVO1FBQUM7UUFDaEV3RyxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztZQUNqQjVNLE1BQU1nSyxPQUFPRCxLQUFLLEdBQUcvSixPQUFPNkI7WUFDNUJtRyxRQUFRaEgsWUFBWWlIO1FBQ3hCO0lBQ0o7SUFDQSxNQUFNeEUsV0FBVyxPQUFPN0Q7UUFDcEJvSyxPQUFPRCxLQUFLLEdBQUc7UUFDZixNQUFNbEssU0FBU0QsTUFBTUMsTUFBTTtRQUMzQixJQUFJRyxPQUFPSCxPQUFPRyxJQUFJO1FBQ3RCLElBQUl1YSxzQkFBc0I7UUFDMUIsTUFBTW5SLFFBQVF0SCxJQUFJdUgsU0FBU3JKO1FBQzNCLE1BQU13YSw2QkFBNkIsQ0FBQ2hDO1lBQ2hDK0Isc0JBQ0lFLE9BQU92WCxLQUFLLENBQUNzVixlQUNSclosYUFBYXFaLGVBQWV0VixNQUFNc1YsV0FBV3ZLLE9BQU8sT0FDckRILFVBQVUwSyxZQUFZMVcsSUFBSW1HLGFBQWFqSSxNQUFNd1k7UUFDekQ7UUFDQSxNQUFNa0MsNkJBQTZCL0ksbUJBQW1COUgsU0FBUytILElBQUk7UUFDbkUsTUFBTStJLDRCQUE0QmhKLG1CQUFtQjlILFNBQVM2SixjQUFjO1FBQzVFLElBQUl0SyxPQUFPO1lBQ1AsSUFBSUo7WUFDSixJQUFJckM7WUFDSixNQUFNNlIsYUFBYTNZLE9BQU9YLElBQUksR0FDeEI0UixjQUFjMUgsTUFBTUUsRUFBRSxJQUN0QjNKLGNBQWNDO1lBQ3BCLE1BQU02UyxjQUFjN1MsTUFBTVYsSUFBSSxLQUFLaUUsT0FBT0MsSUFBSSxJQUFJeEQsTUFBTVYsSUFBSSxLQUFLaUUsT0FBT0UsU0FBUztZQUNqRixNQUFNdVgsdUJBQXVCLENBQUVySSxjQUFjbkosTUFBTUUsRUFBRSxLQUNqRCxDQUFDTyxTQUFTc04sUUFBUSxJQUNsQixDQUFDclYsSUFBSW9FLFdBQVdVLE1BQU0sRUFBRTVHLFNBQ3hCLENBQUNvSixNQUFNRSxFQUFFLENBQUN1UixJQUFJLElBQ2RySCxlQUFlZixhQUFhM1EsSUFBSW9FLFdBQVdNLGFBQWEsRUFBRXhHLE9BQU9rRyxXQUFXdU4sV0FBVyxFQUFFa0gsMkJBQTJCRDtZQUN4SCxNQUFNSSxVQUFVdEksVUFBVXhTLE1BQU1vSCxRQUFRcUw7WUFDeEM5UCxJQUFJc0YsYUFBYWpJLE1BQU13WTtZQUN2QixJQUFJL0YsYUFBYTtnQkFDYnJKLE1BQU1FLEVBQUUsQ0FBQzlGLE1BQU0sSUFBSTRGLE1BQU1FLEVBQUUsQ0FBQzlGLE1BQU0sQ0FBQzVEO2dCQUNuQzhXLHNCQUFzQkEsbUJBQW1CO1lBQzdDLE9BQ0ssSUFBSXROLE1BQU1FLEVBQUUsQ0FBQzdGLFFBQVEsRUFBRTtnQkFDeEIyRixNQUFNRSxFQUFFLENBQUM3RixRQUFRLENBQUM3RDtZQUN0QjtZQUNBLE1BQU0rSSxhQUFhNFAsb0JBQW9CdlksTUFBTXdZLFlBQVkvRjtZQUN6RCxNQUFNaUcsZUFBZSxDQUFDcEssY0FBYzNGLGVBQWVtUztZQUNuRCxDQUFDckksZUFDRy9GLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUNqQjVNO2dCQUNBZCxNQUFNVSxNQUFNVixJQUFJO2dCQUNoQjhJLFFBQVFoSCxZQUFZaUg7WUFDeEI7WUFDSixJQUFJMlMsc0JBQXNCO2dCQUN0QixJQUFJcFYsZ0JBQWdCbUIsT0FBTyxJQUFJaVEseUJBQXlCalEsT0FBTyxFQUFFO29CQUM3RCxJQUFJa0QsU0FBUytILElBQUksS0FBSyxVQUFVO3dCQUM1QixJQUFJYSxhQUFhOzRCQUNiekw7d0JBQ0o7b0JBQ0osT0FDSyxJQUFJLENBQUN5TCxhQUFhO3dCQUNuQnpMO29CQUNKO2dCQUNKO2dCQUNBLE9BQVEwUixnQkFDSmhNLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO29CQUFFNU07b0JBQU0sR0FBSThhLFVBQVUsQ0FBQyxJQUFJblMsVUFBVTtnQkFBRTtZQUNwRTtZQUNBLENBQUM4SixlQUFlcUksV0FBV3BPLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUFFLEdBQUcxRyxVQUFVO1lBQUM7WUFDaEUsSUFBSTJELFNBQVNzTixRQUFRLEVBQUU7Z0JBQ25CLE1BQU0sRUFBRXZRLE1BQU0sRUFBRSxHQUFHLE1BQU13USxXQUFXO29CQUFDcFg7aUJBQUs7Z0JBQzFDd2EsMkJBQTJCaEM7Z0JBQzNCLElBQUkrQixxQkFBcUI7b0JBQ3JCLE1BQU1RLDRCQUE0QmhJLGtCQUFrQjdNLFdBQVdVLE1BQU0sRUFBRXlDLFNBQVNySjtvQkFDaEYsTUFBTWdiLG9CQUFvQmpJLGtCQUFrQm5NLFFBQVF5QyxTQUFTMFIsMEJBQTBCL2EsSUFBSSxJQUFJQTtvQkFDL0ZnSixRQUFRZ1Msa0JBQWtCaFMsS0FBSztvQkFDL0JoSixPQUFPZ2Isa0JBQWtCaGIsSUFBSTtvQkFDN0IyRyxVQUFVMkgsY0FBYzFIO2dCQUM1QjtZQUNKLE9BQ0s7Z0JBQ0QwUSxvQkFBb0I7b0JBQUN0WDtpQkFBSyxFQUFFO2dCQUM1QmdKLFFBQVEsQ0FBQyxNQUFNbUwsY0FBYy9LLE9BQU9oQyxPQUFPdEIsUUFBUSxFQUFFbUMsYUFBYTRPLGtDQUFrQ2hOLFNBQVN3SCx5QkFBeUIsRUFBRSxDQUFDclIsS0FBSztnQkFDOUlzWCxvQkFBb0I7b0JBQUN0WDtpQkFBSztnQkFDMUJ3YSwyQkFBMkJoQztnQkFDM0IsSUFBSStCLHFCQUFxQjtvQkFDckIsSUFBSXZSLE9BQU87d0JBQ1ByQyxVQUFVO29CQUNkLE9BQ0ssSUFBSW5CLGdCQUFnQm1CLE9BQU8sSUFDNUJpUSx5QkFBeUJqUSxPQUFPLEVBQUU7d0JBQ2xDQSxVQUFVLE1BQU0wUSx5QkFBeUJoTyxTQUFTO29CQUN0RDtnQkFDSjtZQUNKO1lBQ0EsSUFBSWtSLHFCQUFxQjtnQkFDckJuUixNQUFNRSxFQUFFLENBQUN1UixJQUFJLElBQ1RYLFFBQVE5USxNQUFNRSxFQUFFLENBQUN1UixJQUFJO2dCQUN6QjlCLG9CQUFvQi9ZLE1BQU0yRyxTQUFTcUMsT0FBT0w7WUFDOUM7UUFDSjtJQUNKO0lBQ0EsTUFBTXNTLGNBQWMsQ0FBQy9SLEtBQUszSDtRQUN0QixJQUFJTyxJQUFJb0UsV0FBV1UsTUFBTSxFQUFFckYsUUFBUTJILElBQUlLLEtBQUssRUFBRTtZQUMxQ0wsSUFBSUssS0FBSztZQUNULE9BQU87UUFDWDtRQUNBO0lBQ0o7SUFDQSxNQUFNMlEsVUFBVSxPQUFPbGEsTUFBTW1RLFVBQVUsQ0FBQyxDQUFDO1FBQ3JDLElBQUl4SjtRQUNKLElBQUl1UDtRQUNKLE1BQU1nRixhQUFhOU4sc0JBQXNCcE47UUFDekMsSUFBSTZKLFNBQVNzTixRQUFRLEVBQUU7WUFDbkIsTUFBTXZRLFNBQVMsTUFBTXdTLDRCQUE0QnpYLFlBQVkzQixRQUFRQSxPQUFPa2I7WUFDNUV2VSxVQUFVMkgsY0FBYzFIO1lBQ3hCc1AsbUJBQW1CbFcsT0FDYixDQUFDa2IsV0FBVy9PLElBQUksQ0FBQyxDQUFDbk0sT0FBUzhCLElBQUk4RSxRQUFRNUcsU0FDdkMyRztRQUNWLE9BQ0ssSUFBSTNHLE1BQU07WUFDWGtXLG1CQUFtQixDQUFDLE1BQU1pRixRQUFRdlgsR0FBRyxDQUFDc1gsV0FBV3pULEdBQUcsQ0FBQyxPQUFPQztnQkFDeEQsTUFBTTBCLFFBQVF0SCxJQUFJdUgsU0FBUzNCO2dCQUMzQixPQUFPLE1BQU0yUCx5QkFBeUJqTyxTQUFTQSxNQUFNRSxFQUFFLEdBQUc7b0JBQUUsQ0FBQzVCLFVBQVUsRUFBRTBCO2dCQUFNLElBQUlBO1lBQ3ZGLEdBQUUsRUFBRzRLLEtBQUssQ0FBQ3RTO1lBQ1gsQ0FBRSxFQUFDd1Usb0JBQW9CLENBQUNoUSxXQUFXUyxPQUFPLEtBQUtLO1FBQ25ELE9BQ0s7WUFDRGtQLG1CQUFtQnZQLFVBQVUsTUFBTTBRLHlCQUF5QmhPO1FBQ2hFO1FBQ0FxRCxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztZQUNqQixHQUFJLENBQUMxRixTQUFTbEgsU0FDVCxDQUFDd0YsZ0JBQWdCbUIsT0FBTyxJQUFJaVEseUJBQXlCalEsT0FBTyxLQUN6REEsWUFBWVQsV0FBV1MsT0FBTyxHQUNoQyxDQUFDLElBQ0Q7Z0JBQUUzRztZQUFLLENBQUM7WUFDZCxHQUFJNkosU0FBU3NOLFFBQVEsSUFBSSxDQUFDblgsT0FBTztnQkFBRTJHO1lBQVEsSUFBSSxDQUFDLENBQUM7WUFDakRDLFFBQVFWLFdBQVdVLE1BQU07UUFDN0I7UUFDQXVKLFFBQVFpTCxXQUFXLElBQ2YsQ0FBQ2xGLG9CQUNEdEQsc0JBQXNCdkosU0FBUzRSLGFBQWFqYixPQUFPa2IsYUFBYTlULE9BQU8yQyxLQUFLO1FBQ2hGLE9BQU9tTTtJQUNYO0lBQ0EsTUFBTXdELFlBQVksQ0FBQ3dCO1FBQ2YsTUFBTWxULFNBQVM7WUFDWCxHQUFJZ0MsT0FBT0QsS0FBSyxHQUFHOUIsY0FBYzdDLGNBQWM7UUFDbkQ7UUFDQSxPQUFPekQsWUFBWXVaLGNBQ2JsVCxTQUNBZCxTQUFTZ1UsY0FDTHBaLElBQUlrRyxRQUFRa1QsY0FDWkEsV0FBV3pULEdBQUcsQ0FBQyxDQUFDekgsT0FBUzhCLElBQUlrRyxRQUFRaEk7SUFDbkQ7SUFDQSxNQUFNcWIsZ0JBQWdCLENBQUNyYixNQUFNK0UsWUFBZTtZQUN4QzhELFNBQVMsQ0FBQyxDQUFDL0csSUFBSSxDQUFDaUQsYUFBYW1CLFVBQVMsRUFBR1UsTUFBTSxFQUFFNUc7WUFDakRxRyxTQUFTLENBQUMsQ0FBQ3ZFLElBQUksQ0FBQ2lELGFBQWFtQixVQUFTLEVBQUdLLFdBQVcsRUFBRXZHO1lBQ3REZ0osT0FBT2xILElBQUksQ0FBQ2lELGFBQWFtQixVQUFTLEVBQUdVLE1BQU0sRUFBRTVHO1lBQzdDMEcsY0FBYyxDQUFDLENBQUM1RSxJQUFJb0UsV0FBV08sZ0JBQWdCLEVBQUV6RztZQUNqRCtJLFdBQVcsQ0FBQyxDQUFDakgsSUFBSSxDQUFDaUQsYUFBYW1CLFVBQVMsRUFBR00sYUFBYSxFQUFFeEc7UUFDOUQ7SUFDQSxNQUFNc2IsY0FBYyxDQUFDdGI7UUFDakJBLFFBQ0lvTixzQkFBc0JwTixNQUFNd1gsT0FBTyxDQUFDLENBQUMrRCxZQUFjak0sTUFBTXBKLFdBQVdVLE1BQU0sRUFBRTJVO1FBQ2hGN08sVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDakJoRyxRQUFRNUcsT0FBT2tHLFdBQVdVLE1BQU0sR0FBRyxDQUFDO1FBQ3hDO0lBQ0o7SUFDQSxNQUFNa0csV0FBVyxDQUFDOU0sTUFBTWdKLE9BQU9tSDtRQUMzQixNQUFNakgsTUFBTSxDQUFDcEgsSUFBSXVILFNBQVNySixNQUFNO1lBQUVzSixJQUFJLENBQUM7UUFBRSxHQUFHQSxFQUFFLElBQUksQ0FBQyxHQUFHSixHQUFHO1FBQ3pELE1BQU1zUyxlQUFlMVosSUFBSW9FLFdBQVdVLE1BQU0sRUFBRTVHLFNBQVMsQ0FBQztRQUN0RCx1RUFBdUU7UUFDdkUsTUFBTSxFQUFFa0osS0FBS3VTLFVBQVUsRUFBRS9SLE9BQU8sRUFBRXhLLElBQUksRUFBRSxHQUFHd2MsaUJBQWlCLEdBQUdGO1FBQy9EN1ksSUFBSXVELFdBQVdVLE1BQU0sRUFBRTVHLE1BQU07WUFDekIsR0FBRzBiLGVBQWU7WUFDbEIsR0FBRzFTLEtBQUs7WUFDUkU7UUFDSjtRQUNBd0QsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDakI1TTtZQUNBNEcsUUFBUVYsV0FBV1UsTUFBTTtZQUN6QkQsU0FBUztRQUNiO1FBQ0F3SixXQUFXQSxRQUFRaUwsV0FBVyxJQUFJbFMsT0FBT0EsSUFBSUssS0FBSyxJQUFJTCxJQUFJSyxLQUFLO0lBQ25FO0lBQ0EsTUFBTWhDLFFBQVEsQ0FBQ3ZILE1BQU1pQyxlQUFpQnVNLFdBQVd4TyxRQUMzQzBNLFVBQVVDLEtBQUssQ0FBQ2EsU0FBUyxDQUFDO1lBQ3hCWixNQUFNLENBQUMrTyxVQUFZM2IsS0FBSytILFVBQVVsRyxXQUFXSSxlQUFlMFo7UUFDaEUsS0FDRTVULFVBQVUvSCxNQUFNaUMsY0FBYztJQUNwQyxNQUFNNEUsYUFBYSxDQUFDbkMsUUFBVWdJLFVBQVVDLEtBQUssQ0FBQ2EsU0FBUyxDQUFDO1lBQ3BEWixNQUFNLENBQUM3SDtnQkFDSCxJQUFJc08sc0JBQXNCM08sTUFBTTFFLElBQUksRUFBRStFLFVBQVUvRSxJQUFJLEVBQUUwRSxNQUFNcUIsS0FBSyxLQUM3RG9OLHNCQUFzQnBPLFdBQVdMLE1BQU1LLFNBQVMsSUFBSVMsaUJBQWlCb1csZUFBZWxYLE1BQU1tWCxZQUFZLEdBQUc7b0JBQ3pHblgsTUFBTXFDLFFBQVEsQ0FBQzt3QkFDWGlCLFFBQVE7NEJBQUUsR0FBR0MsV0FBVzt3QkFBQzt3QkFDekIsR0FBRy9CLFVBQVU7d0JBQ2IsR0FBR25CLFNBQVM7b0JBQ2hCO2dCQUNKO1lBQ0o7UUFDSixHQUFHMkksV0FBVztJQUNkLE1BQU1GLFlBQVksQ0FBQzlJO1FBQ2ZzRixPQUFPRCxLQUFLLEdBQUc7UUFDZjZNLDJCQUEyQjtZQUN2QixHQUFHQSx3QkFBd0I7WUFDM0IsR0FBR2xTLE1BQU1LLFNBQVM7UUFDdEI7UUFDQSxPQUFPOEIsV0FBVztZQUNkLEdBQUduQyxLQUFLO1lBQ1JLLFdBQVc2UjtRQUNmO0lBQ0o7SUFDQSxNQUFNMU0sYUFBYSxDQUFDbEssTUFBTW1RLFVBQVUsQ0FBQyxDQUFDO1FBQ2xDLEtBQUssTUFBTXpJLGFBQWExSCxPQUFPb04sc0JBQXNCcE4sUUFBUW9ILE9BQU8yQyxLQUFLLENBQUU7WUFDdkUzQyxPQUFPMkMsS0FBSyxDQUFDK1IsTUFBTSxDQUFDcFU7WUFDcEJOLE9BQU9rQixLQUFLLENBQUN3VCxNQUFNLENBQUNwVTtZQUNwQixJQUFJLENBQUN5SSxRQUFRNEwsU0FBUyxFQUFFO2dCQUNwQnpNLE1BQU1qRyxTQUFTM0I7Z0JBQ2Y0SCxNQUFNckgsYUFBYVA7WUFDdkI7WUFDQSxDQUFDeUksUUFBUTZMLFNBQVMsSUFBSTFNLE1BQU1wSixXQUFXVSxNQUFNLEVBQUVjO1lBQy9DLENBQUN5SSxRQUFROEwsU0FBUyxJQUFJM00sTUFBTXBKLFdBQVdLLFdBQVcsRUFBRW1CO1lBQ3BELENBQUN5SSxRQUFRK0wsV0FBVyxJQUFJNU0sTUFBTXBKLFdBQVdNLGFBQWEsRUFBRWtCO1lBQ3hELENBQUN5SSxRQUFRZ00sZ0JBQWdCLElBQ3JCN00sTUFBTXBKLFdBQVdPLGdCQUFnQixFQUFFaUI7WUFDdkMsQ0FBQ21DLFNBQVN6QixnQkFBZ0IsSUFDdEIsQ0FBQytILFFBQVFpTSxnQkFBZ0IsSUFDekI5TSxNQUFNbEssZ0JBQWdCc0M7UUFDOUI7UUFDQWdGLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQ2pCNUUsUUFBUWhILFlBQVlpSDtRQUN4QjtRQUNBeUUsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDakIsR0FBRzFHLFVBQVU7WUFDYixHQUFJLENBQUNpSyxRQUFROEwsU0FBUyxHQUFHLENBQUMsSUFBSTtnQkFBRTVWLFNBQVMyUjtZQUFZLENBQUM7UUFDMUQ7UUFDQSxDQUFDN0gsUUFBUWtNLFdBQVcsSUFBSXJWO0lBQzVCO0lBQ0EsTUFBTW1ELG9CQUFvQixDQUFDLEVBQUVyRSxRQUFRLEVBQUU5RixJQUFJLEVBQUc7UUFDMUMsSUFBSSxVQUFXOEYsYUFBYWtFLE9BQU9ELEtBQUssSUFDcEMsQ0FBQyxDQUFDakUsWUFDRnNCLE9BQU90QixRQUFRLENBQUN6RixHQUFHLENBQUNMLE9BQU87WUFDM0I4RixXQUFXc0IsT0FBT3RCLFFBQVEsQ0FBQzBCLEdBQUcsQ0FBQ3hILFFBQVFvSCxPQUFPdEIsUUFBUSxDQUFDZ1csTUFBTSxDQUFDOWI7UUFDbEU7SUFDSjtJQUNBLE1BQU15SSxXQUFXLENBQUN6SSxNQUFNbVEsVUFBVSxDQUFDLENBQUM7UUFDaEMsSUFBSS9HLFFBQVF0SCxJQUFJdUgsU0FBU3JKO1FBQ3pCLE1BQU1zYyxvQkFBb0JqYSxVQUFVOE4sUUFBUXJLLFFBQVEsS0FBS3pELFVBQVV3SCxTQUFTL0QsUUFBUTtRQUNwRm5ELElBQUkwRyxTQUFTckosTUFBTTtZQUNmLEdBQUlvSixTQUFTLENBQUMsQ0FBQztZQUNmRSxJQUFJO2dCQUNBLEdBQUlGLFNBQVNBLE1BQU1FLEVBQUUsR0FBR0YsTUFBTUUsRUFBRSxHQUFHO29CQUFFSixLQUFLO3dCQUFFbEo7b0JBQUs7Z0JBQUUsQ0FBQztnQkFDcERBO2dCQUNBK0osT0FBTztnQkFDUCxHQUFHb0csT0FBTztZQUNkO1FBQ0o7UUFDQS9JLE9BQU8yQyxLQUFLLENBQUN2QyxHQUFHLENBQUN4SDtRQUNqQixJQUFJb0osT0FBTztZQUNQZSxrQkFBa0I7Z0JBQ2RyRSxVQUFVekQsVUFBVThOLFFBQVFySyxRQUFRLElBQzlCcUssUUFBUXJLLFFBQVEsR0FDaEIrRCxTQUFTL0QsUUFBUTtnQkFDdkI5RjtZQUNKO1FBQ0osT0FDSztZQUNEbVksb0JBQW9CblksTUFBTSxNQUFNbVEsUUFBUS9RLEtBQUs7UUFDakQ7UUFDQSxPQUFPO1lBQ0gsR0FBSWtkLG9CQUNFO2dCQUFFeFcsVUFBVXFLLFFBQVFySyxRQUFRLElBQUkrRCxTQUFTL0QsUUFBUTtZQUFDLElBQ2xELENBQUMsQ0FBQztZQUNSLEdBQUkrRCxTQUFTMFMsV0FBVyxHQUNsQjtnQkFDRXBZLFVBQVUsQ0FBQyxDQUFDZ00sUUFBUWhNLFFBQVE7Z0JBQzVCSixLQUFLeU4sYUFBYXJCLFFBQVFwTSxHQUFHO2dCQUM3QkQsS0FBSzBOLGFBQWFyQixRQUFRck0sR0FBRztnQkFDN0JHLFdBQVd1TixhQUFhckIsUUFBUWxNLFNBQVM7Z0JBQ3pDRCxXQUFXd04sYUFBYXJCLFFBQVFuTSxTQUFTO2dCQUN6Q0UsU0FBU3NOLGFBQWFyQixRQUFRak0sT0FBTztZQUN6QyxJQUNFLENBQUMsQ0FBQztZQUNSbEU7WUFDQXlEO1lBQ0FELFFBQVFDO1lBQ1J5RixLQUFLLENBQUNBO2dCQUNGLElBQUlBLEtBQUs7b0JBQ0xULFNBQVN6SSxNQUFNbVE7b0JBQ2YvRyxRQUFRdEgsSUFBSXVILFNBQVNySjtvQkFDckIsTUFBTXdjLFdBQVc3YSxZQUFZdUgsSUFBSTlKLEtBQUssSUFDaEM4SixJQUFJdVQsZ0JBQWdCLEdBQ2hCdlQsSUFBSXVULGdCQUFnQixDQUFDLHdCQUF3QixDQUFDLEVBQUUsSUFBSXZULE1BQ3BEQSxNQUNKQTtvQkFDTixNQUFNd1Qsa0JBQWtCM04sa0JBQWtCeU47b0JBQzFDLE1BQU14TCxPQUFPNUgsTUFBTUUsRUFBRSxDQUFDMEgsSUFBSSxJQUFJLEVBQUU7b0JBQ2hDLElBQUkwTCxrQkFDRTFMLEtBQUtxQixJQUFJLENBQUMsQ0FBQ2pDLFNBQVdBLFdBQVdvTSxZQUNqQ0EsYUFBYXBULE1BQU1FLEVBQUUsQ0FBQ0osR0FBRyxFQUFFO3dCQUM3QjtvQkFDSjtvQkFDQXZHLElBQUkwRyxTQUFTckosTUFBTTt3QkFDZnNKLElBQUk7NEJBQ0EsR0FBR0YsTUFBTUUsRUFBRTs0QkFDWCxHQUFJb1Qsa0JBQ0U7Z0NBQ0UxTCxNQUFNO3VDQUNDQSxLQUFLdlAsTUFBTSxDQUFDdU47b0NBQ2Z3Tjt1Q0FDSS9jLE1BQU1DLE9BQU8sQ0FBQ29DLElBQUlzRCxnQkFBZ0JwRixTQUFTO3dDQUFDLENBQUM7cUNBQUUsR0FBRyxFQUFFO2lDQUMzRDtnQ0FDRGtKLEtBQUs7b0NBQUVoSyxNQUFNc2QsU0FBU3RkLElBQUk7b0NBQUVjO2dDQUFLOzRCQUNyQyxJQUNFO2dDQUFFa0osS0FBS3NUOzRCQUFTLENBQUM7d0JBQzNCO29CQUNKO29CQUNBckUsb0JBQW9CblksTUFBTSxPQUFPNkIsV0FBVzJhO2dCQUNoRCxPQUNLO29CQUNEcFQsUUFBUXRILElBQUl1SCxTQUFTckosTUFBTSxDQUFDO29CQUM1QixJQUFJb0osTUFBTUUsRUFBRSxFQUFFO3dCQUNWRixNQUFNRSxFQUFFLENBQUNTLEtBQUssR0FBRztvQkFDckI7b0JBQ0NGLENBQUFBLFNBQVN6QixnQkFBZ0IsSUFBSStILFFBQVEvSCxnQkFBZ0IsS0FDbEQsQ0FBRWpJLENBQUFBLG1CQUFtQmlILE9BQU9rQixLQUFLLEVBQUV0SSxTQUFTZ0ssT0FBT0MsTUFBTSxLQUN6RDdDLE9BQU9xUCxPQUFPLENBQUNqUCxHQUFHLENBQUN4SDtnQkFDM0I7WUFDSjtRQUNKO0lBQ0o7SUFDQSxNQUFNMmMsY0FBYyxJQUFNOVMsU0FBU3VNLGdCQUFnQixJQUMvQ3hELHNCQUFzQnZKLFNBQVM0UixhQUFhN1QsT0FBTzJDLEtBQUs7SUFDNUQsTUFBTTZTLGVBQWUsQ0FBQzlXO1FBQ2xCLElBQUl6RCxVQUFVeUQsV0FBVztZQUNyQjRHLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUFFOUc7WUFBUztZQUNoQzhNLHNCQUFzQnZKLFNBQVMsQ0FBQ0gsS0FBS2xKO2dCQUNqQyxNQUFNOFMsZUFBZWhSLElBQUl1SCxTQUFTcko7Z0JBQ2xDLElBQUk4UyxjQUFjO29CQUNkNUosSUFBSXBELFFBQVEsR0FBR2dOLGFBQWF4SixFQUFFLENBQUN4RCxRQUFRLElBQUlBO29CQUMzQyxJQUFJckcsTUFBTUMsT0FBTyxDQUFDb1QsYUFBYXhKLEVBQUUsQ0FBQzBILElBQUksR0FBRzt3QkFDckM4QixhQUFheEosRUFBRSxDQUFDMEgsSUFBSSxDQUFDd0csT0FBTyxDQUFDLENBQUNqRDs0QkFDMUJBLFNBQVN6TyxRQUFRLEdBQUdnTixhQUFheEosRUFBRSxDQUFDeEQsUUFBUSxJQUFJQTt3QkFDcEQ7b0JBQ0o7Z0JBQ0o7WUFDSixHQUFHLEdBQUc7UUFDVjtJQUNKO0lBQ0EsTUFBTTJGLGVBQWUsQ0FBQ29SLFNBQVNDLFlBQWMsT0FBT0M7WUFDaEQsSUFBSUMsZUFBZW5iO1lBQ25CLElBQUlrYixHQUFHO2dCQUNIQSxFQUFFRSxjQUFjLElBQUlGLEVBQUVFLGNBQWM7Z0JBQ3BDRixFQUFFRyxPQUFPLElBQ0xILEVBQUVHLE9BQU87WUFDakI7WUFDQSxJQUFJckYsY0FBYzdXLFlBQVlpSDtZQUM5QnlFLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUNqQjRKLGNBQWM7WUFDbEI7WUFDQSxJQUFJM00sU0FBU3NOLFFBQVEsRUFBRTtnQkFDbkIsTUFBTSxFQUFFdlEsTUFBTSxFQUFFb0IsTUFBTSxFQUFFLEdBQUcsTUFBTW9QO2dCQUNqQ2xSLFdBQVdVLE1BQU0sR0FBR0E7Z0JBQ3BCaVIsY0FBYzdQO1lBQ2xCLE9BQ0s7Z0JBQ0QsTUFBTXFQLHlCQUF5QmhPO1lBQ25DO1lBQ0EsSUFBSWpDLE9BQU90QixRQUFRLENBQUNxWCxJQUFJLEVBQUU7Z0JBQ3RCLEtBQUssTUFBTW5kLFFBQVFvSCxPQUFPdEIsUUFBUSxDQUFFO29CQUNoQ25ELElBQUlrVixhQUFhN1gsTUFBTTZCO2dCQUMzQjtZQUNKO1lBQ0F5TixNQUFNcEosV0FBV1UsTUFBTSxFQUFFO1lBQ3pCLElBQUkwSCxjQUFjcEksV0FBV1UsTUFBTSxHQUFHO2dCQUNsQzhGLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO29CQUNqQmhHLFFBQVEsQ0FBQztnQkFDYjtnQkFDQSxJQUFJO29CQUNBLE1BQU1pVyxRQUFRaEYsYUFBYWtGO2dCQUMvQixFQUNBLE9BQU8vVCxPQUFPO29CQUNWZ1UsZUFBZWhVO2dCQUNuQjtZQUNKLE9BQ0s7Z0JBQ0QsSUFBSThULFdBQVc7b0JBQ1gsTUFBTUEsVUFBVTt3QkFBRSxHQUFHNVcsV0FBV1UsTUFBTTtvQkFBQyxHQUFHbVc7Z0JBQzlDO2dCQUNBSjtnQkFDQTFGLFdBQVcwRjtZQUNmO1lBQ0FqUSxVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztnQkFDakI2RyxhQUFhO2dCQUNiK0MsY0FBYztnQkFDZDNKLG9CQUFvQnlCLGNBQWNwSSxXQUFXVSxNQUFNLEtBQUssQ0FBQ29XO2dCQUN6RDFHLGFBQWFwUSxXQUFXb1EsV0FBVyxHQUFHO2dCQUN0QzFQLFFBQVFWLFdBQVdVLE1BQU07WUFDN0I7WUFDQSxJQUFJb1csY0FBYztnQkFDZCxNQUFNQTtZQUNWO1FBQ0o7SUFDQSxNQUFNSSxhQUFhLENBQUNwZCxNQUFNbVEsVUFBVSxDQUFDLENBQUM7UUFDbEMsSUFBSXJPLElBQUl1SCxTQUFTckosT0FBTztZQUNwQixJQUFJMkIsWUFBWXdPLFFBQVFsTyxZQUFZLEdBQUc7Z0JBQ25Db1ksU0FBU3JhLE1BQU1nQixZQUFZYyxJQUFJc0QsZ0JBQWdCcEY7WUFDbkQsT0FDSztnQkFDRHFhLFNBQVNyYSxNQUFNbVEsUUFBUWxPLFlBQVk7Z0JBQ25DVSxJQUFJeUMsZ0JBQWdCcEYsTUFBTWdCLFlBQVltUCxRQUFRbE8sWUFBWTtZQUM5RDtZQUNBLElBQUksQ0FBQ2tPLFFBQVErTCxXQUFXLEVBQUU7Z0JBQ3RCNU0sTUFBTXBKLFdBQVdNLGFBQWEsRUFBRXhHO1lBQ3BDO1lBQ0EsSUFBSSxDQUFDbVEsUUFBUThMLFNBQVMsRUFBRTtnQkFDcEIzTSxNQUFNcEosV0FBV0ssV0FBVyxFQUFFdkc7Z0JBQzlCa0csV0FBV0csT0FBTyxHQUFHOEosUUFBUWxPLFlBQVksR0FDbkMrVixVQUFVaFksTUFBTWdCLFlBQVljLElBQUlzRCxnQkFBZ0JwRixVQUNoRGdZO1lBQ1Y7WUFDQSxJQUFJLENBQUM3SCxRQUFRNkwsU0FBUyxFQUFFO2dCQUNwQjFNLE1BQU1wSixXQUFXVSxNQUFNLEVBQUU1RztnQkFDekJ3RixnQkFBZ0JtQixPQUFPLElBQUlLO1lBQy9CO1lBQ0EwRixVQUFVQyxLQUFLLENBQUNDLElBQUksQ0FBQztnQkFBRSxHQUFHMUcsVUFBVTtZQUFDO1FBQ3pDO0lBQ0o7SUFDQSxNQUFNbVgsU0FBUyxDQUFDaFcsWUFBWWlXLG1CQUFtQixDQUFDLENBQUM7UUFDN0MsTUFBTUMsZ0JBQWdCbFcsYUFBYXJHLFlBQVlxRyxjQUFjakM7UUFDN0QsTUFBTW9ZLHFCQUFxQnhjLFlBQVl1YztRQUN2QyxNQUFNRSxxQkFBcUJuUCxjQUFjakg7UUFDekMsTUFBTVcsU0FBU3lWLHFCQUFxQnJZLGlCQUFpQm9ZO1FBQ3JELElBQUksQ0FBQ0YsaUJBQWlCSSxpQkFBaUIsRUFBRTtZQUNyQ3RZLGlCQUFpQm1ZO1FBQ3JCO1FBQ0EsSUFBSSxDQUFDRCxpQkFBaUJLLFVBQVUsRUFBRTtZQUM5QixJQUFJTCxpQkFBaUJNLGVBQWUsRUFBRTtnQkFDbEMsTUFBTUMsZ0JBQWdCLElBQUl4YyxJQUFJO3VCQUN2QitGLE9BQU8yQyxLQUFLO3VCQUNaMUUsT0FBT29GLElBQUksQ0FBQ3NGLGVBQWUzSyxnQkFBZ0I2QztpQkFDakQ7Z0JBQ0QsS0FBSyxNQUFNUCxhQUFhakksTUFBTThYLElBQUksQ0FBQ3NHLGVBQWdCO29CQUMvQy9iLElBQUlvRSxXQUFXSyxXQUFXLEVBQUVtQixhQUN0Qi9FLElBQUlxRixRQUFRTixXQUFXNUYsSUFBSW1HLGFBQWFQLGNBQ3hDMlMsU0FBUzNTLFdBQVc1RixJQUFJa0csUUFBUU47Z0JBQzFDO1lBQ0osT0FDSztnQkFDRCxJQUFJOUcsU0FBU2UsWUFBWTBGLGFBQWE7b0JBQ2xDLEtBQUssTUFBTXJILFFBQVFvSCxPQUFPMkMsS0FBSyxDQUFFO3dCQUM3QixNQUFNWCxRQUFRdEgsSUFBSXVILFNBQVNySjt3QkFDM0IsSUFBSW9KLFNBQVNBLE1BQU1FLEVBQUUsRUFBRTs0QkFDbkIsTUFBTThJLGlCQUFpQjNTLE1BQU1DLE9BQU8sQ0FBQzBKLE1BQU1FLEVBQUUsQ0FBQzBILElBQUksSUFDNUM1SCxNQUFNRSxFQUFFLENBQUMwSCxJQUFJLENBQUMsRUFBRSxHQUNoQjVILE1BQU1FLEVBQUUsQ0FBQ0osR0FBRzs0QkFDbEIsSUFBSXVGLGNBQWMyRCxpQkFBaUI7Z0NBQy9CLE1BQU0wTCxPQUFPMUwsZUFBZTJMLE9BQU8sQ0FBQztnQ0FDcEMsSUFBSUQsTUFBTTtvQ0FDTkEsS0FBS0UsS0FBSztvQ0FDVjtnQ0FDSjs0QkFDSjt3QkFDSjtvQkFDSjtnQkFDSjtnQkFDQSxLQUFLLE1BQU10VyxhQUFhTixPQUFPMkMsS0FBSyxDQUFFO29CQUNsQ3NRLFNBQVMzUyxXQUFXNUYsSUFBSWtHLFFBQVFOO2dCQUNwQztZQUNKO1lBQ0FPLGNBQWNqSCxZQUFZZ0g7WUFDMUIwRSxVQUFVcEUsS0FBSyxDQUFDc0UsSUFBSSxDQUFDO2dCQUNqQjVFLFFBQVE7b0JBQUUsR0FBR0EsTUFBTTtnQkFBQztZQUN4QjtZQUNBMEUsVUFBVUMsS0FBSyxDQUFDQyxJQUFJLENBQUM7Z0JBQ2pCNUUsUUFBUTtvQkFBRSxHQUFHQSxNQUFNO2dCQUFDO1lBQ3hCO1FBQ0o7UUFDQVosU0FBUztZQUNMMkMsT0FBT3VULGlCQUFpQk0sZUFBZSxHQUFHeFcsT0FBTzJDLEtBQUssR0FBRyxJQUFJMUk7WUFDN0RvVixTQUFTLElBQUlwVjtZQUNiaUgsT0FBTyxJQUFJakg7WUFDWHlFLFVBQVUsSUFBSXpFO1lBQ2RrRyxPQUFPLElBQUlsRztZQUNYc0csVUFBVTtZQUNWNEIsT0FBTztRQUNYO1FBQ0FTLE9BQU9ELEtBQUssR0FDUixDQUFDdkUsZ0JBQWdCbUIsT0FBTyxJQUNwQixDQUFDLENBQUMyVyxpQkFBaUJqQixXQUFXLElBQzlCLENBQUMsQ0FBQ2lCLGlCQUFpQk0sZUFBZTtRQUMxQzVULE9BQU96QyxLQUFLLEdBQUcsQ0FBQyxDQUFDc0MsU0FBU3pCLGdCQUFnQjtRQUMxQ3NFLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQ2pCMEosYUFBYWdILGlCQUFpQlcsZUFBZSxHQUN2Qy9YLFdBQVdvUSxXQUFXLEdBQ3RCO1lBQ05qUSxTQUFTb1gscUJBQ0gsUUFDQUgsaUJBQWlCckIsU0FBUyxHQUN0Qi9WLFdBQVdHLE9BQU8sR0FDbEIsQ0FBQyxDQUFFaVgsQ0FBQUEsaUJBQWlCSSxpQkFBaUIsSUFDbkMsQ0FBQzVQLFVBQVV6RyxZQUFZakMsZUFBYztZQUNqRHFPLGFBQWE2SixpQkFBaUJZLGVBQWUsR0FDdkNoWSxXQUFXdU4sV0FBVyxHQUN0QjtZQUNObE4sYUFBYWtYLHFCQUNQLENBQUMsSUFDREgsaUJBQWlCTSxlQUFlLEdBQzVCTixpQkFBaUJJLGlCQUFpQixJQUFJelYsY0FDbEM4SCxlQUFlM0ssZ0JBQWdCNkMsZUFDL0IvQixXQUFXSyxXQUFXLEdBQzFCK1csaUJBQWlCSSxpQkFBaUIsSUFBSXJXLGFBQ2xDMEksZUFBZTNLLGdCQUFnQmlDLGNBQy9CaVcsaUJBQWlCckIsU0FBUyxHQUN0Qi9WLFdBQVdLLFdBQVcsR0FDdEIsQ0FBQztZQUNuQkMsZUFBZThXLGlCQUFpQnBCLFdBQVcsR0FDckNoVyxXQUFXTSxhQUFhLEdBQ3hCLENBQUM7WUFDUEksUUFBUTBXLGlCQUFpQmEsVUFBVSxHQUFHalksV0FBV1UsTUFBTSxHQUFHLENBQUM7WUFDM0RpRyxvQkFBb0J5USxpQkFBaUJjLHNCQUFzQixHQUNyRGxZLFdBQVcyRyxrQkFBa0IsR0FDN0I7WUFDTjJKLGNBQWM7UUFDbEI7SUFDSjtJQUNBLE1BQU13SCxRQUFRLENBQUMzVyxZQUFZaVcsbUJBQXFCRCxPQUFPN08sV0FBV25ILGNBQzVEQSxXQUFXWSxlQUNYWixZQUFZaVc7SUFDbEIsTUFBTWUsV0FBVyxDQUFDcmUsTUFBTW1RLFVBQVUsQ0FBQyxDQUFDO1FBQ2hDLE1BQU0vRyxRQUFRdEgsSUFBSXVILFNBQVNySjtRQUMzQixNQUFNb1MsaUJBQWlCaEosU0FBU0EsTUFBTUUsRUFBRTtRQUN4QyxJQUFJOEksZ0JBQWdCO1lBQ2hCLE1BQU1vSyxXQUFXcEssZUFBZXBCLElBQUksR0FDOUJvQixlQUFlcEIsSUFBSSxDQUFDLEVBQUUsR0FDdEJvQixlQUFlbEosR0FBRztZQUN4QixJQUFJc1QsU0FBU2pULEtBQUssRUFBRTtnQkFDaEJpVCxTQUFTalQsS0FBSztnQkFDZDRHLFFBQVFtTyxZQUFZLElBQ2hCOVAsV0FBV2dPLFNBQVNoVCxNQUFNLEtBQzFCZ1QsU0FBU2hULE1BQU07WUFDdkI7UUFDSjtJQUNKO0lBQ0EsTUFBTW9TLGdCQUFnQixDQUFDMUM7UUFDbkJoVCxhQUFhO1lBQ1QsR0FBR0EsVUFBVTtZQUNiLEdBQUdnVCxnQkFBZ0I7UUFDdkI7SUFDSjtJQUNBLE1BQU1xRixzQkFBc0IsSUFBTS9QLFdBQVczRSxTQUFTMUUsYUFBYSxLQUMvRDBFLFNBQVMxRSxhQUFhLEdBQUdxWixJQUFJLENBQUMsQ0FBQ3hXO1lBQzNCZ1csTUFBTWhXLFFBQVE2QixTQUFTNFUsWUFBWTtZQUNuQy9SLFVBQVVDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUNqQnRHLFdBQVc7WUFDZjtRQUNKO0lBQ0osTUFBTVQsVUFBVTtRQUNaYixTQUFTO1lBQ0x5RDtZQUNBeUI7WUFDQW1SO1lBQ0E1UDtZQUNBcUI7WUFDQWpHO1lBQ0F1UTtZQUNBclA7WUFDQWlRO1lBQ0FoUjtZQUNBeVE7WUFDQXROO1lBQ0ErTjtZQUNBeUI7WUFDQTBEO1lBQ0FrQjtZQUNBclc7WUFDQTBVO1lBQ0FsUTtZQUNBbEg7WUFDQSxJQUFJNkQsV0FBVTtnQkFDVixPQUFPQTtZQUNYO1lBQ0EsSUFBSXBCLGVBQWM7Z0JBQ2QsT0FBT0E7WUFDWDtZQUNBLElBQUkrQixVQUFTO2dCQUNULE9BQU9BO1lBQ1g7WUFDQSxJQUFJQSxRQUFPNUssTUFBTztnQkFDZDRLLFNBQVM1SztZQUNiO1lBQ0EsSUFBSWdHLGtCQUFpQjtnQkFDakIsT0FBT0E7WUFDWDtZQUNBLElBQUlnQyxVQUFTO2dCQUNULE9BQU9BO1lBQ1g7WUFDQSxJQUFJQSxRQUFPaEksTUFBTztnQkFDZGdJLFNBQVNoSTtZQUNiO1lBQ0EsSUFBSThHLGNBQWE7Z0JBQ2IsT0FBT0E7WUFDWDtZQUNBLElBQUkyRCxZQUFXO2dCQUNYLE9BQU9BO1lBQ1g7WUFDQSxJQUFJQSxVQUFTekssTUFBTztnQkFDaEJ5SyxXQUFXO29CQUNQLEdBQUdBLFFBQVE7b0JBQ1gsR0FBR3pLLEtBQUs7Z0JBQ1o7WUFDSjtRQUNKO1FBQ0FvTztRQUNBME07UUFDQXpSO1FBQ0FnRDtRQUNBbEU7UUFDQThTO1FBQ0FYO1FBQ0FzRTtRQUNBWjtRQUNBOUI7UUFDQXBSO1FBQ0E0QztRQUNBdVI7UUFDQWhEO0lBQ0o7SUFDQSxPQUFPO1FBQ0gsR0FBR3hWLE9BQU87UUFDVjZZLGFBQWE3WTtJQUNqQjtBQUNKO0FBRUEsSUFBSThZLGFBQWE7SUFDYixNQUFNQyxJQUFJLE9BQU9DLGdCQUFnQixjQUFjeGYsS0FBS3lmLEdBQUcsS0FBS0QsWUFBWUMsR0FBRyxLQUFLO0lBQ2hGLE9BQU8sdUNBQXVDcGMsT0FBTyxDQUFDLFNBQVMsQ0FBQ3FjO1FBQzVELE1BQU1DLElBQUksQ0FBQ0MsS0FBS0MsTUFBTSxLQUFLLEtBQUtOLENBQUFBLElBQUssS0FBSztRQUMxQyxPQUFPLENBQUNHLEtBQUssTUFBTUMsSUFBSSxJQUFLLE1BQU8sR0FBRSxFQUFHRyxRQUFRLENBQUM7SUFDckQ7QUFDSjtBQUVBLElBQUlDLG9CQUFvQixDQUFDcGYsTUFBTTRDLE9BQU91TixVQUFVLENBQUMsQ0FBQyxHQUFLQSxRQUFRaUwsV0FBVyxJQUFJelosWUFBWXdPLFFBQVFpTCxXQUFXLElBQ3ZHakwsUUFBUWtQLFNBQVMsSUFDZixDQUFDLEVBQUVyZixLQUFLLENBQUMsRUFBRTJCLFlBQVl3TyxRQUFRbVAsVUFBVSxJQUFJMWMsUUFBUXVOLFFBQVFtUCxVQUFVLENBQUMsQ0FBQyxDQUFDLEdBQzVFO0FBRU4sSUFBSUMsV0FBVyxDQUFDdGUsTUFBTTdCLFNBQVU7V0FDekI2QjtXQUNBbU0sc0JBQXNCaE87S0FDNUI7QUFFRCxJQUFJb2dCLGlCQUFpQixDQUFDcGdCLFNBQVVLLE1BQU1DLE9BQU8sQ0FBQ04sVUFBU0EsT0FBTXFJLEdBQUcsQ0FBQyxJQUFNNUYsYUFBYUE7QUFFcEYsU0FBUzRkLE9BQU94ZSxJQUFJLEVBQUUyQixLQUFLLEVBQUV4RCxNQUFLO0lBQzlCLE9BQU87V0FDQTZCLEtBQUttTyxLQUFLLENBQUMsR0FBR3hNO1dBQ2R3SyxzQkFBc0JoTztXQUN0QjZCLEtBQUttTyxLQUFLLENBQUN4TTtLQUNqQjtBQUNMO0FBRUEsSUFBSThjLGNBQWMsQ0FBQ3plLE1BQU1zVyxNQUFNb0k7SUFDM0IsSUFBSSxDQUFDbGdCLE1BQU1DLE9BQU8sQ0FBQ3VCLE9BQU87UUFDdEIsT0FBTyxFQUFFO0lBQ2I7SUFDQSxJQUFJVSxZQUFZVixJQUFJLENBQUMwZSxHQUFHLEdBQUc7UUFDdkIxZSxJQUFJLENBQUMwZSxHQUFHLEdBQUc5ZDtJQUNmO0lBQ0FaLEtBQUsyZSxNQUFNLENBQUNELElBQUksR0FBRzFlLEtBQUsyZSxNQUFNLENBQUNySSxNQUFNLEVBQUUsQ0FBQyxFQUFFO0lBQzFDLE9BQU90VztBQUNYO0FBRUEsSUFBSTRlLFlBQVksQ0FBQzVlLE1BQU03QixTQUFVO1dBQzFCZ08sc0JBQXNCaE87V0FDdEJnTyxzQkFBc0JuTTtLQUM1QjtBQUVELFNBQVM2ZSxnQkFBZ0I3ZSxJQUFJLEVBQUU4ZSxPQUFPO0lBQ2xDLElBQUlDLElBQUk7SUFDUixNQUFNQyxPQUFPO1dBQUloZjtLQUFLO0lBQ3RCLEtBQUssTUFBTTJCLFNBQVNtZCxRQUFTO1FBQ3pCRSxLQUFLTCxNQUFNLENBQUNoZCxRQUFRb2QsR0FBRztRQUN2QkE7SUFDSjtJQUNBLE9BQU94ZSxRQUFReWUsTUFBTW5kLE1BQU0sR0FBR21kLE9BQU8sRUFBRTtBQUMzQztBQUNBLElBQUlDLGdCQUFnQixDQUFDamYsTUFBTTJCLFFBQVVqQixZQUFZaUIsU0FDM0MsRUFBRSxHQUNGa2QsZ0JBQWdCN2UsTUFBTW1NLHNCQUFzQnhLLE9BQU91ZCxJQUFJLENBQUMsQ0FBQ0MsR0FBR0MsSUFBTUQsSUFBSUM7QUFFNUUsSUFBSUMsY0FBYyxDQUFDcmYsTUFBTXNmLFFBQVFDO0lBQzdCLENBQUN2ZixJQUFJLENBQUNzZixPQUFPLEVBQUV0ZixJQUFJLENBQUN1ZixPQUFPLENBQUMsR0FBRztRQUFDdmYsSUFBSSxDQUFDdWYsT0FBTztRQUFFdmYsSUFBSSxDQUFDc2YsT0FBTztLQUFDO0FBQy9EO0FBRUEsSUFBSUUsV0FBVyxDQUFDNUksYUFBYWpWLE9BQU94RDtJQUNoQ3lZLFdBQVcsQ0FBQ2pWLE1BQU0sR0FBR3hEO0lBQ3JCLE9BQU95WTtBQUNYO0FBRUE7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQW9DQyxHQUNELFNBQVM2SSxjQUFjaGMsS0FBSztJQUN4QixNQUFNbUIsVUFBVXRCO0lBQ2hCLE1BQU0sRUFBRVMsVUFBVWEsUUFBUWIsT0FBTyxFQUFFaEYsSUFBSSxFQUFFMmdCLFVBQVUsSUFBSSxFQUFFdlksZ0JBQWdCLEVBQUVNLEtBQUssRUFBRyxHQUFHaEU7SUFDdEYsTUFBTSxDQUFDaUwsUUFBUWlSLFVBQVUsR0FBRzdoQiwyQ0FBdUIsQ0FBQ2lHLFFBQVEyVSxjQUFjLENBQUMzWjtJQUMzRSxNQUFNNmdCLE1BQU05aEIseUNBQXFCLENBQUNpRyxRQUFRMlUsY0FBYyxDQUFDM1osTUFBTXlILEdBQUcsQ0FBQ2tYO0lBQ25FLE1BQU1tQyxZQUFZL2hCLHlDQUFxQixDQUFDNFE7SUFDeEMsTUFBTW9SLFFBQVFoaUIseUNBQXFCLENBQUNpQjtJQUNwQyxNQUFNZ2hCLFlBQVlqaUIseUNBQXFCLENBQUM7SUFDeENnaUIsTUFBTWphLE9BQU8sR0FBRzlHO0lBQ2hCOGdCLFVBQVVoYSxPQUFPLEdBQUc2STtJQUNwQjNLLFFBQVFvQyxNQUFNLENBQUNrQixLQUFLLENBQUNkLEdBQUcsQ0FBQ3hIO0lBQ3pCMEksU0FDSTFELFFBQVF5RCxRQUFRLENBQUN6SSxNQUFNMEk7SUFDM0IzSiw0Q0FBd0IsQ0FBQyxJQUFNaUcsUUFBUTBILFNBQVMsQ0FBQ3BFLEtBQUssQ0FBQ2tGLFNBQVMsQ0FBQztZQUM3RFosTUFBTSxDQUFDLEVBQUU1RSxNQUFNLEVBQUVoSSxNQUFNaWhCLGNBQWMsRUFBRztnQkFDcEMsSUFBSUEsbUJBQW1CRixNQUFNamEsT0FBTyxJQUFJLENBQUNtYSxnQkFBZ0I7b0JBQ3JELE1BQU1wSixjQUFjL1YsSUFBSWtHLFFBQVErWSxNQUFNamEsT0FBTztvQkFDN0MsSUFBSXJILE1BQU1DLE9BQU8sQ0FBQ21ZLGNBQWM7d0JBQzVCK0ksVUFBVS9JO3dCQUNWZ0osSUFBSS9aLE9BQU8sR0FBRytRLFlBQVlwUSxHQUFHLENBQUNrWDtvQkFDbEM7Z0JBQ0o7WUFDSjtRQUNKLEdBQUdqUixXQUFXLEVBQUU7UUFBQzFJO0tBQVE7SUFDekIsTUFBTWtjLGVBQWVuaUIsOENBQTBCLENBQUMsQ0FBQ29pQjtRQUM3Q0gsVUFBVWxhLE9BQU8sR0FBRztRQUNwQjlCLFFBQVF5UyxjQUFjLENBQUN6WCxNQUFNbWhCO0lBQ2pDLEdBQUc7UUFBQ25jO1FBQVNoRjtLQUFLO0lBQ2xCLE1BQU1pTSxTQUFTLENBQUM3TSxRQUFPK1E7UUFDbkIsTUFBTWlSLGNBQWNoVSxzQkFBc0JwTSxZQUFZNUI7UUFDdEQsTUFBTStoQiwwQkFBMEI1QixTQUFTdmEsUUFBUTJVLGNBQWMsQ0FBQzNaLE9BQU9vaEI7UUFDdkVwYyxRQUFRb0MsTUFBTSxDQUFDbUMsS0FBSyxHQUFHNlYsa0JBQWtCcGYsTUFBTW1oQix3QkFBd0JyZSxNQUFNLEdBQUcsR0FBR3FOO1FBQ25GMFEsSUFBSS9aLE9BQU8sR0FBR3lZLFNBQVNzQixJQUFJL1osT0FBTyxFQUFFc2EsWUFBWTNaLEdBQUcsQ0FBQ2tYO1FBQ3BEdUMsYUFBYUM7UUFDYlAsVUFBVU87UUFDVm5jLFFBQVF5UyxjQUFjLENBQUN6WCxNQUFNbWhCLHlCQUF5QjVCLFVBQVU7WUFDNUR6SCxNQUFNMEgsZUFBZXBnQjtRQUN6QjtJQUNKO0lBQ0EsTUFBTWlpQixVQUFVLENBQUNqaUIsUUFBTytRO1FBQ3BCLE1BQU1tUixlQUFlbFUsc0JBQXNCcE0sWUFBWTVCO1FBQ3ZELE1BQU0raEIsMEJBQTBCdEIsVUFBVTdhLFFBQVEyVSxjQUFjLENBQUMzWixPQUFPc2hCO1FBQ3hFdGMsUUFBUW9DLE1BQU0sQ0FBQ21DLEtBQUssR0FBRzZWLGtCQUFrQnBmLE1BQU0sR0FBR21RO1FBQ2xEMFEsSUFBSS9aLE9BQU8sR0FBRytZLFVBQVVnQixJQUFJL1osT0FBTyxFQUFFd2EsYUFBYTdaLEdBQUcsQ0FBQ2tYO1FBQ3REdUMsYUFBYUM7UUFDYlAsVUFBVU87UUFDVm5jLFFBQVF5UyxjQUFjLENBQUN6WCxNQUFNbWhCLHlCQUF5QnRCLFdBQVc7WUFDN0QvSCxNQUFNMEgsZUFBZXBnQjtRQUN6QjtJQUNKO0lBQ0EsTUFBTW1pQixTQUFTLENBQUMzZTtRQUNaLE1BQU11ZSwwQkFBMEJqQixjQUFjbGIsUUFBUTJVLGNBQWMsQ0FBQzNaLE9BQU80QztRQUM1RWllLElBQUkvWixPQUFPLEdBQUdvWixjQUFjVyxJQUFJL1osT0FBTyxFQUFFbEU7UUFDekNzZSxhQUFhQztRQUNiUCxVQUFVTztRQUNWLENBQUMxaEIsTUFBTUMsT0FBTyxDQUFDb0MsSUFBSWtELFFBQVFxRSxPQUFPLEVBQUVySixVQUNoQzJDLElBQUlxQyxRQUFRcUUsT0FBTyxFQUFFckosTUFBTTZCO1FBQy9CbUQsUUFBUXlTLGNBQWMsQ0FBQ3pYLE1BQU1taEIseUJBQXlCakIsZUFBZTtZQUNqRXBJLE1BQU1sVjtRQUNWO0lBQ0o7SUFDQSxNQUFNNGUsV0FBVyxDQUFDNWUsT0FBT3hELFFBQU8rUTtRQUM1QixNQUFNc1IsY0FBY3JVLHNCQUFzQnBNLFlBQVk1QjtRQUN0RCxNQUFNK2hCLDBCQUEwQjFCLE9BQU96YSxRQUFRMlUsY0FBYyxDQUFDM1osT0FBTzRDLE9BQU82ZTtRQUM1RXpjLFFBQVFvQyxNQUFNLENBQUNtQyxLQUFLLEdBQUc2VixrQkFBa0JwZixNQUFNNEMsT0FBT3VOO1FBQ3REMFEsSUFBSS9aLE9BQU8sR0FBRzJZLE9BQU9vQixJQUFJL1osT0FBTyxFQUFFbEUsT0FBTzZlLFlBQVloYSxHQUFHLENBQUNrWDtRQUN6RHVDLGFBQWFDO1FBQ2JQLFVBQVVPO1FBQ1ZuYyxRQUFReVMsY0FBYyxDQUFDelgsTUFBTW1oQix5QkFBeUIxQixRQUFRO1lBQzFEM0gsTUFBTWxWO1lBQ05tVixNQUFNeUgsZUFBZXBnQjtRQUN6QjtJQUNKO0lBQ0EsTUFBTXNpQixPQUFPLENBQUNuQixRQUFRQztRQUNsQixNQUFNVywwQkFBMEJuYyxRQUFRMlUsY0FBYyxDQUFDM1o7UUFDdkRzZ0IsWUFBWWEseUJBQXlCWixRQUFRQztRQUM3Q0YsWUFBWU8sSUFBSS9aLE9BQU8sRUFBRXlaLFFBQVFDO1FBQ2pDVSxhQUFhQztRQUNiUCxVQUFVTztRQUNWbmMsUUFBUXlTLGNBQWMsQ0FBQ3pYLE1BQU1taEIseUJBQXlCYixhQUFhO1lBQy9EeEksTUFBTXlJO1lBQ054SSxNQUFNeUk7UUFDVixHQUFHO0lBQ1A7SUFDQSxNQUFNbUIsT0FBTyxDQUFDcEssTUFBTW9JO1FBQ2hCLE1BQU13QiwwQkFBMEJuYyxRQUFRMlUsY0FBYyxDQUFDM1o7UUFDdkQwZixZQUFZeUIseUJBQXlCNUosTUFBTW9JO1FBQzNDRCxZQUFZbUIsSUFBSS9aLE9BQU8sRUFBRXlRLE1BQU1vSTtRQUMvQnVCLGFBQWFDO1FBQ2JQLFVBQVVPO1FBQ1ZuYyxRQUFReVMsY0FBYyxDQUFDelgsTUFBTW1oQix5QkFBeUJ6QixhQUFhO1lBQy9ENUgsTUFBTVA7WUFDTlEsTUFBTTRIO1FBQ1YsR0FBRztJQUNQO0lBQ0EsTUFBTWlDLFNBQVMsQ0FBQ2hmLE9BQU94RDtRQUNuQixNQUFNMEksY0FBYzlHLFlBQVk1QjtRQUNoQyxNQUFNK2hCLDBCQUEwQlYsU0FBU3piLFFBQVEyVSxjQUFjLENBQUMzWixPQUFPNEMsT0FBT2tGO1FBQzlFK1ksSUFBSS9aLE9BQU8sR0FBRztlQUFJcWE7U0FBd0IsQ0FBQzFaLEdBQUcsQ0FBQyxDQUFDb2EsTUFBTTdCLElBQU0sQ0FBQzZCLFFBQVE3QixNQUFNcGQsUUFBUStiLGVBQWVrQyxJQUFJL1osT0FBTyxDQUFDa1osRUFBRTtRQUNoSGtCLGFBQWFDO1FBQ2JQLFVBQVU7ZUFBSU87U0FBd0I7UUFDdENuYyxRQUFReVMsY0FBYyxDQUFDelgsTUFBTW1oQix5QkFBeUJWLFVBQVU7WUFDNUQzSSxNQUFNbFY7WUFDTm1WLE1BQU1qUTtRQUNWLEdBQUcsTUFBTTtJQUNiO0lBQ0EsTUFBTXBGLFVBQVUsQ0FBQ3REO1FBQ2IsTUFBTStoQiwwQkFBMEIvVCxzQkFBc0JwTSxZQUFZNUI7UUFDbEV5aEIsSUFBSS9aLE9BQU8sR0FBR3FhLHdCQUF3QjFaLEdBQUcsQ0FBQ2tYO1FBQzFDdUMsYUFBYTtlQUFJQztTQUF3QjtRQUN6Q1AsVUFBVTtlQUFJTztTQUF3QjtRQUN0Q25jLFFBQVF5UyxjQUFjLENBQUN6WCxNQUFNO2VBQUltaEI7U0FBd0IsRUFBRSxDQUFDbGdCLE9BQVNBLE1BQU0sQ0FBQyxHQUFHLE1BQU07SUFDekY7SUFDQWxDLDRDQUF3QixDQUFDO1FBQ3JCaUcsUUFBUWdGLE1BQU0sQ0FBQ0MsTUFBTSxHQUFHO1FBQ3hCdUksVUFBVXhTLE1BQU1nRixRQUFRb0MsTUFBTSxLQUMxQnBDLFFBQVEwSCxTQUFTLENBQUNDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQ3pCLEdBQUc1SCxRQUFRa0IsVUFBVTtRQUN6QjtRQUNKLElBQUk4YSxVQUFVbGEsT0FBTyxJQUNoQixFQUFDNkssbUJBQW1CM00sUUFBUTZFLFFBQVEsQ0FBQytILElBQUksRUFBRUMsVUFBVSxJQUNsRDdNLFFBQVFrQixVQUFVLENBQUN1TixXQUFXLEtBQ2xDLENBQUM5QixtQkFBbUIzTSxRQUFRNkUsUUFBUSxDQUFDNkosY0FBYyxFQUFFN0IsVUFBVSxFQUFFO1lBQ2pFLElBQUk3TSxRQUFRNkUsUUFBUSxDQUFDc04sUUFBUSxFQUFFO2dCQUMzQm5TLFFBQVFvUyxVQUFVLENBQUM7b0JBQUNwWDtpQkFBSyxFQUFFd2UsSUFBSSxDQUFDLENBQUN0YztvQkFDN0IsTUFBTThHLFFBQVFsSCxJQUFJSSxPQUFPMEUsTUFBTSxFQUFFNUc7b0JBQ2pDLE1BQU04aEIsZ0JBQWdCaGdCLElBQUlrRCxRQUFRa0IsVUFBVSxDQUFDVSxNQUFNLEVBQUU1RztvQkFDckQsSUFBSThoQixnQkFDRSxDQUFFOVksU0FBUzhZLGNBQWM1aUIsSUFBSSxJQUMxQjhKLFNBQ0k4WSxDQUFBQSxjQUFjNWlCLElBQUksS0FBSzhKLE1BQU05SixJQUFJLElBQzlCNGlCLGNBQWNwWSxPQUFPLEtBQUtWLE1BQU1VLE9BQU8sSUFDakRWLFNBQVNBLE1BQU05SixJQUFJLEVBQUU7d0JBQ3ZCOEosUUFDTXJHLElBQUlxQyxRQUFRa0IsVUFBVSxDQUFDVSxNQUFNLEVBQUU1RyxNQUFNZ0osU0FDckNzRyxNQUFNdEssUUFBUWtCLFVBQVUsQ0FBQ1UsTUFBTSxFQUFFNUc7d0JBQ3ZDZ0YsUUFBUTBILFNBQVMsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUM7NEJBQ3pCaEcsUUFBUTVCLFFBQVFrQixVQUFVLENBQUNVLE1BQU07d0JBQ3JDO29CQUNKO2dCQUNKO1lBQ0osT0FDSztnQkFDRCxNQUFNd0MsUUFBUXRILElBQUlrRCxRQUFRcUUsT0FBTyxFQUFFcko7Z0JBQ25DLElBQUlvSixTQUNBQSxNQUFNRSxFQUFFLElBQ1IsQ0FBRXFJLENBQUFBLG1CQUFtQjNNLFFBQVE2RSxRQUFRLENBQUM2SixjQUFjLEVBQUU3QixVQUFVLElBQzVERixtQkFBbUIzTSxRQUFRNkUsUUFBUSxDQUFDK0gsSUFBSSxFQUFFQyxVQUFVLEdBQUc7b0JBQzNEc0MsY0FBYy9LLE9BQU9wRSxRQUFRb0MsTUFBTSxDQUFDdEIsUUFBUSxFQUFFZCxRQUFRaUQsV0FBVyxFQUFFakQsUUFBUTZFLFFBQVEsQ0FBQ3VILFlBQVksS0FBSzdOLGdCQUFnQkssR0FBRyxFQUFFb0IsUUFBUTZFLFFBQVEsQ0FBQ3dILHlCQUF5QixFQUFFLE1BQU1tTixJQUFJLENBQUMsQ0FBQ3hWLFFBQVUsQ0FBQ3NGLGNBQWN0RixVQUN2TWhFLFFBQVEwSCxTQUFTLENBQUNDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDOzRCQUN6QmhHLFFBQVFnTiwwQkFBMEI1TyxRQUFRa0IsVUFBVSxDQUFDVSxNQUFNLEVBQUVvQyxPQUFPaEo7d0JBQ3hFO2dCQUNSO1lBQ0o7UUFDSjtRQUNBZ0YsUUFBUTBILFNBQVMsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUM7WUFDekI1TTtZQUNBZ0ksUUFBUWhILFlBQVlnRSxRQUFRaUQsV0FBVztRQUMzQztRQUNBakQsUUFBUW9DLE1BQU0sQ0FBQ21DLEtBQUssSUFDaEJxSixzQkFBc0I1TixRQUFRcUUsT0FBTyxFQUFFLENBQUNILEtBQUszSDtZQUN6QyxJQUFJeUQsUUFBUW9DLE1BQU0sQ0FBQ21DLEtBQUssSUFDcEJoSSxJQUFJb1IsVUFBVSxDQUFDM04sUUFBUW9DLE1BQU0sQ0FBQ21DLEtBQUssS0FDbkNMLElBQUlLLEtBQUssRUFBRTtnQkFDWEwsSUFBSUssS0FBSztnQkFDVCxPQUFPO1lBQ1g7WUFDQTtRQUNKO1FBQ0p2RSxRQUFRb0MsTUFBTSxDQUFDbUMsS0FBSyxHQUFHO1FBQ3ZCdkUsUUFBUWdDLFNBQVM7UUFDakJnYSxVQUFVbGEsT0FBTyxHQUFHO0lBQ3hCLEdBQUc7UUFBQzZJO1FBQVEzUDtRQUFNZ0Y7S0FBUTtJQUMxQmpHLDRDQUF3QixDQUFDO1FBQ3JCLENBQUMrQyxJQUFJa0QsUUFBUWlELFdBQVcsRUFBRWpJLFNBQVNnRixRQUFReVMsY0FBYyxDQUFDelg7UUFDMUQsT0FBTztZQUNILE1BQU04SixnQkFBZ0IsQ0FBQzlKLE1BQU1aO2dCQUN6QixNQUFNZ0ssUUFBUXRILElBQUlrRCxRQUFRcUUsT0FBTyxFQUFFcko7Z0JBQ25DLElBQUlvSixTQUFTQSxNQUFNRSxFQUFFLEVBQUU7b0JBQ25CRixNQUFNRSxFQUFFLENBQUNTLEtBQUssR0FBRzNLO2dCQUNyQjtZQUNKO1lBQ0E0RixRQUFRNkUsUUFBUSxDQUFDekIsZ0JBQWdCLElBQUlBLG1CQUMvQnBELFFBQVFrRixVQUFVLENBQUNsSyxRQUNuQjhKLGNBQWM5SixNQUFNO1FBQzlCO0lBQ0osR0FBRztRQUFDQTtRQUFNZ0Y7UUFBUzJiO1FBQVN2WTtLQUFpQjtJQUM3QyxPQUFPO1FBQ0hzWixNQUFNM2lCLDhDQUEwQixDQUFDMmlCLE1BQU07WUFBQ1I7WUFBY2xoQjtZQUFNZ0Y7U0FBUTtRQUNwRTJjLE1BQU01aUIsOENBQTBCLENBQUM0aUIsTUFBTTtZQUFDVDtZQUFjbGhCO1lBQU1nRjtTQUFRO1FBQ3BFcWMsU0FBU3RpQiw4Q0FBMEIsQ0FBQ3NpQixTQUFTO1lBQUNIO1lBQWNsaEI7WUFBTWdGO1NBQVE7UUFDMUVpSCxRQUFRbE4sOENBQTBCLENBQUNrTixRQUFRO1lBQUNpVjtZQUFjbGhCO1lBQU1nRjtTQUFRO1FBQ3hFdWMsUUFBUXhpQiw4Q0FBMEIsQ0FBQ3dpQixRQUFRO1lBQUNMO1lBQWNsaEI7WUFBTWdGO1NBQVE7UUFDeEV5YSxRQUFRMWdCLDhDQUEwQixDQUFDeWlCLFVBQVU7WUFBQ047WUFBY2xoQjtZQUFNZ0Y7U0FBUTtRQUMxRTRjLFFBQVE3aUIsOENBQTBCLENBQUM2aUIsUUFBUTtZQUFDVjtZQUFjbGhCO1lBQU1nRjtTQUFRO1FBQ3hFdEMsU0FBUzNELDhDQUEwQixDQUFDMkQsU0FBUztZQUFDd2U7WUFBY2xoQjtZQUFNZ0Y7U0FBUTtRQUMxRTJLLFFBQVE1USwwQ0FBc0IsQ0FBQyxJQUFNNFEsT0FBT2xJLEdBQUcsQ0FBQyxDQUFDMkIsT0FBT3hHLFFBQVc7b0JBQy9ELEdBQUd3RyxLQUFLO29CQUNSLENBQUN1WCxRQUFRLEVBQUVFLElBQUkvWixPQUFPLENBQUNsRSxNQUFNLElBQUkrYjtnQkFDckMsS0FBSztZQUFDaFA7WUFBUWdSO1NBQVE7SUFDMUI7QUFDSjtBQUVBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBNEJDLEdBQ0QsU0FBU29CLFFBQVFyZCxRQUFRLENBQUMsQ0FBQztJQUN2QixNQUFNc2QsZUFBZWpqQix5Q0FBcUIsQ0FBQzhDO0lBQzNDLE1BQU1vZ0IsVUFBVWxqQix5Q0FBcUIsQ0FBQzhDO0lBQ3RDLE1BQU0sQ0FBQ2tELFdBQVdpQixnQkFBZ0IsR0FBR2pILDJDQUF1QixDQUFDO1FBQ3pEc0gsU0FBUztRQUNUSyxjQUFjO1FBQ2RKLFdBQVdrSSxXQUFXOUosTUFBTVMsYUFBYTtRQUN6Q3NPLGFBQWE7UUFDYitDLGNBQWM7UUFDZDNKLG9CQUFvQjtRQUNwQmxHLFNBQVM7UUFDVDJQLGFBQWE7UUFDYi9QLGFBQWEsQ0FBQztRQUNkQyxlQUFlLENBQUM7UUFDaEJDLGtCQUFrQixDQUFDO1FBQ25CRyxRQUFRbEMsTUFBTWtDLE1BQU0sSUFBSSxDQUFDO1FBQ3pCZCxVQUFVcEIsTUFBTW9CLFFBQVEsSUFBSTtRQUM1QnlRLFNBQVM7UUFDVHBSLGVBQWVxSixXQUFXOUosTUFBTVMsYUFBYSxJQUN2Q3RELFlBQ0E2QyxNQUFNUyxhQUFhO0lBQzdCO0lBQ0EsSUFBSSxDQUFDNmMsYUFBYWxiLE9BQU8sRUFBRTtRQUN2QmtiLGFBQWFsYixPQUFPLEdBQUc7WUFDbkIsR0FBSXBDLE1BQU1nYSxXQUFXLEdBQUdoYSxNQUFNZ2EsV0FBVyxHQUFHckksa0JBQWtCM1IsTUFBTTtZQUNwRUs7UUFDSjtRQUNBLElBQUlMLE1BQU1nYSxXQUFXLElBQ2pCaGEsTUFBTVMsYUFBYSxJQUNuQixDQUFDcUosV0FBVzlKLE1BQU1TLGFBQWEsR0FBRztZQUNsQ1QsTUFBTWdhLFdBQVcsQ0FBQ1YsS0FBSyxDQUFDdFosTUFBTVMsYUFBYSxFQUFFVCxNQUFNK1osWUFBWTtRQUNuRTtJQUNKO0lBQ0EsTUFBTXpaLFVBQVVnZCxhQUFhbGIsT0FBTyxDQUFDOUIsT0FBTztJQUM1Q0EsUUFBUTZFLFFBQVEsR0FBR25GO0lBQ25CZSwwQkFBMEI7UUFDdEIsTUFBTXljLE1BQU1sZCxRQUFRNkIsVUFBVSxDQUFDO1lBQzNCOUIsV0FBV0MsUUFBUVEsZUFBZTtZQUNsQ3VCLFVBQVUsSUFBTWYsZ0JBQWdCO29CQUFFLEdBQUdoQixRQUFRa0IsVUFBVTtnQkFBQztZQUN4RDJWLGNBQWM7UUFDbEI7UUFDQTdWLGdCQUFnQixDQUFDL0UsT0FBVTtnQkFDdkIsR0FBR0EsSUFBSTtnQkFDUHNWLFNBQVM7WUFDYjtRQUNBdlIsUUFBUWtCLFVBQVUsQ0FBQ3FRLE9BQU8sR0FBRztRQUM3QixPQUFPMkw7SUFDWCxHQUFHO1FBQUNsZDtLQUFRO0lBQ1pqRyw0Q0FBd0IsQ0FBQyxJQUFNaUcsUUFBUTRYLFlBQVksQ0FBQ2xZLE1BQU1vQixRQUFRLEdBQUc7UUFBQ2Q7UUFBU04sTUFBTW9CLFFBQVE7S0FBQztJQUM5Ri9HLDRDQUF3QixDQUFDO1FBQ3JCLElBQUkyRixNQUFNa04sSUFBSSxFQUFFO1lBQ1o1TSxRQUFRNkUsUUFBUSxDQUFDK0gsSUFBSSxHQUFHbE4sTUFBTWtOLElBQUk7UUFDdEM7UUFDQSxJQUFJbE4sTUFBTWdQLGNBQWMsRUFBRTtZQUN0QjFPLFFBQVE2RSxRQUFRLENBQUM2SixjQUFjLEdBQUdoUCxNQUFNZ1AsY0FBYztRQUMxRDtRQUNBLElBQUloUCxNQUFNa0MsTUFBTSxJQUFJLENBQUMwSCxjQUFjNUosTUFBTWtDLE1BQU0sR0FBRztZQUM5QzVCLFFBQVFrVCxVQUFVLENBQUN4VCxNQUFNa0MsTUFBTTtRQUNuQztJQUNKLEdBQUc7UUFBQzVCO1FBQVNOLE1BQU1rQyxNQUFNO1FBQUVsQyxNQUFNa04sSUFBSTtRQUFFbE4sTUFBTWdQLGNBQWM7S0FBQztJQUM1RDNVLDRDQUF3QixDQUFDO1FBQ3JCMkYsTUFBTTBELGdCQUFnQixJQUNsQnBELFFBQVEwSCxTQUFTLENBQUNDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO1lBQ3pCNUUsUUFBUWhELFFBQVErQyxTQUFTO1FBQzdCO0lBQ1IsR0FBRztRQUFDL0M7UUFBU04sTUFBTTBELGdCQUFnQjtLQUFDO0lBQ3BDckosNENBQXdCLENBQUM7UUFDckIsSUFBSWlHLFFBQVFRLGVBQWUsQ0FBQ2EsT0FBTyxFQUFFO1lBQ2pDLE1BQU1BLFVBQVVyQixRQUFRZ1QsU0FBUztZQUNqQyxJQUFJM1IsWUFBWXRCLFVBQVVzQixPQUFPLEVBQUU7Z0JBQy9CckIsUUFBUTBILFNBQVMsQ0FBQ0MsS0FBSyxDQUFDQyxJQUFJLENBQUM7b0JBQ3pCdkc7Z0JBQ0o7WUFDSjtRQUNKO0lBQ0osR0FBRztRQUFDckI7UUFBU0QsVUFBVXNCLE9BQU87S0FBQztJQUMvQnRILDRDQUF3QixDQUFDO1FBQ3JCLElBQUkyRixNQUFNc0QsTUFBTSxJQUFJLENBQUM4RixVQUFVcEosTUFBTXNELE1BQU0sRUFBRWlhLFFBQVFuYixPQUFPLEdBQUc7WUFDM0Q5QixRQUFRcVksTUFBTSxDQUFDM1ksTUFBTXNELE1BQU0sRUFBRWhELFFBQVE2RSxRQUFRLENBQUM0VSxZQUFZO1lBQzFEd0QsUUFBUW5iLE9BQU8sR0FBR3BDLE1BQU1zRCxNQUFNO1lBQzlCaEMsZ0JBQWdCLENBQUMyRyxRQUFXO29CQUFFLEdBQUdBLEtBQUs7Z0JBQUM7UUFDM0MsT0FDSztZQUNEM0gsUUFBUXVaLG1CQUFtQjtRQUMvQjtJQUNKLEdBQUc7UUFBQ3ZaO1FBQVNOLE1BQU1zRCxNQUFNO0tBQUM7SUFDMUJqSiw0Q0FBd0IsQ0FBQztRQUNyQixJQUFJLENBQUNpRyxRQUFRZ0YsTUFBTSxDQUFDRCxLQUFLLEVBQUU7WUFDdkIvRSxRQUFRZ0MsU0FBUztZQUNqQmhDLFFBQVFnRixNQUFNLENBQUNELEtBQUssR0FBRztRQUMzQjtRQUNBLElBQUkvRSxRQUFRZ0YsTUFBTSxDQUFDekMsS0FBSyxFQUFFO1lBQ3RCdkMsUUFBUWdGLE1BQU0sQ0FBQ3pDLEtBQUssR0FBRztZQUN2QnZDLFFBQVEwSCxTQUFTLENBQUNDLEtBQUssQ0FBQ0MsSUFBSSxDQUFDO2dCQUFFLEdBQUc1SCxRQUFRa0IsVUFBVTtZQUFDO1FBQ3pEO1FBQ0FsQixRQUFRa0QsZ0JBQWdCO0lBQzVCO0lBQ0E4WixhQUFhbGIsT0FBTyxDQUFDL0IsU0FBUyxHQUFHRCxrQkFBa0JDLFdBQVdDO0lBQzlELE9BQU9nZCxhQUFhbGIsT0FBTztBQUMvQjtBQUVvSyxDQUNwSyxzQ0FBc0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWd3YXRjaGdvdi1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWFjdC1ob29rLWZvcm1ANy41Ni40X3JlYWN0QDE4LjMuMS9ub2RlX21vZHVsZXMvcmVhY3QtaG9vay1mb3JtL2Rpc3QvaW5kZXguZXNtLm1qcz9jYzYxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCBSZWFjdF9fZGVmYXVsdCBmcm9tICdyZWFjdCc7XG5cbnZhciBpc0NoZWNrQm94SW5wdXQgPSAoZWxlbWVudCkgPT4gZWxlbWVudC50eXBlID09PSAnY2hlY2tib3gnO1xuXG52YXIgaXNEYXRlT2JqZWN0ID0gKHZhbHVlKSA9PiB2YWx1ZSBpbnN0YW5jZW9mIERhdGU7XG5cbnZhciBpc051bGxPclVuZGVmaW5lZCA9ICh2YWx1ZSkgPT4gdmFsdWUgPT0gbnVsbDtcblxuY29uc3QgaXNPYmplY3RUeXBlID0gKHZhbHVlKSA9PiB0eXBlb2YgdmFsdWUgPT09ICdvYmplY3QnO1xudmFyIGlzT2JqZWN0ID0gKHZhbHVlKSA9PiAhaXNOdWxsT3JVbmRlZmluZWQodmFsdWUpICYmXG4gICAgIUFycmF5LmlzQXJyYXkodmFsdWUpICYmXG4gICAgaXNPYmplY3RUeXBlKHZhbHVlKSAmJlxuICAgICFpc0RhdGVPYmplY3QodmFsdWUpO1xuXG52YXIgZ2V0RXZlbnRWYWx1ZSA9IChldmVudCkgPT4gaXNPYmplY3QoZXZlbnQpICYmIGV2ZW50LnRhcmdldFxuICAgID8gaXNDaGVja0JveElucHV0KGV2ZW50LnRhcmdldClcbiAgICAgICAgPyBldmVudC50YXJnZXQuY2hlY2tlZFxuICAgICAgICA6IGV2ZW50LnRhcmdldC52YWx1ZVxuICAgIDogZXZlbnQ7XG5cbnZhciBnZXROb2RlUGFyZW50TmFtZSA9IChuYW1lKSA9PiBuYW1lLnN1YnN0cmluZygwLCBuYW1lLnNlYXJjaCgvXFwuXFxkKyhcXC58JCkvKSkgfHwgbmFtZTtcblxudmFyIGlzTmFtZUluRmllbGRBcnJheSA9IChuYW1lcywgbmFtZSkgPT4gbmFtZXMuaGFzKGdldE5vZGVQYXJlbnROYW1lKG5hbWUpKTtcblxudmFyIGlzUGxhaW5PYmplY3QgPSAodGVtcE9iamVjdCkgPT4ge1xuICAgIGNvbnN0IHByb3RvdHlwZUNvcHkgPSB0ZW1wT2JqZWN0LmNvbnN0cnVjdG9yICYmIHRlbXBPYmplY3QuY29uc3RydWN0b3IucHJvdG90eXBlO1xuICAgIHJldHVybiAoaXNPYmplY3QocHJvdG90eXBlQ29weSkgJiYgcHJvdG90eXBlQ29weS5oYXNPd25Qcm9wZXJ0eSgnaXNQcm90b3R5cGVPZicpKTtcbn07XG5cbnZhciBpc1dlYiA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmXG4gICAgdHlwZW9mIHdpbmRvdy5IVE1MRWxlbWVudCAhPT0gJ3VuZGVmaW5lZCcgJiZcbiAgICB0eXBlb2YgZG9jdW1lbnQgIT09ICd1bmRlZmluZWQnO1xuXG5mdW5jdGlvbiBjbG9uZU9iamVjdChkYXRhKSB7XG4gICAgbGV0IGNvcHk7XG4gICAgY29uc3QgaXNBcnJheSA9IEFycmF5LmlzQXJyYXkoZGF0YSk7XG4gICAgY29uc3QgaXNGaWxlTGlzdEluc3RhbmNlID0gdHlwZW9mIEZpbGVMaXN0ICE9PSAndW5kZWZpbmVkJyA/IGRhdGEgaW5zdGFuY2VvZiBGaWxlTGlzdCA6IGZhbHNlO1xuICAgIGlmIChkYXRhIGluc3RhbmNlb2YgRGF0ZSkge1xuICAgICAgICBjb3B5ID0gbmV3IERhdGUoZGF0YSk7XG4gICAgfVxuICAgIGVsc2UgaWYgKGRhdGEgaW5zdGFuY2VvZiBTZXQpIHtcbiAgICAgICAgY29weSA9IG5ldyBTZXQoZGF0YSk7XG4gICAgfVxuICAgIGVsc2UgaWYgKCEoaXNXZWIgJiYgKGRhdGEgaW5zdGFuY2VvZiBCbG9iIHx8IGlzRmlsZUxpc3RJbnN0YW5jZSkpICYmXG4gICAgICAgIChpc0FycmF5IHx8IGlzT2JqZWN0KGRhdGEpKSkge1xuICAgICAgICBjb3B5ID0gaXNBcnJheSA/IFtdIDoge307XG4gICAgICAgIGlmICghaXNBcnJheSAmJiAhaXNQbGFpbk9iamVjdChkYXRhKSkge1xuICAgICAgICAgICAgY29weSA9IGRhdGE7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiBkYXRhKSB7XG4gICAgICAgICAgICAgICAgaWYgKGRhdGEuaGFzT3duUHJvcGVydHkoa2V5KSkge1xuICAgICAgICAgICAgICAgICAgICBjb3B5W2tleV0gPSBjbG9uZU9iamVjdChkYXRhW2tleV0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIGRhdGE7XG4gICAgfVxuICAgIHJldHVybiBjb3B5O1xufVxuXG52YXIgY29tcGFjdCA9ICh2YWx1ZSkgPT4gQXJyYXkuaXNBcnJheSh2YWx1ZSkgPyB2YWx1ZS5maWx0ZXIoQm9vbGVhbikgOiBbXTtcblxudmFyIGlzVW5kZWZpbmVkID0gKHZhbCkgPT4gdmFsID09PSB1bmRlZmluZWQ7XG5cbnZhciBnZXQgPSAob2JqZWN0LCBwYXRoLCBkZWZhdWx0VmFsdWUpID0+IHtcbiAgICBpZiAoIXBhdGggfHwgIWlzT2JqZWN0KG9iamVjdCkpIHtcbiAgICAgICAgcmV0dXJuIGRlZmF1bHRWYWx1ZTtcbiAgICB9XG4gICAgY29uc3QgcmVzdWx0ID0gY29tcGFjdChwYXRoLnNwbGl0KC9bLFtcXF0uXSs/LykpLnJlZHVjZSgocmVzdWx0LCBrZXkpID0+IGlzTnVsbE9yVW5kZWZpbmVkKHJlc3VsdCkgPyByZXN1bHQgOiByZXN1bHRba2V5XSwgb2JqZWN0KTtcbiAgICByZXR1cm4gaXNVbmRlZmluZWQocmVzdWx0KSB8fCByZXN1bHQgPT09IG9iamVjdFxuICAgICAgICA/IGlzVW5kZWZpbmVkKG9iamVjdFtwYXRoXSlcbiAgICAgICAgICAgID8gZGVmYXVsdFZhbHVlXG4gICAgICAgICAgICA6IG9iamVjdFtwYXRoXVxuICAgICAgICA6IHJlc3VsdDtcbn07XG5cbnZhciBpc0Jvb2xlYW4gPSAodmFsdWUpID0+IHR5cGVvZiB2YWx1ZSA9PT0gJ2Jvb2xlYW4nO1xuXG52YXIgaXNLZXkgPSAodmFsdWUpID0+IC9eXFx3KiQvLnRlc3QodmFsdWUpO1xuXG52YXIgc3RyaW5nVG9QYXRoID0gKGlucHV0KSA9PiBjb21wYWN0KGlucHV0LnJlcGxhY2UoL1tcInwnXXxcXF0vZywgJycpLnNwbGl0KC9cXC58XFxbLykpO1xuXG52YXIgc2V0ID0gKG9iamVjdCwgcGF0aCwgdmFsdWUpID0+IHtcbiAgICBsZXQgaW5kZXggPSAtMTtcbiAgICBjb25zdCB0ZW1wUGF0aCA9IGlzS2V5KHBhdGgpID8gW3BhdGhdIDogc3RyaW5nVG9QYXRoKHBhdGgpO1xuICAgIGNvbnN0IGxlbmd0aCA9IHRlbXBQYXRoLmxlbmd0aDtcbiAgICBjb25zdCBsYXN0SW5kZXggPSBsZW5ndGggLSAxO1xuICAgIHdoaWxlICgrK2luZGV4IDwgbGVuZ3RoKSB7XG4gICAgICAgIGNvbnN0IGtleSA9IHRlbXBQYXRoW2luZGV4XTtcbiAgICAgICAgbGV0IG5ld1ZhbHVlID0gdmFsdWU7XG4gICAgICAgIGlmIChpbmRleCAhPT0gbGFzdEluZGV4KSB7XG4gICAgICAgICAgICBjb25zdCBvYmpWYWx1ZSA9IG9iamVjdFtrZXldO1xuICAgICAgICAgICAgbmV3VmFsdWUgPVxuICAgICAgICAgICAgICAgIGlzT2JqZWN0KG9ialZhbHVlKSB8fCBBcnJheS5pc0FycmF5KG9ialZhbHVlKVxuICAgICAgICAgICAgICAgICAgICA/IG9ialZhbHVlXG4gICAgICAgICAgICAgICAgICAgIDogIWlzTmFOKCt0ZW1wUGF0aFtpbmRleCArIDFdKVxuICAgICAgICAgICAgICAgICAgICAgICAgPyBbXVxuICAgICAgICAgICAgICAgICAgICAgICAgOiB7fTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoa2V5ID09PSAnX19wcm90b19fJyB8fCBrZXkgPT09ICdjb25zdHJ1Y3RvcicgfHwga2V5ID09PSAncHJvdG90eXBlJykge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIG9iamVjdFtrZXldID0gbmV3VmFsdWU7XG4gICAgICAgIG9iamVjdCA9IG9iamVjdFtrZXldO1xuICAgIH1cbn07XG5cbmNvbnN0IEVWRU5UUyA9IHtcbiAgICBCTFVSOiAnYmx1cicsXG4gICAgRk9DVVNfT1VUOiAnZm9jdXNvdXQnLFxuICAgIENIQU5HRTogJ2NoYW5nZScsXG59O1xuY29uc3QgVkFMSURBVElPTl9NT0RFID0ge1xuICAgIG9uQmx1cjogJ29uQmx1cicsXG4gICAgb25DaGFuZ2U6ICdvbkNoYW5nZScsXG4gICAgb25TdWJtaXQ6ICdvblN1Ym1pdCcsXG4gICAgb25Ub3VjaGVkOiAnb25Ub3VjaGVkJyxcbiAgICBhbGw6ICdhbGwnLFxufTtcbmNvbnN0IElOUFVUX1ZBTElEQVRJT05fUlVMRVMgPSB7XG4gICAgbWF4OiAnbWF4JyxcbiAgICBtaW46ICdtaW4nLFxuICAgIG1heExlbmd0aDogJ21heExlbmd0aCcsXG4gICAgbWluTGVuZ3RoOiAnbWluTGVuZ3RoJyxcbiAgICBwYXR0ZXJuOiAncGF0dGVybicsXG4gICAgcmVxdWlyZWQ6ICdyZXF1aXJlZCcsXG4gICAgdmFsaWRhdGU6ICd2YWxpZGF0ZScsXG59O1xuXG5jb25zdCBIb29rRm9ybUNvbnRleHQgPSBSZWFjdF9fZGVmYXVsdC5jcmVhdGVDb250ZXh0KG51bGwpO1xuLyoqXG4gKiBUaGlzIGN1c3RvbSBob29rIGFsbG93cyB5b3UgdG8gYWNjZXNzIHRoZSBmb3JtIGNvbnRleHQuIHVzZUZvcm1Db250ZXh0IGlzIGludGVuZGVkIHRvIGJlIHVzZWQgaW4gZGVlcGx5IG5lc3RlZCBzdHJ1Y3R1cmVzLCB3aGVyZSBpdCB3b3VsZCBiZWNvbWUgaW5jb252ZW5pZW50IHRvIHBhc3MgdGhlIGNvbnRleHQgYXMgYSBwcm9wLiBUbyBiZSB1c2VkIHdpdGgge0BsaW5rIEZvcm1Qcm92aWRlcn0uXG4gKlxuICogQHJlbWFya3NcbiAqIFtBUEldKGh0dHBzOi8vcmVhY3QtaG9vay1mb3JtLmNvbS9kb2NzL3VzZWZvcm1jb250ZXh0KSDigKIgW0RlbW9dKGh0dHBzOi8vY29kZXNhbmRib3guaW8vcy9yZWFjdC1ob29rLWZvcm0tdjctZm9ybS1jb250ZXh0LXl0dWRpKVxuICpcbiAqIEByZXR1cm5zIHJldHVybiBhbGwgdXNlRm9ybSBtZXRob2RzXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzeFxuICogZnVuY3Rpb24gQXBwKCkge1xuICogICBjb25zdCBtZXRob2RzID0gdXNlRm9ybSgpO1xuICogICBjb25zdCBvblN1Ym1pdCA9IGRhdGEgPT4gY29uc29sZS5sb2coZGF0YSk7XG4gKlxuICogICByZXR1cm4gKFxuICogICAgIDxGb3JtUHJvdmlkZXIgey4uLm1ldGhvZHN9ID5cbiAqICAgICAgIDxmb3JtIG9uU3VibWl0PXttZXRob2RzLmhhbmRsZVN1Ym1pdChvblN1Ym1pdCl9PlxuICogICAgICAgICA8TmVzdGVkSW5wdXQgLz5cbiAqICAgICAgICAgPGlucHV0IHR5cGU9XCJzdWJtaXRcIiAvPlxuICogICAgICAgPC9mb3JtPlxuICogICAgIDwvRm9ybVByb3ZpZGVyPlxuICogICApO1xuICogfVxuICpcbiAqICBmdW5jdGlvbiBOZXN0ZWRJbnB1dCgpIHtcbiAqICAgY29uc3QgeyByZWdpc3RlciB9ID0gdXNlRm9ybUNvbnRleHQoKTsgLy8gcmV0cmlldmUgYWxsIGhvb2sgbWV0aG9kc1xuICogICByZXR1cm4gPGlucHV0IHsuLi5yZWdpc3RlcihcInRlc3RcIil9IC8+O1xuICogfVxuICogYGBgXG4gKi9cbmNvbnN0IHVzZUZvcm1Db250ZXh0ID0gKCkgPT4gUmVhY3RfX2RlZmF1bHQudXNlQ29udGV4dChIb29rRm9ybUNvbnRleHQpO1xuLyoqXG4gKiBBIHByb3ZpZGVyIGNvbXBvbmVudCB0aGF0IHByb3BhZ2F0ZXMgdGhlIGB1c2VGb3JtYCBtZXRob2RzIHRvIGFsbCBjaGlsZHJlbiBjb21wb25lbnRzIHZpYSBbUmVhY3QgQ29udGV4dF0oaHR0cHM6Ly9yZWFjdGpzLm9yZy9kb2NzL2NvbnRleHQuaHRtbCkgQVBJLiBUbyBiZSB1c2VkIHdpdGgge0BsaW5rIHVzZUZvcm1Db250ZXh0fS5cbiAqXG4gKiBAcmVtYXJrc1xuICogW0FQSV0oaHR0cHM6Ly9yZWFjdC1ob29rLWZvcm0uY29tL2RvY3MvdXNlZm9ybWNvbnRleHQpIOKAoiBbRGVtb10oaHR0cHM6Ly9jb2Rlc2FuZGJveC5pby9zL3JlYWN0LWhvb2stZm9ybS12Ny1mb3JtLWNvbnRleHQteXR1ZGkpXG4gKlxuICogQHBhcmFtIHByb3BzIC0gYWxsIHVzZUZvcm0gbWV0aG9kc1xuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGZ1bmN0aW9uIEFwcCgpIHtcbiAqICAgY29uc3QgbWV0aG9kcyA9IHVzZUZvcm0oKTtcbiAqICAgY29uc3Qgb25TdWJtaXQgPSBkYXRhID0+IGNvbnNvbGUubG9nKGRhdGEpO1xuICpcbiAqICAgcmV0dXJuIChcbiAqICAgICA8Rm9ybVByb3ZpZGVyIHsuLi5tZXRob2RzfSA+XG4gKiAgICAgICA8Zm9ybSBvblN1Ym1pdD17bWV0aG9kcy5oYW5kbGVTdWJtaXQob25TdWJtaXQpfT5cbiAqICAgICAgICAgPE5lc3RlZElucHV0IC8+XG4gKiAgICAgICAgIDxpbnB1dCB0eXBlPVwic3VibWl0XCIgLz5cbiAqICAgICAgIDwvZm9ybT5cbiAqICAgICA8L0Zvcm1Qcm92aWRlcj5cbiAqICAgKTtcbiAqIH1cbiAqXG4gKiAgZnVuY3Rpb24gTmVzdGVkSW5wdXQoKSB7XG4gKiAgIGNvbnN0IHsgcmVnaXN0ZXIgfSA9IHVzZUZvcm1Db250ZXh0KCk7IC8vIHJldHJpZXZlIGFsbCBob29rIG1ldGhvZHNcbiAqICAgcmV0dXJuIDxpbnB1dCB7Li4ucmVnaXN0ZXIoXCJ0ZXN0XCIpfSAvPjtcbiAqIH1cbiAqIGBgYFxuICovXG5jb25zdCBGb3JtUHJvdmlkZXIgPSAocHJvcHMpID0+IHtcbiAgICBjb25zdCB7IGNoaWxkcmVuLCAuLi5kYXRhIH0gPSBwcm9wcztcbiAgICByZXR1cm4gKFJlYWN0X19kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoSG9va0Zvcm1Db250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlOiBkYXRhIH0sIGNoaWxkcmVuKSk7XG59O1xuXG52YXIgZ2V0UHJveHlGb3JtU3RhdGUgPSAoZm9ybVN0YXRlLCBjb250cm9sLCBsb2NhbFByb3h5Rm9ybVN0YXRlLCBpc1Jvb3QgPSB0cnVlKSA9PiB7XG4gICAgY29uc3QgcmVzdWx0ID0ge1xuICAgICAgICBkZWZhdWx0VmFsdWVzOiBjb250cm9sLl9kZWZhdWx0VmFsdWVzLFxuICAgIH07XG4gICAgZm9yIChjb25zdCBrZXkgaW4gZm9ybVN0YXRlKSB7XG4gICAgICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShyZXN1bHQsIGtleSwge1xuICAgICAgICAgICAgZ2V0OiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgX2tleSA9IGtleTtcbiAgICAgICAgICAgICAgICBpZiAoY29udHJvbC5fcHJveHlGb3JtU3RhdGVbX2tleV0gIT09IFZBTElEQVRJT05fTU9ERS5hbGwpIHtcbiAgICAgICAgICAgICAgICAgICAgY29udHJvbC5fcHJveHlGb3JtU3RhdGVbX2tleV0gPSAhaXNSb290IHx8IFZBTElEQVRJT05fTU9ERS5hbGw7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGxvY2FsUHJveHlGb3JtU3RhdGUgJiYgKGxvY2FsUHJveHlGb3JtU3RhdGVbX2tleV0gPSB0cnVlKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gZm9ybVN0YXRlW19rZXldO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG59O1xuXG5jb25zdCB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0ID0gdHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgPyBSZWFjdC51c2VMYXlvdXRFZmZlY3QgOiBSZWFjdC51c2VFZmZlY3Q7XG5cbi8qKlxuICogVGhpcyBjdXN0b20gaG9vayBhbGxvd3MgeW91IHRvIHN1YnNjcmliZSB0byBlYWNoIGZvcm0gc3RhdGUsIGFuZCBpc29sYXRlIHRoZSByZS1yZW5kZXIgYXQgdGhlIGN1c3RvbSBob29rIGxldmVsLiBJdCBoYXMgaXRzIHNjb3BlIGluIHRlcm1zIG9mIGZvcm0gc3RhdGUgc3Vic2NyaXB0aW9uLCBzbyBpdCB3b3VsZCBub3QgYWZmZWN0IG90aGVyIHVzZUZvcm1TdGF0ZSBhbmQgdXNlRm9ybS4gVXNpbmcgdGhpcyBob29rIGNhbiByZWR1Y2UgdGhlIHJlLXJlbmRlciBpbXBhY3Qgb24gbGFyZ2UgYW5kIGNvbXBsZXggZm9ybSBhcHBsaWNhdGlvbi5cbiAqXG4gKiBAcmVtYXJrc1xuICogW0FQSV0oaHR0cHM6Ly9yZWFjdC1ob29rLWZvcm0uY29tL2RvY3MvdXNlZm9ybXN0YXRlKSDigKIgW0RlbW9dKGh0dHBzOi8vY29kZXNhbmRib3guaW8vcy91c2Vmb3Jtc3RhdGUtNzV4bHkpXG4gKlxuICogQHBhcmFtIHByb3BzIC0gaW5jbHVkZSBvcHRpb25zIG9uIHNwZWNpZnkgZmllbGRzIHRvIHN1YnNjcmliZS4ge0BsaW5rIFVzZUZvcm1TdGF0ZVJldHVybn1cbiAqXG4gKiBAZXhhbXBsZVxuICogYGBgdHN4XG4gKiBmdW5jdGlvbiBBcHAoKSB7XG4gKiAgIGNvbnN0IHsgcmVnaXN0ZXIsIGhhbmRsZVN1Ym1pdCwgY29udHJvbCB9ID0gdXNlRm9ybSh7XG4gKiAgICAgZGVmYXVsdFZhbHVlczoge1xuICogICAgIGZpcnN0TmFtZTogXCJmaXJzdE5hbWVcIlxuICogICB9fSk7XG4gKiAgIGNvbnN0IHsgZGlydHlGaWVsZHMgfSA9IHVzZUZvcm1TdGF0ZSh7XG4gKiAgICAgY29udHJvbFxuICogICB9KTtcbiAqICAgY29uc3Qgb25TdWJtaXQgPSAoZGF0YSkgPT4gY29uc29sZS5sb2coZGF0YSk7XG4gKlxuICogICByZXR1cm4gKFxuICogICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVTdWJtaXQob25TdWJtaXQpfT5cbiAqICAgICAgIDxpbnB1dCB7Li4ucmVnaXN0ZXIoXCJmaXJzdE5hbWVcIil9IHBsYWNlaG9sZGVyPVwiRmlyc3QgTmFtZVwiIC8+XG4gKiAgICAgICB7ZGlydHlGaWVsZHMuZmlyc3ROYW1lICYmIDxwPkZpZWxkIGlzIGRpcnR5LjwvcD59XG4gKiAgICAgICA8aW5wdXQgdHlwZT1cInN1Ym1pdFwiIC8+XG4gKiAgICAgPC9mb3JtPlxuICogICApO1xuICogfVxuICogYGBgXG4gKi9cbmZ1bmN0aW9uIHVzZUZvcm1TdGF0ZShwcm9wcykge1xuICAgIGNvbnN0IG1ldGhvZHMgPSB1c2VGb3JtQ29udGV4dCgpO1xuICAgIGNvbnN0IHsgY29udHJvbCA9IG1ldGhvZHMuY29udHJvbCwgZGlzYWJsZWQsIG5hbWUsIGV4YWN0IH0gPSBwcm9wcyB8fCB7fTtcbiAgICBjb25zdCBbZm9ybVN0YXRlLCB1cGRhdGVGb3JtU3RhdGVdID0gUmVhY3RfX2RlZmF1bHQudXNlU3RhdGUoY29udHJvbC5fZm9ybVN0YXRlKTtcbiAgICBjb25zdCBfbG9jYWxQcm94eUZvcm1TdGF0ZSA9IFJlYWN0X19kZWZhdWx0LnVzZVJlZih7XG4gICAgICAgIGlzRGlydHk6IGZhbHNlLFxuICAgICAgICBpc0xvYWRpbmc6IGZhbHNlLFxuICAgICAgICBkaXJ0eUZpZWxkczogZmFsc2UsXG4gICAgICAgIHRvdWNoZWRGaWVsZHM6IGZhbHNlLFxuICAgICAgICB2YWxpZGF0aW5nRmllbGRzOiBmYWxzZSxcbiAgICAgICAgaXNWYWxpZGF0aW5nOiBmYWxzZSxcbiAgICAgICAgaXNWYWxpZDogZmFsc2UsXG4gICAgICAgIGVycm9yczogZmFsc2UsXG4gICAgfSk7XG4gICAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCgoKSA9PiBjb250cm9sLl9zdWJzY3JpYmUoe1xuICAgICAgICBuYW1lOiBuYW1lLFxuICAgICAgICBmb3JtU3RhdGU6IF9sb2NhbFByb3h5Rm9ybVN0YXRlLmN1cnJlbnQsXG4gICAgICAgIGV4YWN0LFxuICAgICAgICBjYWxsYmFjazogKGZvcm1TdGF0ZSkgPT4ge1xuICAgICAgICAgICAgIWRpc2FibGVkICYmXG4gICAgICAgICAgICAgICAgdXBkYXRlRm9ybVN0YXRlKHtcbiAgICAgICAgICAgICAgICAgICAgLi4uY29udHJvbC5fZm9ybVN0YXRlLFxuICAgICAgICAgICAgICAgICAgICAuLi5mb3JtU3RhdGUsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgIH0sXG4gICAgfSksIFtuYW1lLCBkaXNhYmxlZCwgZXhhY3RdKTtcbiAgICBSZWFjdF9fZGVmYXVsdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBfbG9jYWxQcm94eUZvcm1TdGF0ZS5jdXJyZW50LmlzVmFsaWQgJiYgY29udHJvbC5fc2V0VmFsaWQodHJ1ZSk7XG4gICAgfSwgW2NvbnRyb2xdKTtcbiAgICByZXR1cm4gUmVhY3RfX2RlZmF1bHQudXNlTWVtbygoKSA9PiBnZXRQcm94eUZvcm1TdGF0ZShmb3JtU3RhdGUsIGNvbnRyb2wsIF9sb2NhbFByb3h5Rm9ybVN0YXRlLmN1cnJlbnQsIGZhbHNlKSwgW2Zvcm1TdGF0ZSwgY29udHJvbF0pO1xufVxuXG52YXIgaXNTdHJpbmcgPSAodmFsdWUpID0+IHR5cGVvZiB2YWx1ZSA9PT0gJ3N0cmluZyc7XG5cbnZhciBnZW5lcmF0ZVdhdGNoT3V0cHV0ID0gKG5hbWVzLCBfbmFtZXMsIGZvcm1WYWx1ZXMsIGlzR2xvYmFsLCBkZWZhdWx0VmFsdWUpID0+IHtcbiAgICBpZiAoaXNTdHJpbmcobmFtZXMpKSB7XG4gICAgICAgIGlzR2xvYmFsICYmIF9uYW1lcy53YXRjaC5hZGQobmFtZXMpO1xuICAgICAgICByZXR1cm4gZ2V0KGZvcm1WYWx1ZXMsIG5hbWVzLCBkZWZhdWx0VmFsdWUpO1xuICAgIH1cbiAgICBpZiAoQXJyYXkuaXNBcnJheShuYW1lcykpIHtcbiAgICAgICAgcmV0dXJuIG5hbWVzLm1hcCgoZmllbGROYW1lKSA9PiAoaXNHbG9iYWwgJiYgX25hbWVzLndhdGNoLmFkZChmaWVsZE5hbWUpLCBnZXQoZm9ybVZhbHVlcywgZmllbGROYW1lKSkpO1xuICAgIH1cbiAgICBpc0dsb2JhbCAmJiAoX25hbWVzLndhdGNoQWxsID0gdHJ1ZSk7XG4gICAgcmV0dXJuIGZvcm1WYWx1ZXM7XG59O1xuXG4vKipcbiAqIEN1c3RvbSBob29rIHRvIHN1YnNjcmliZSB0byBmaWVsZCBjaGFuZ2UgYW5kIGlzb2xhdGUgcmUtcmVuZGVyaW5nIGF0IHRoZSBjb21wb25lbnQgbGV2ZWwuXG4gKlxuICogQHJlbWFya3NcbiAqXG4gKiBbQVBJXShodHRwczovL3JlYWN0LWhvb2stZm9ybS5jb20vZG9jcy91c2V3YXRjaCkg4oCiIFtEZW1vXShodHRwczovL2NvZGVzYW5kYm94LmlvL3MvcmVhY3QtaG9vay1mb3JtLXY3LXRzLXVzZXdhdGNoLWg5aTVlKVxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGNvbnN0IHsgY29udHJvbCB9ID0gdXNlRm9ybSgpO1xuICogY29uc3QgdmFsdWVzID0gdXNlV2F0Y2goe1xuICogICBuYW1lOiBcImZpZWxkTmFtZVwiXG4gKiAgIGNvbnRyb2wsXG4gKiB9KVxuICogYGBgXG4gKi9cbmZ1bmN0aW9uIHVzZVdhdGNoKHByb3BzKSB7XG4gICAgY29uc3QgbWV0aG9kcyA9IHVzZUZvcm1Db250ZXh0KCk7XG4gICAgY29uc3QgeyBjb250cm9sID0gbWV0aG9kcy5jb250cm9sLCBuYW1lLCBkZWZhdWx0VmFsdWUsIGRpc2FibGVkLCBleGFjdCwgfSA9IHByb3BzIHx8IHt9O1xuICAgIGNvbnN0IF9kZWZhdWx0VmFsdWUgPSBSZWFjdF9fZGVmYXVsdC51c2VSZWYoZGVmYXVsdFZhbHVlKTtcbiAgICBjb25zdCBbdmFsdWUsIHVwZGF0ZVZhbHVlXSA9IFJlYWN0X19kZWZhdWx0LnVzZVN0YXRlKGNvbnRyb2wuX2dldFdhdGNoKG5hbWUsIF9kZWZhdWx0VmFsdWUuY3VycmVudCkpO1xuICAgIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoKCkgPT4gY29udHJvbC5fc3Vic2NyaWJlKHtcbiAgICAgICAgbmFtZTogbmFtZSxcbiAgICAgICAgZm9ybVN0YXRlOiB7XG4gICAgICAgICAgICB2YWx1ZXM6IHRydWUsXG4gICAgICAgIH0sXG4gICAgICAgIGV4YWN0LFxuICAgICAgICBjYWxsYmFjazogKGZvcm1TdGF0ZSkgPT4gIWRpc2FibGVkICYmXG4gICAgICAgICAgICB1cGRhdGVWYWx1ZShnZW5lcmF0ZVdhdGNoT3V0cHV0KG5hbWUsIGNvbnRyb2wuX25hbWVzLCBmb3JtU3RhdGUudmFsdWVzIHx8IGNvbnRyb2wuX2Zvcm1WYWx1ZXMsIGZhbHNlLCBfZGVmYXVsdFZhbHVlLmN1cnJlbnQpKSxcbiAgICB9KSwgW25hbWUsIGNvbnRyb2wsIGRpc2FibGVkLCBleGFjdF0pO1xuICAgIFJlYWN0X19kZWZhdWx0LnVzZUVmZmVjdCgoKSA9PiBjb250cm9sLl9yZW1vdmVVbm1vdW50ZWQoKSk7XG4gICAgcmV0dXJuIHZhbHVlO1xufVxuXG4vKipcbiAqIEN1c3RvbSBob29rIHRvIHdvcmsgd2l0aCBjb250cm9sbGVkIGNvbXBvbmVudCwgdGhpcyBmdW5jdGlvbiBwcm92aWRlIHlvdSB3aXRoIGJvdGggZm9ybSBhbmQgZmllbGQgbGV2ZWwgc3RhdGUuIFJlLXJlbmRlciBpcyBpc29sYXRlZCBhdCB0aGUgaG9vayBsZXZlbC5cbiAqXG4gKiBAcmVtYXJrc1xuICogW0FQSV0oaHR0cHM6Ly9yZWFjdC1ob29rLWZvcm0uY29tL2RvY3MvdXNlY29udHJvbGxlcikg4oCiIFtEZW1vXShodHRwczovL2NvZGVzYW5kYm94LmlvL3MvdXNlY29udHJvbGxlci0wbzhweClcbiAqXG4gKiBAcGFyYW0gcHJvcHMgLSB0aGUgcGF0aCBuYW1lIHRvIHRoZSBmb3JtIGZpZWxkIHZhbHVlLCBhbmQgdmFsaWRhdGlvbiBydWxlcy5cbiAqXG4gKiBAcmV0dXJucyBmaWVsZCBwcm9wZXJ0aWVzLCBmaWVsZCBhbmQgZm9ybSBzdGF0ZS4ge0BsaW5rIFVzZUNvbnRyb2xsZXJSZXR1cm59XG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzeFxuICogZnVuY3Rpb24gSW5wdXQocHJvcHMpIHtcbiAqICAgY29uc3QgeyBmaWVsZCwgZmllbGRTdGF0ZSwgZm9ybVN0YXRlIH0gPSB1c2VDb250cm9sbGVyKHByb3BzKTtcbiAqICAgcmV0dXJuIChcbiAqICAgICA8ZGl2PlxuICogICAgICAgPGlucHV0IHsuLi5maWVsZH0gcGxhY2Vob2xkZXI9e3Byb3BzLm5hbWV9IC8+XG4gKiAgICAgICA8cD57ZmllbGRTdGF0ZS5pc1RvdWNoZWQgJiYgXCJUb3VjaGVkXCJ9PC9wPlxuICogICAgICAgPHA+e2Zvcm1TdGF0ZS5pc1N1Ym1pdHRlZCA/IFwic3VibWl0dGVkXCIgOiBcIlwifTwvcD5cbiAqICAgICA8L2Rpdj5cbiAqICAgKTtcbiAqIH1cbiAqIGBgYFxuICovXG5mdW5jdGlvbiB1c2VDb250cm9sbGVyKHByb3BzKSB7XG4gICAgY29uc3QgbWV0aG9kcyA9IHVzZUZvcm1Db250ZXh0KCk7XG4gICAgY29uc3QgeyBuYW1lLCBkaXNhYmxlZCwgY29udHJvbCA9IG1ldGhvZHMuY29udHJvbCwgc2hvdWxkVW5yZWdpc3RlciB9ID0gcHJvcHM7XG4gICAgY29uc3QgaXNBcnJheUZpZWxkID0gaXNOYW1lSW5GaWVsZEFycmF5KGNvbnRyb2wuX25hbWVzLmFycmF5LCBuYW1lKTtcbiAgICBjb25zdCB2YWx1ZSA9IHVzZVdhdGNoKHtcbiAgICAgICAgY29udHJvbCxcbiAgICAgICAgbmFtZSxcbiAgICAgICAgZGVmYXVsdFZhbHVlOiBnZXQoY29udHJvbC5fZm9ybVZhbHVlcywgbmFtZSwgZ2V0KGNvbnRyb2wuX2RlZmF1bHRWYWx1ZXMsIG5hbWUsIHByb3BzLmRlZmF1bHRWYWx1ZSkpLFxuICAgICAgICBleGFjdDogdHJ1ZSxcbiAgICB9KTtcbiAgICBjb25zdCBmb3JtU3RhdGUgPSB1c2VGb3JtU3RhdGUoe1xuICAgICAgICBjb250cm9sLFxuICAgICAgICBuYW1lLFxuICAgICAgICBleGFjdDogdHJ1ZSxcbiAgICB9KTtcbiAgICBjb25zdCBfcHJvcHMgPSBSZWFjdF9fZGVmYXVsdC51c2VSZWYocHJvcHMpO1xuICAgIGNvbnN0IF9yZWdpc3RlclByb3BzID0gUmVhY3RfX2RlZmF1bHQudXNlUmVmKGNvbnRyb2wucmVnaXN0ZXIobmFtZSwge1xuICAgICAgICAuLi5wcm9wcy5ydWxlcyxcbiAgICAgICAgdmFsdWUsXG4gICAgICAgIC4uLihpc0Jvb2xlYW4ocHJvcHMuZGlzYWJsZWQpID8geyBkaXNhYmxlZDogcHJvcHMuZGlzYWJsZWQgfSA6IHt9KSxcbiAgICB9KSk7XG4gICAgY29uc3QgZmllbGRTdGF0ZSA9IFJlYWN0X19kZWZhdWx0LnVzZU1lbW8oKCkgPT4gT2JqZWN0LmRlZmluZVByb3BlcnRpZXMoe30sIHtcbiAgICAgICAgaW52YWxpZDoge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGdldDogKCkgPT4gISFnZXQoZm9ybVN0YXRlLmVycm9ycywgbmFtZSksXG4gICAgICAgIH0sXG4gICAgICAgIGlzRGlydHk6IHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBnZXQ6ICgpID0+ICEhZ2V0KGZvcm1TdGF0ZS5kaXJ0eUZpZWxkcywgbmFtZSksXG4gICAgICAgIH0sXG4gICAgICAgIGlzVG91Y2hlZDoge1xuICAgICAgICAgICAgZW51bWVyYWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGdldDogKCkgPT4gISFnZXQoZm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMsIG5hbWUpLFxuICAgICAgICB9LFxuICAgICAgICBpc1ZhbGlkYXRpbmc6IHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBnZXQ6ICgpID0+ICEhZ2V0KGZvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzLCBuYW1lKSxcbiAgICAgICAgfSxcbiAgICAgICAgZXJyb3I6IHtcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICBnZXQ6ICgpID0+IGdldChmb3JtU3RhdGUuZXJyb3JzLCBuYW1lKSxcbiAgICAgICAgfSxcbiAgICB9KSwgW2Zvcm1TdGF0ZSwgbmFtZV0pO1xuICAgIGNvbnN0IG9uQ2hhbmdlID0gUmVhY3RfX2RlZmF1bHQudXNlQ2FsbGJhY2soKGV2ZW50KSA9PiBfcmVnaXN0ZXJQcm9wcy5jdXJyZW50Lm9uQ2hhbmdlKHtcbiAgICAgICAgdGFyZ2V0OiB7XG4gICAgICAgICAgICB2YWx1ZTogZ2V0RXZlbnRWYWx1ZShldmVudCksXG4gICAgICAgICAgICBuYW1lOiBuYW1lLFxuICAgICAgICB9LFxuICAgICAgICB0eXBlOiBFVkVOVFMuQ0hBTkdFLFxuICAgIH0pLCBbbmFtZV0pO1xuICAgIGNvbnN0IG9uQmx1ciA9IFJlYWN0X19kZWZhdWx0LnVzZUNhbGxiYWNrKCgpID0+IF9yZWdpc3RlclByb3BzLmN1cnJlbnQub25CbHVyKHtcbiAgICAgICAgdGFyZ2V0OiB7XG4gICAgICAgICAgICB2YWx1ZTogZ2V0KGNvbnRyb2wuX2Zvcm1WYWx1ZXMsIG5hbWUpLFxuICAgICAgICAgICAgbmFtZTogbmFtZSxcbiAgICAgICAgfSxcbiAgICAgICAgdHlwZTogRVZFTlRTLkJMVVIsXG4gICAgfSksIFtuYW1lLCBjb250cm9sLl9mb3JtVmFsdWVzXSk7XG4gICAgY29uc3QgcmVmID0gUmVhY3RfX2RlZmF1bHQudXNlQ2FsbGJhY2soKGVsbSkgPT4ge1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChjb250cm9sLl9maWVsZHMsIG5hbWUpO1xuICAgICAgICBpZiAoZmllbGQgJiYgZWxtKSB7XG4gICAgICAgICAgICBmaWVsZC5fZi5yZWYgPSB7XG4gICAgICAgICAgICAgICAgZm9jdXM6ICgpID0+IGVsbS5mb2N1cygpLFxuICAgICAgICAgICAgICAgIHNlbGVjdDogKCkgPT4gZWxtLnNlbGVjdCgpLFxuICAgICAgICAgICAgICAgIHNldEN1c3RvbVZhbGlkaXR5OiAobWVzc2FnZSkgPT4gZWxtLnNldEN1c3RvbVZhbGlkaXR5KG1lc3NhZ2UpLFxuICAgICAgICAgICAgICAgIHJlcG9ydFZhbGlkaXR5OiAoKSA9PiBlbG0ucmVwb3J0VmFsaWRpdHkoKSxcbiAgICAgICAgICAgIH07XG4gICAgICAgIH1cbiAgICB9LCBbY29udHJvbC5fZmllbGRzLCBuYW1lXSk7XG4gICAgY29uc3QgZmllbGQgPSBSZWFjdF9fZGVmYXVsdC51c2VNZW1vKCgpID0+ICh7XG4gICAgICAgIG5hbWUsXG4gICAgICAgIHZhbHVlLFxuICAgICAgICAuLi4oaXNCb29sZWFuKGRpc2FibGVkKSB8fCBmb3JtU3RhdGUuZGlzYWJsZWRcbiAgICAgICAgICAgID8geyBkaXNhYmxlZDogZm9ybVN0YXRlLmRpc2FibGVkIHx8IGRpc2FibGVkIH1cbiAgICAgICAgICAgIDoge30pLFxuICAgICAgICBvbkNoYW5nZSxcbiAgICAgICAgb25CbHVyLFxuICAgICAgICByZWYsXG4gICAgfSksIFtuYW1lLCBkaXNhYmxlZCwgZm9ybVN0YXRlLmRpc2FibGVkLCBvbkNoYW5nZSwgb25CbHVyLCByZWYsIHZhbHVlXSk7XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgY29uc3QgX3Nob3VsZFVucmVnaXN0ZXJGaWVsZCA9IGNvbnRyb2wuX29wdGlvbnMuc2hvdWxkVW5yZWdpc3RlciB8fCBzaG91bGRVbnJlZ2lzdGVyO1xuICAgICAgICBjb250cm9sLnJlZ2lzdGVyKG5hbWUsIHtcbiAgICAgICAgICAgIC4uLl9wcm9wcy5jdXJyZW50LnJ1bGVzLFxuICAgICAgICAgICAgLi4uKGlzQm9vbGVhbihfcHJvcHMuY3VycmVudC5kaXNhYmxlZClcbiAgICAgICAgICAgICAgICA/IHsgZGlzYWJsZWQ6IF9wcm9wcy5jdXJyZW50LmRpc2FibGVkIH1cbiAgICAgICAgICAgICAgICA6IHt9KSxcbiAgICAgICAgfSk7XG4gICAgICAgIGNvbnN0IHVwZGF0ZU1vdW50ZWQgPSAobmFtZSwgdmFsdWUpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KGNvbnRyb2wuX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICBpZiAoZmllbGQgJiYgZmllbGQuX2YpIHtcbiAgICAgICAgICAgICAgICBmaWVsZC5fZi5tb3VudCA9IHZhbHVlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgICB1cGRhdGVNb3VudGVkKG5hbWUsIHRydWUpO1xuICAgICAgICBpZiAoX3Nob3VsZFVucmVnaXN0ZXJGaWVsZCkge1xuICAgICAgICAgICAgY29uc3QgdmFsdWUgPSBjbG9uZU9iamVjdChnZXQoY29udHJvbC5fb3B0aW9ucy5kZWZhdWx0VmFsdWVzLCBuYW1lKSk7XG4gICAgICAgICAgICBzZXQoY29udHJvbC5fZGVmYXVsdFZhbHVlcywgbmFtZSwgdmFsdWUpO1xuICAgICAgICAgICAgaWYgKGlzVW5kZWZpbmVkKGdldChjb250cm9sLl9mb3JtVmFsdWVzLCBuYW1lKSkpIHtcbiAgICAgICAgICAgICAgICBzZXQoY29udHJvbC5fZm9ybVZhbHVlcywgbmFtZSwgdmFsdWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgICFpc0FycmF5RmllbGQgJiYgY29udHJvbC5yZWdpc3RlcihuYW1lKTtcbiAgICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgICAgIChpc0FycmF5RmllbGRcbiAgICAgICAgICAgICAgICA/IF9zaG91bGRVbnJlZ2lzdGVyRmllbGQgJiYgIWNvbnRyb2wuX3N0YXRlLmFjdGlvblxuICAgICAgICAgICAgICAgIDogX3Nob3VsZFVucmVnaXN0ZXJGaWVsZClcbiAgICAgICAgICAgICAgICA/IGNvbnRyb2wudW5yZWdpc3RlcihuYW1lKVxuICAgICAgICAgICAgICAgIDogdXBkYXRlTW91bnRlZChuYW1lLCBmYWxzZSk7XG4gICAgICAgIH07XG4gICAgfSwgW25hbWUsIGNvbnRyb2wsIGlzQXJyYXlGaWVsZCwgc2hvdWxkVW5yZWdpc3Rlcl0pO1xuICAgIFJlYWN0X19kZWZhdWx0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGNvbnRyb2wuX3NldERpc2FibGVkRmllbGQoe1xuICAgICAgICAgICAgZGlzYWJsZWQsXG4gICAgICAgICAgICBuYW1lLFxuICAgICAgICB9KTtcbiAgICB9LCBbZGlzYWJsZWQsIG5hbWUsIGNvbnRyb2xdKTtcbiAgICByZXR1cm4gUmVhY3RfX2RlZmF1bHQudXNlTWVtbygoKSA9PiAoe1xuICAgICAgICBmaWVsZCxcbiAgICAgICAgZm9ybVN0YXRlLFxuICAgICAgICBmaWVsZFN0YXRlLFxuICAgIH0pLCBbZmllbGQsIGZvcm1TdGF0ZSwgZmllbGRTdGF0ZV0pO1xufVxuXG4vKipcbiAqIENvbXBvbmVudCBiYXNlZCBvbiBgdXNlQ29udHJvbGxlcmAgaG9vayB0byB3b3JrIHdpdGggY29udHJvbGxlZCBjb21wb25lbnQuXG4gKlxuICogQHJlbWFya3NcbiAqIFtBUEldKGh0dHBzOi8vcmVhY3QtaG9vay1mb3JtLmNvbS9kb2NzL3VzZWNvbnRyb2xsZXIvY29udHJvbGxlcikg4oCiIFtEZW1vXShodHRwczovL2NvZGVzYW5kYm94LmlvL3MvcmVhY3QtaG9vay1mb3JtLXY2LWNvbnRyb2xsZXItdHMtand5encpIOKAoiBbVmlkZW9dKGh0dHBzOi8vd3d3LnlvdXR1YmUuY29tL3dhdGNoP3Y9TjJVTmtfVUNWeUEpXG4gKlxuICogQHBhcmFtIHByb3BzIC0gdGhlIHBhdGggbmFtZSB0byB0aGUgZm9ybSBmaWVsZCB2YWx1ZSwgYW5kIHZhbGlkYXRpb24gcnVsZXMuXG4gKlxuICogQHJldHVybnMgcHJvdmlkZSBmaWVsZCBoYW5kbGVyIGZ1bmN0aW9ucywgZmllbGQgYW5kIGZvcm0gc3RhdGUuXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzeFxuICogZnVuY3Rpb24gQXBwKCkge1xuICogICBjb25zdCB7IGNvbnRyb2wgfSA9IHVzZUZvcm08Rm9ybVZhbHVlcz4oe1xuICogICAgIGRlZmF1bHRWYWx1ZXM6IHtcbiAqICAgICAgIHRlc3Q6IFwiXCJcbiAqICAgICB9XG4gKiAgIH0pO1xuICpcbiAqICAgcmV0dXJuIChcbiAqICAgICA8Zm9ybT5cbiAqICAgICAgIDxDb250cm9sbGVyXG4gKiAgICAgICAgIGNvbnRyb2w9e2NvbnRyb2x9XG4gKiAgICAgICAgIG5hbWU9XCJ0ZXN0XCJcbiAqICAgICAgICAgcmVuZGVyPXsoeyBmaWVsZDogeyBvbkNoYW5nZSwgb25CbHVyLCB2YWx1ZSwgcmVmIH0sIGZvcm1TdGF0ZSwgZmllbGRTdGF0ZSB9KSA9PiAoXG4gKiAgICAgICAgICAgPD5cbiAqICAgICAgICAgICAgIDxpbnB1dFxuICogICAgICAgICAgICAgICBvbkNoYW5nZT17b25DaGFuZ2V9IC8vIHNlbmQgdmFsdWUgdG8gaG9vayBmb3JtXG4gKiAgICAgICAgICAgICAgIG9uQmx1cj17b25CbHVyfSAvLyBub3RpZnkgd2hlbiBpbnB1dCBpcyB0b3VjaGVkXG4gKiAgICAgICAgICAgICAgIHZhbHVlPXt2YWx1ZX0gLy8gcmV0dXJuIHVwZGF0ZWQgdmFsdWVcbiAqICAgICAgICAgICAgICAgcmVmPXtyZWZ9IC8vIHNldCByZWYgZm9yIGZvY3VzIG1hbmFnZW1lbnRcbiAqICAgICAgICAgICAgIC8+XG4gKiAgICAgICAgICAgICA8cD57Zm9ybVN0YXRlLmlzU3VibWl0dGVkID8gXCJzdWJtaXR0ZWRcIiA6IFwiXCJ9PC9wPlxuICogICAgICAgICAgICAgPHA+e2ZpZWxkU3RhdGUuaXNUb3VjaGVkID8gXCJ0b3VjaGVkXCIgOiBcIlwifTwvcD5cbiAqICAgICAgICAgICA8Lz5cbiAqICAgICAgICAgKX1cbiAqICAgICAgIC8+XG4gKiAgICAgPC9mb3JtPlxuICogICApO1xuICogfVxuICogYGBgXG4gKi9cbmNvbnN0IENvbnRyb2xsZXIgPSAocHJvcHMpID0+IHByb3BzLnJlbmRlcih1c2VDb250cm9sbGVyKHByb3BzKSk7XG5cbmNvbnN0IGZsYXR0ZW4gPSAob2JqKSA9PiB7XG4gICAgY29uc3Qgb3V0cHV0ID0ge307XG4gICAgZm9yIChjb25zdCBrZXkgb2YgT2JqZWN0LmtleXMob2JqKSkge1xuICAgICAgICBpZiAoaXNPYmplY3RUeXBlKG9ialtrZXldKSAmJiBvYmpba2V5XSAhPT0gbnVsbCkge1xuICAgICAgICAgICAgY29uc3QgbmVzdGVkID0gZmxhdHRlbihvYmpba2V5XSk7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IG5lc3RlZEtleSBvZiBPYmplY3Qua2V5cyhuZXN0ZWQpKSB7XG4gICAgICAgICAgICAgICAgb3V0cHV0W2Ake2tleX0uJHtuZXN0ZWRLZXl9YF0gPSBuZXN0ZWRbbmVzdGVkS2V5XTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIG91dHB1dFtrZXldID0gb2JqW2tleV07XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIG91dHB1dDtcbn07XG5cbmNvbnN0IFBPU1RfUkVRVUVTVCA9ICdwb3N0Jztcbi8qKlxuICogRm9ybSBjb21wb25lbnQgdG8gbWFuYWdlIHN1Ym1pc3Npb24uXG4gKlxuICogQHBhcmFtIHByb3BzIC0gdG8gc2V0dXAgc3VibWlzc2lvbiBkZXRhaWwuIHtAbGluayBGb3JtUHJvcHN9XG4gKlxuICogQHJldHVybnMgZm9ybSBjb21wb25lbnQgb3IgaGVhZGxlc3MgcmVuZGVyIHByb3AuXG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzeFxuICogZnVuY3Rpb24gQXBwKCkge1xuICogICBjb25zdCB7IGNvbnRyb2wsIGZvcm1TdGF0ZTogeyBlcnJvcnMgfSB9ID0gdXNlRm9ybSgpO1xuICpcbiAqICAgcmV0dXJuIChcbiAqICAgICA8Rm9ybSBhY3Rpb249XCIvYXBpXCIgY29udHJvbD17Y29udHJvbH0+XG4gKiAgICAgICA8aW5wdXQgey4uLnJlZ2lzdGVyKFwibmFtZVwiKX0gLz5cbiAqICAgICAgIDxwPntlcnJvcnM/LnJvb3Q/LnNlcnZlciAmJiAnU2VydmVyIGVycm9yJ308L3A+XG4gKiAgICAgICA8YnV0dG9uPlN1Ym1pdDwvYnV0dG9uPlxuICogICAgIDwvRm9ybT5cbiAqICAgKTtcbiAqIH1cbiAqIGBgYFxuICovXG5mdW5jdGlvbiBGb3JtKHByb3BzKSB7XG4gICAgY29uc3QgbWV0aG9kcyA9IHVzZUZvcm1Db250ZXh0KCk7XG4gICAgY29uc3QgW21vdW50ZWQsIHNldE1vdW50ZWRdID0gUmVhY3RfX2RlZmF1bHQudXNlU3RhdGUoZmFsc2UpO1xuICAgIGNvbnN0IHsgY29udHJvbCA9IG1ldGhvZHMuY29udHJvbCwgb25TdWJtaXQsIGNoaWxkcmVuLCBhY3Rpb24sIG1ldGhvZCA9IFBPU1RfUkVRVUVTVCwgaGVhZGVycywgZW5jVHlwZSwgb25FcnJvciwgcmVuZGVyLCBvblN1Y2Nlc3MsIHZhbGlkYXRlU3RhdHVzLCAuLi5yZXN0IH0gPSBwcm9wcztcbiAgICBjb25zdCBzdWJtaXQgPSBhc3luYyAoZXZlbnQpID0+IHtcbiAgICAgICAgbGV0IGhhc0Vycm9yID0gZmFsc2U7XG4gICAgICAgIGxldCB0eXBlID0gJyc7XG4gICAgICAgIGF3YWl0IGNvbnRyb2wuaGFuZGxlU3VibWl0KGFzeW5jIChkYXRhKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpO1xuICAgICAgICAgICAgbGV0IGZvcm1EYXRhSnNvbiA9ICcnO1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBmb3JtRGF0YUpzb24gPSBKU09OLnN0cmluZ2lmeShkYXRhKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChfYSkgeyB9XG4gICAgICAgICAgICBjb25zdCBmbGF0dGVuRm9ybVZhbHVlcyA9IGZsYXR0ZW4oY29udHJvbC5fZm9ybVZhbHVlcyk7XG4gICAgICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiBmbGF0dGVuRm9ybVZhbHVlcykge1xuICAgICAgICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZChrZXksIGZsYXR0ZW5Gb3JtVmFsdWVzW2tleV0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKG9uU3VibWl0KSB7XG4gICAgICAgICAgICAgICAgYXdhaXQgb25TdWJtaXQoe1xuICAgICAgICAgICAgICAgICAgICBkYXRhLFxuICAgICAgICAgICAgICAgICAgICBldmVudCxcbiAgICAgICAgICAgICAgICAgICAgbWV0aG9kLFxuICAgICAgICAgICAgICAgICAgICBmb3JtRGF0YSxcbiAgICAgICAgICAgICAgICAgICAgZm9ybURhdGFKc29uLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGFjdGlvbikge1xuICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHNob3VsZFN0cmluZ2lmeVN1Ym1pc3Npb25EYXRhID0gW1xuICAgICAgICAgICAgICAgICAgICAgICAgaGVhZGVycyAmJiBoZWFkZXJzWydDb250ZW50LVR5cGUnXSxcbiAgICAgICAgICAgICAgICAgICAgICAgIGVuY1R5cGUsXG4gICAgICAgICAgICAgICAgICAgIF0uc29tZSgodmFsdWUpID0+IHZhbHVlICYmIHZhbHVlLmluY2x1ZGVzKCdqc29uJykpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKFN0cmluZyhhY3Rpb24pLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICBtZXRob2QsXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uaGVhZGVycyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi4oZW5jVHlwZSA/IHsgJ0NvbnRlbnQtVHlwZSc6IGVuY1R5cGUgfSA6IHt9KSxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICBib2R5OiBzaG91bGRTdHJpbmdpZnlTdWJtaXNzaW9uRGF0YSA/IGZvcm1EYXRhSnNvbiA6IGZvcm1EYXRhLFxuICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAodmFsaWRhdGVTdGF0dXNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICF2YWxpZGF0ZVN0YXR1cyhyZXNwb25zZS5zdGF0dXMpXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiByZXNwb25zZS5zdGF0dXMgPCAyMDAgfHwgcmVzcG9uc2Uuc3RhdHVzID49IDMwMCkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGhhc0Vycm9yID0gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uRXJyb3IgJiYgb25FcnJvcih7IHJlc3BvbnNlIH0pO1xuICAgICAgICAgICAgICAgICAgICAgICAgdHlwZSA9IFN0cmluZyhyZXNwb25zZS5zdGF0dXMpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAgICAgb25TdWNjZXNzICYmIG9uU3VjY2Vzcyh7IHJlc3BvbnNlIH0pO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgICAgICBoYXNFcnJvciA9IHRydWU7XG4gICAgICAgICAgICAgICAgICAgIG9uRXJyb3IgJiYgb25FcnJvcih7IGVycm9yIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSkoZXZlbnQpO1xuICAgICAgICBpZiAoaGFzRXJyb3IgJiYgcHJvcHMuY29udHJvbCkge1xuICAgICAgICAgICAgcHJvcHMuY29udHJvbC5fc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgaXNTdWJtaXRTdWNjZXNzZnVsOiBmYWxzZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgcHJvcHMuY29udHJvbC5zZXRFcnJvcigncm9vdC5zZXJ2ZXInLCB7XG4gICAgICAgICAgICAgICAgdHlwZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBSZWFjdF9fZGVmYXVsdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBzZXRNb3VudGVkKHRydWUpO1xuICAgIH0sIFtdKTtcbiAgICByZXR1cm4gcmVuZGVyID8gKFJlYWN0X19kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoUmVhY3RfX2RlZmF1bHQuRnJhZ21lbnQsIG51bGwsIHJlbmRlcih7XG4gICAgICAgIHN1Ym1pdCxcbiAgICB9KSkpIDogKFJlYWN0X19kZWZhdWx0LmNyZWF0ZUVsZW1lbnQoXCJmb3JtXCIsIHsgbm9WYWxpZGF0ZTogbW91bnRlZCwgYWN0aW9uOiBhY3Rpb24sIG1ldGhvZDogbWV0aG9kLCBlbmNUeXBlOiBlbmNUeXBlLCBvblN1Ym1pdDogc3VibWl0LCAuLi5yZXN0IH0sIGNoaWxkcmVuKSk7XG59XG5cbnZhciBhcHBlbmRFcnJvcnMgPSAobmFtZSwgdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhLCBlcnJvcnMsIHR5cGUsIG1lc3NhZ2UpID0+IHZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYVxuICAgID8ge1xuICAgICAgICAuLi5lcnJvcnNbbmFtZV0sXG4gICAgICAgIHR5cGVzOiB7XG4gICAgICAgICAgICAuLi4oZXJyb3JzW25hbWVdICYmIGVycm9yc1tuYW1lXS50eXBlcyA/IGVycm9yc1tuYW1lXS50eXBlcyA6IHt9KSxcbiAgICAgICAgICAgIFt0eXBlXTogbWVzc2FnZSB8fCB0cnVlLFxuICAgICAgICB9LFxuICAgIH1cbiAgICA6IHt9O1xuXG52YXIgY29udmVydFRvQXJyYXlQYXlsb2FkID0gKHZhbHVlKSA9PiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkgPyB2YWx1ZSA6IFt2YWx1ZV0pO1xuXG52YXIgY3JlYXRlU3ViamVjdCA9ICgpID0+IHtcbiAgICBsZXQgX29ic2VydmVycyA9IFtdO1xuICAgIGNvbnN0IG5leHQgPSAodmFsdWUpID0+IHtcbiAgICAgICAgZm9yIChjb25zdCBvYnNlcnZlciBvZiBfb2JzZXJ2ZXJzKSB7XG4gICAgICAgICAgICBvYnNlcnZlci5uZXh0ICYmIG9ic2VydmVyLm5leHQodmFsdWUpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBzdWJzY3JpYmUgPSAob2JzZXJ2ZXIpID0+IHtcbiAgICAgICAgX29ic2VydmVycy5wdXNoKG9ic2VydmVyKTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIHVuc3Vic2NyaWJlOiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgX29ic2VydmVycyA9IF9vYnNlcnZlcnMuZmlsdGVyKChvKSA9PiBvICE9PSBvYnNlcnZlcik7XG4gICAgICAgICAgICB9LFxuICAgICAgICB9O1xuICAgIH07XG4gICAgY29uc3QgdW5zdWJzY3JpYmUgPSAoKSA9PiB7XG4gICAgICAgIF9vYnNlcnZlcnMgPSBbXTtcbiAgICB9O1xuICAgIHJldHVybiB7XG4gICAgICAgIGdldCBvYnNlcnZlcnMoKSB7XG4gICAgICAgICAgICByZXR1cm4gX29ic2VydmVycztcbiAgICAgICAgfSxcbiAgICAgICAgbmV4dCxcbiAgICAgICAgc3Vic2NyaWJlLFxuICAgICAgICB1bnN1YnNjcmliZSxcbiAgICB9O1xufTtcblxudmFyIGlzUHJpbWl0aXZlID0gKHZhbHVlKSA9PiBpc051bGxPclVuZGVmaW5lZCh2YWx1ZSkgfHwgIWlzT2JqZWN0VHlwZSh2YWx1ZSk7XG5cbmZ1bmN0aW9uIGRlZXBFcXVhbChvYmplY3QxLCBvYmplY3QyKSB7XG4gICAgaWYgKGlzUHJpbWl0aXZlKG9iamVjdDEpIHx8IGlzUHJpbWl0aXZlKG9iamVjdDIpKSB7XG4gICAgICAgIHJldHVybiBvYmplY3QxID09PSBvYmplY3QyO1xuICAgIH1cbiAgICBpZiAoaXNEYXRlT2JqZWN0KG9iamVjdDEpICYmIGlzRGF0ZU9iamVjdChvYmplY3QyKSkge1xuICAgICAgICByZXR1cm4gb2JqZWN0MS5nZXRUaW1lKCkgPT09IG9iamVjdDIuZ2V0VGltZSgpO1xuICAgIH1cbiAgICBjb25zdCBrZXlzMSA9IE9iamVjdC5rZXlzKG9iamVjdDEpO1xuICAgIGNvbnN0IGtleXMyID0gT2JqZWN0LmtleXMob2JqZWN0Mik7XG4gICAgaWYgKGtleXMxLmxlbmd0aCAhPT0ga2V5czIubGVuZ3RoKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgZm9yIChjb25zdCBrZXkgb2Yga2V5czEpIHtcbiAgICAgICAgY29uc3QgdmFsMSA9IG9iamVjdDFba2V5XTtcbiAgICAgICAgaWYgKCFrZXlzMi5pbmNsdWRlcyhrZXkpKSB7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKGtleSAhPT0gJ3JlZicpIHtcbiAgICAgICAgICAgIGNvbnN0IHZhbDIgPSBvYmplY3QyW2tleV07XG4gICAgICAgICAgICBpZiAoKGlzRGF0ZU9iamVjdCh2YWwxKSAmJiBpc0RhdGVPYmplY3QodmFsMikpIHx8XG4gICAgICAgICAgICAgICAgKGlzT2JqZWN0KHZhbDEpICYmIGlzT2JqZWN0KHZhbDIpKSB8fFxuICAgICAgICAgICAgICAgIChBcnJheS5pc0FycmF5KHZhbDEpICYmIEFycmF5LmlzQXJyYXkodmFsMikpXG4gICAgICAgICAgICAgICAgPyAhZGVlcEVxdWFsKHZhbDEsIHZhbDIpXG4gICAgICAgICAgICAgICAgOiB2YWwxICE9PSB2YWwyKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xufVxuXG52YXIgaXNFbXB0eU9iamVjdCA9ICh2YWx1ZSkgPT4gaXNPYmplY3QodmFsdWUpICYmICFPYmplY3Qua2V5cyh2YWx1ZSkubGVuZ3RoO1xuXG52YXIgaXNGaWxlSW5wdXQgPSAoZWxlbWVudCkgPT4gZWxlbWVudC50eXBlID09PSAnZmlsZSc7XG5cbnZhciBpc0Z1bmN0aW9uID0gKHZhbHVlKSA9PiB0eXBlb2YgdmFsdWUgPT09ICdmdW5jdGlvbic7XG5cbnZhciBpc0hUTUxFbGVtZW50ID0gKHZhbHVlKSA9PiB7XG4gICAgaWYgKCFpc1dlYikge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGNvbnN0IG93bmVyID0gdmFsdWUgPyB2YWx1ZS5vd25lckRvY3VtZW50IDogMDtcbiAgICByZXR1cm4gKHZhbHVlIGluc3RhbmNlb2ZcbiAgICAgICAgKG93bmVyICYmIG93bmVyLmRlZmF1bHRWaWV3ID8gb3duZXIuZGVmYXVsdFZpZXcuSFRNTEVsZW1lbnQgOiBIVE1MRWxlbWVudCkpO1xufTtcblxudmFyIGlzTXVsdGlwbGVTZWxlY3QgPSAoZWxlbWVudCkgPT4gZWxlbWVudC50eXBlID09PSBgc2VsZWN0LW11bHRpcGxlYDtcblxudmFyIGlzUmFkaW9JbnB1dCA9IChlbGVtZW50KSA9PiBlbGVtZW50LnR5cGUgPT09ICdyYWRpbyc7XG5cbnZhciBpc1JhZGlvT3JDaGVja2JveCA9IChyZWYpID0+IGlzUmFkaW9JbnB1dChyZWYpIHx8IGlzQ2hlY2tCb3hJbnB1dChyZWYpO1xuXG52YXIgbGl2ZSA9IChyZWYpID0+IGlzSFRNTEVsZW1lbnQocmVmKSAmJiByZWYuaXNDb25uZWN0ZWQ7XG5cbmZ1bmN0aW9uIGJhc2VHZXQob2JqZWN0LCB1cGRhdGVQYXRoKSB7XG4gICAgY29uc3QgbGVuZ3RoID0gdXBkYXRlUGF0aC5zbGljZSgwLCAtMSkubGVuZ3RoO1xuICAgIGxldCBpbmRleCA9IDA7XG4gICAgd2hpbGUgKGluZGV4IDwgbGVuZ3RoKSB7XG4gICAgICAgIG9iamVjdCA9IGlzVW5kZWZpbmVkKG9iamVjdCkgPyBpbmRleCsrIDogb2JqZWN0W3VwZGF0ZVBhdGhbaW5kZXgrK11dO1xuICAgIH1cbiAgICByZXR1cm4gb2JqZWN0O1xufVxuZnVuY3Rpb24gaXNFbXB0eUFycmF5KG9iaikge1xuICAgIGZvciAoY29uc3Qga2V5IGluIG9iaikge1xuICAgICAgICBpZiAob2JqLmhhc093blByb3BlcnR5KGtleSkgJiYgIWlzVW5kZWZpbmVkKG9ialtrZXldKSkge1xuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xufVxuZnVuY3Rpb24gdW5zZXQob2JqZWN0LCBwYXRoKSB7XG4gICAgY29uc3QgcGF0aHMgPSBBcnJheS5pc0FycmF5KHBhdGgpXG4gICAgICAgID8gcGF0aFxuICAgICAgICA6IGlzS2V5KHBhdGgpXG4gICAgICAgICAgICA/IFtwYXRoXVxuICAgICAgICAgICAgOiBzdHJpbmdUb1BhdGgocGF0aCk7XG4gICAgY29uc3QgY2hpbGRPYmplY3QgPSBwYXRocy5sZW5ndGggPT09IDEgPyBvYmplY3QgOiBiYXNlR2V0KG9iamVjdCwgcGF0aHMpO1xuICAgIGNvbnN0IGluZGV4ID0gcGF0aHMubGVuZ3RoIC0gMTtcbiAgICBjb25zdCBrZXkgPSBwYXRoc1tpbmRleF07XG4gICAgaWYgKGNoaWxkT2JqZWN0KSB7XG4gICAgICAgIGRlbGV0ZSBjaGlsZE9iamVjdFtrZXldO1xuICAgIH1cbiAgICBpZiAoaW5kZXggIT09IDAgJiZcbiAgICAgICAgKChpc09iamVjdChjaGlsZE9iamVjdCkgJiYgaXNFbXB0eU9iamVjdChjaGlsZE9iamVjdCkpIHx8XG4gICAgICAgICAgICAoQXJyYXkuaXNBcnJheShjaGlsZE9iamVjdCkgJiYgaXNFbXB0eUFycmF5KGNoaWxkT2JqZWN0KSkpKSB7XG4gICAgICAgIHVuc2V0KG9iamVjdCwgcGF0aHMuc2xpY2UoMCwgLTEpKTtcbiAgICB9XG4gICAgcmV0dXJuIG9iamVjdDtcbn1cblxudmFyIG9iamVjdEhhc0Z1bmN0aW9uID0gKGRhdGEpID0+IHtcbiAgICBmb3IgKGNvbnN0IGtleSBpbiBkYXRhKSB7XG4gICAgICAgIGlmIChpc0Z1bmN0aW9uKGRhdGFba2V5XSkpIHtcbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbn07XG5cbmZ1bmN0aW9uIG1hcmtGaWVsZHNEaXJ0eShkYXRhLCBmaWVsZHMgPSB7fSkge1xuICAgIGNvbnN0IGlzUGFyZW50Tm9kZUFycmF5ID0gQXJyYXkuaXNBcnJheShkYXRhKTtcbiAgICBpZiAoaXNPYmplY3QoZGF0YSkgfHwgaXNQYXJlbnROb2RlQXJyYXkpIHtcbiAgICAgICAgZm9yIChjb25zdCBrZXkgaW4gZGF0YSkge1xuICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZGF0YVtrZXldKSB8fFxuICAgICAgICAgICAgICAgIChpc09iamVjdChkYXRhW2tleV0pICYmICFvYmplY3RIYXNGdW5jdGlvbihkYXRhW2tleV0pKSkge1xuICAgICAgICAgICAgICAgIGZpZWxkc1trZXldID0gQXJyYXkuaXNBcnJheShkYXRhW2tleV0pID8gW10gOiB7fTtcbiAgICAgICAgICAgICAgICBtYXJrRmllbGRzRGlydHkoZGF0YVtrZXldLCBmaWVsZHNba2V5XSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmICghaXNOdWxsT3JVbmRlZmluZWQoZGF0YVtrZXldKSkge1xuICAgICAgICAgICAgICAgIGZpZWxkc1trZXldID0gdHJ1ZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gZmllbGRzO1xufVxuZnVuY3Rpb24gZ2V0RGlydHlGaWVsZHNGcm9tRGVmYXVsdFZhbHVlcyhkYXRhLCBmb3JtVmFsdWVzLCBkaXJ0eUZpZWxkc0Zyb21WYWx1ZXMpIHtcbiAgICBjb25zdCBpc1BhcmVudE5vZGVBcnJheSA9IEFycmF5LmlzQXJyYXkoZGF0YSk7XG4gICAgaWYgKGlzT2JqZWN0KGRhdGEpIHx8IGlzUGFyZW50Tm9kZUFycmF5KSB7XG4gICAgICAgIGZvciAoY29uc3Qga2V5IGluIGRhdGEpIHtcbiAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGRhdGFba2V5XSkgfHxcbiAgICAgICAgICAgICAgICAoaXNPYmplY3QoZGF0YVtrZXldKSAmJiAhb2JqZWN0SGFzRnVuY3Rpb24oZGF0YVtrZXldKSkpIHtcbiAgICAgICAgICAgICAgICBpZiAoaXNVbmRlZmluZWQoZm9ybVZhbHVlcykgfHxcbiAgICAgICAgICAgICAgICAgICAgaXNQcmltaXRpdmUoZGlydHlGaWVsZHNGcm9tVmFsdWVzW2tleV0pKSB7XG4gICAgICAgICAgICAgICAgICAgIGRpcnR5RmllbGRzRnJvbVZhbHVlc1trZXldID0gQXJyYXkuaXNBcnJheShkYXRhW2tleV0pXG4gICAgICAgICAgICAgICAgICAgICAgICA/IG1hcmtGaWVsZHNEaXJ0eShkYXRhW2tleV0sIFtdKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiB7IC4uLm1hcmtGaWVsZHNEaXJ0eShkYXRhW2tleV0pIH07XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBnZXREaXJ0eUZpZWxkc0Zyb21EZWZhdWx0VmFsdWVzKGRhdGFba2V5XSwgaXNOdWxsT3JVbmRlZmluZWQoZm9ybVZhbHVlcykgPyB7fSA6IGZvcm1WYWx1ZXNba2V5XSwgZGlydHlGaWVsZHNGcm9tVmFsdWVzW2tleV0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgIGRpcnR5RmllbGRzRnJvbVZhbHVlc1trZXldID0gIWRlZXBFcXVhbChkYXRhW2tleV0sIGZvcm1WYWx1ZXNba2V5XSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGRpcnR5RmllbGRzRnJvbVZhbHVlcztcbn1cbnZhciBnZXREaXJ0eUZpZWxkcyA9IChkZWZhdWx0VmFsdWVzLCBmb3JtVmFsdWVzKSA9PiBnZXREaXJ0eUZpZWxkc0Zyb21EZWZhdWx0VmFsdWVzKGRlZmF1bHRWYWx1ZXMsIGZvcm1WYWx1ZXMsIG1hcmtGaWVsZHNEaXJ0eShmb3JtVmFsdWVzKSk7XG5cbmNvbnN0IGRlZmF1bHRSZXN1bHQgPSB7XG4gICAgdmFsdWU6IGZhbHNlLFxuICAgIGlzVmFsaWQ6IGZhbHNlLFxufTtcbmNvbnN0IHZhbGlkUmVzdWx0ID0geyB2YWx1ZTogdHJ1ZSwgaXNWYWxpZDogdHJ1ZSB9O1xudmFyIGdldENoZWNrYm94VmFsdWUgPSAob3B0aW9ucykgPT4ge1xuICAgIGlmIChBcnJheS5pc0FycmF5KG9wdGlvbnMpKSB7XG4gICAgICAgIGlmIChvcHRpb25zLmxlbmd0aCA+IDEpIHtcbiAgICAgICAgICAgIGNvbnN0IHZhbHVlcyA9IG9wdGlvbnNcbiAgICAgICAgICAgICAgICAuZmlsdGVyKChvcHRpb24pID0+IG9wdGlvbiAmJiBvcHRpb24uY2hlY2tlZCAmJiAhb3B0aW9uLmRpc2FibGVkKVxuICAgICAgICAgICAgICAgIC5tYXAoKG9wdGlvbikgPT4gb3B0aW9uLnZhbHVlKTtcbiAgICAgICAgICAgIHJldHVybiB7IHZhbHVlOiB2YWx1ZXMsIGlzVmFsaWQ6ICEhdmFsdWVzLmxlbmd0aCB9O1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBvcHRpb25zWzBdLmNoZWNrZWQgJiYgIW9wdGlvbnNbMF0uZGlzYWJsZWRcbiAgICAgICAgICAgID8gLy8gQHRzLWV4cGVjdC1lcnJvciBleHBlY3RlZCB0byB3b3JrIGluIHRoZSBicm93c2VyXG4gICAgICAgICAgICAgICAgb3B0aW9uc1swXS5hdHRyaWJ1dGVzICYmICFpc1VuZGVmaW5lZChvcHRpb25zWzBdLmF0dHJpYnV0ZXMudmFsdWUpXG4gICAgICAgICAgICAgICAgICAgID8gaXNVbmRlZmluZWQob3B0aW9uc1swXS52YWx1ZSkgfHwgb3B0aW9uc1swXS52YWx1ZSA9PT0gJydcbiAgICAgICAgICAgICAgICAgICAgICAgID8gdmFsaWRSZXN1bHRcbiAgICAgICAgICAgICAgICAgICAgICAgIDogeyB2YWx1ZTogb3B0aW9uc1swXS52YWx1ZSwgaXNWYWxpZDogdHJ1ZSB9XG4gICAgICAgICAgICAgICAgICAgIDogdmFsaWRSZXN1bHRcbiAgICAgICAgICAgIDogZGVmYXVsdFJlc3VsdDtcbiAgICB9XG4gICAgcmV0dXJuIGRlZmF1bHRSZXN1bHQ7XG59O1xuXG52YXIgZ2V0RmllbGRWYWx1ZUFzID0gKHZhbHVlLCB7IHZhbHVlQXNOdW1iZXIsIHZhbHVlQXNEYXRlLCBzZXRWYWx1ZUFzIH0pID0+IGlzVW5kZWZpbmVkKHZhbHVlKVxuICAgID8gdmFsdWVcbiAgICA6IHZhbHVlQXNOdW1iZXJcbiAgICAgICAgPyB2YWx1ZSA9PT0gJydcbiAgICAgICAgICAgID8gTmFOXG4gICAgICAgICAgICA6IHZhbHVlXG4gICAgICAgICAgICAgICAgPyArdmFsdWVcbiAgICAgICAgICAgICAgICA6IHZhbHVlXG4gICAgICAgIDogdmFsdWVBc0RhdGUgJiYgaXNTdHJpbmcodmFsdWUpXG4gICAgICAgICAgICA/IG5ldyBEYXRlKHZhbHVlKVxuICAgICAgICAgICAgOiBzZXRWYWx1ZUFzXG4gICAgICAgICAgICAgICAgPyBzZXRWYWx1ZUFzKHZhbHVlKVxuICAgICAgICAgICAgICAgIDogdmFsdWU7XG5cbmNvbnN0IGRlZmF1bHRSZXR1cm4gPSB7XG4gICAgaXNWYWxpZDogZmFsc2UsXG4gICAgdmFsdWU6IG51bGwsXG59O1xudmFyIGdldFJhZGlvVmFsdWUgPSAob3B0aW9ucykgPT4gQXJyYXkuaXNBcnJheShvcHRpb25zKVxuICAgID8gb3B0aW9ucy5yZWR1Y2UoKHByZXZpb3VzLCBvcHRpb24pID0+IG9wdGlvbiAmJiBvcHRpb24uY2hlY2tlZCAmJiAhb3B0aW9uLmRpc2FibGVkXG4gICAgICAgID8ge1xuICAgICAgICAgICAgaXNWYWxpZDogdHJ1ZSxcbiAgICAgICAgICAgIHZhbHVlOiBvcHRpb24udmFsdWUsXG4gICAgICAgIH1cbiAgICAgICAgOiBwcmV2aW91cywgZGVmYXVsdFJldHVybilcbiAgICA6IGRlZmF1bHRSZXR1cm47XG5cbmZ1bmN0aW9uIGdldEZpZWxkVmFsdWUoX2YpIHtcbiAgICBjb25zdCByZWYgPSBfZi5yZWY7XG4gICAgaWYgKGlzRmlsZUlucHV0KHJlZikpIHtcbiAgICAgICAgcmV0dXJuIHJlZi5maWxlcztcbiAgICB9XG4gICAgaWYgKGlzUmFkaW9JbnB1dChyZWYpKSB7XG4gICAgICAgIHJldHVybiBnZXRSYWRpb1ZhbHVlKF9mLnJlZnMpLnZhbHVlO1xuICAgIH1cbiAgICBpZiAoaXNNdWx0aXBsZVNlbGVjdChyZWYpKSB7XG4gICAgICAgIHJldHVybiBbLi4ucmVmLnNlbGVjdGVkT3B0aW9uc10ubWFwKCh7IHZhbHVlIH0pID0+IHZhbHVlKTtcbiAgICB9XG4gICAgaWYgKGlzQ2hlY2tCb3hJbnB1dChyZWYpKSB7XG4gICAgICAgIHJldHVybiBnZXRDaGVja2JveFZhbHVlKF9mLnJlZnMpLnZhbHVlO1xuICAgIH1cbiAgICByZXR1cm4gZ2V0RmllbGRWYWx1ZUFzKGlzVW5kZWZpbmVkKHJlZi52YWx1ZSkgPyBfZi5yZWYudmFsdWUgOiByZWYudmFsdWUsIF9mKTtcbn1cblxudmFyIGdldFJlc29sdmVyT3B0aW9ucyA9IChmaWVsZHNOYW1lcywgX2ZpZWxkcywgY3JpdGVyaWFNb2RlLCBzaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uKSA9PiB7XG4gICAgY29uc3QgZmllbGRzID0ge307XG4gICAgZm9yIChjb25zdCBuYW1lIG9mIGZpZWxkc05hbWVzKSB7XG4gICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KF9maWVsZHMsIG5hbWUpO1xuICAgICAgICBmaWVsZCAmJiBzZXQoZmllbGRzLCBuYW1lLCBmaWVsZC5fZik7XG4gICAgfVxuICAgIHJldHVybiB7XG4gICAgICAgIGNyaXRlcmlhTW9kZSxcbiAgICAgICAgbmFtZXM6IFsuLi5maWVsZHNOYW1lc10sXG4gICAgICAgIGZpZWxkcyxcbiAgICAgICAgc2hvdWxkVXNlTmF0aXZlVmFsaWRhdGlvbixcbiAgICB9O1xufTtcblxudmFyIGlzUmVnZXggPSAodmFsdWUpID0+IHZhbHVlIGluc3RhbmNlb2YgUmVnRXhwO1xuXG52YXIgZ2V0UnVsZVZhbHVlID0gKHJ1bGUpID0+IGlzVW5kZWZpbmVkKHJ1bGUpXG4gICAgPyBydWxlXG4gICAgOiBpc1JlZ2V4KHJ1bGUpXG4gICAgICAgID8gcnVsZS5zb3VyY2VcbiAgICAgICAgOiBpc09iamVjdChydWxlKVxuICAgICAgICAgICAgPyBpc1JlZ2V4KHJ1bGUudmFsdWUpXG4gICAgICAgICAgICAgICAgPyBydWxlLnZhbHVlLnNvdXJjZVxuICAgICAgICAgICAgICAgIDogcnVsZS52YWx1ZVxuICAgICAgICAgICAgOiBydWxlO1xuXG52YXIgZ2V0VmFsaWRhdGlvbk1vZGVzID0gKG1vZGUpID0+ICh7XG4gICAgaXNPblN1Ym1pdDogIW1vZGUgfHwgbW9kZSA9PT0gVkFMSURBVElPTl9NT0RFLm9uU3VibWl0LFxuICAgIGlzT25CbHVyOiBtb2RlID09PSBWQUxJREFUSU9OX01PREUub25CbHVyLFxuICAgIGlzT25DaGFuZ2U6IG1vZGUgPT09IFZBTElEQVRJT05fTU9ERS5vbkNoYW5nZSxcbiAgICBpc09uQWxsOiBtb2RlID09PSBWQUxJREFUSU9OX01PREUuYWxsLFxuICAgIGlzT25Ub3VjaDogbW9kZSA9PT0gVkFMSURBVElPTl9NT0RFLm9uVG91Y2hlZCxcbn0pO1xuXG5jb25zdCBBU1lOQ19GVU5DVElPTiA9ICdBc3luY0Z1bmN0aW9uJztcbnZhciBoYXNQcm9taXNlVmFsaWRhdGlvbiA9IChmaWVsZFJlZmVyZW5jZSkgPT4gISFmaWVsZFJlZmVyZW5jZSAmJlxuICAgICEhZmllbGRSZWZlcmVuY2UudmFsaWRhdGUgJiZcbiAgICAhISgoaXNGdW5jdGlvbihmaWVsZFJlZmVyZW5jZS52YWxpZGF0ZSkgJiZcbiAgICAgICAgZmllbGRSZWZlcmVuY2UudmFsaWRhdGUuY29uc3RydWN0b3IubmFtZSA9PT0gQVNZTkNfRlVOQ1RJT04pIHx8XG4gICAgICAgIChpc09iamVjdChmaWVsZFJlZmVyZW5jZS52YWxpZGF0ZSkgJiZcbiAgICAgICAgICAgIE9iamVjdC52YWx1ZXMoZmllbGRSZWZlcmVuY2UudmFsaWRhdGUpLmZpbmQoKHZhbGlkYXRlRnVuY3Rpb24pID0+IHZhbGlkYXRlRnVuY3Rpb24uY29uc3RydWN0b3IubmFtZSA9PT0gQVNZTkNfRlVOQ1RJT04pKSk7XG5cbnZhciBoYXNWYWxpZGF0aW9uID0gKG9wdGlvbnMpID0+IG9wdGlvbnMubW91bnQgJiZcbiAgICAob3B0aW9ucy5yZXF1aXJlZCB8fFxuICAgICAgICBvcHRpb25zLm1pbiB8fFxuICAgICAgICBvcHRpb25zLm1heCB8fFxuICAgICAgICBvcHRpb25zLm1heExlbmd0aCB8fFxuICAgICAgICBvcHRpb25zLm1pbkxlbmd0aCB8fFxuICAgICAgICBvcHRpb25zLnBhdHRlcm4gfHxcbiAgICAgICAgb3B0aW9ucy52YWxpZGF0ZSk7XG5cbnZhciBpc1dhdGNoZWQgPSAobmFtZSwgX25hbWVzLCBpc0JsdXJFdmVudCkgPT4gIWlzQmx1ckV2ZW50ICYmXG4gICAgKF9uYW1lcy53YXRjaEFsbCB8fFxuICAgICAgICBfbmFtZXMud2F0Y2guaGFzKG5hbWUpIHx8XG4gICAgICAgIFsuLi5fbmFtZXMud2F0Y2hdLnNvbWUoKHdhdGNoTmFtZSkgPT4gbmFtZS5zdGFydHNXaXRoKHdhdGNoTmFtZSkgJiZcbiAgICAgICAgICAgIC9eXFwuXFx3Ky8udGVzdChuYW1lLnNsaWNlKHdhdGNoTmFtZS5sZW5ndGgpKSkpO1xuXG5jb25zdCBpdGVyYXRlRmllbGRzQnlBY3Rpb24gPSAoZmllbGRzLCBhY3Rpb24sIGZpZWxkc05hbWVzLCBhYm9ydEVhcmx5KSA9PiB7XG4gICAgZm9yIChjb25zdCBrZXkgb2YgZmllbGRzTmFtZXMgfHwgT2JqZWN0LmtleXMoZmllbGRzKSkge1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChmaWVsZHMsIGtleSk7XG4gICAgICAgIGlmIChmaWVsZCkge1xuICAgICAgICAgICAgY29uc3QgeyBfZiwgLi4uY3VycmVudEZpZWxkIH0gPSBmaWVsZDtcbiAgICAgICAgICAgIGlmIChfZikge1xuICAgICAgICAgICAgICAgIGlmIChfZi5yZWZzICYmIF9mLnJlZnNbMF0gJiYgYWN0aW9uKF9mLnJlZnNbMF0sIGtleSkgJiYgIWFib3J0RWFybHkpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKF9mLnJlZiAmJiBhY3Rpb24oX2YucmVmLCBfZi5uYW1lKSAmJiAhYWJvcnRFYXJseSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChpdGVyYXRlRmllbGRzQnlBY3Rpb24oY3VycmVudEZpZWxkLCBhY3Rpb24pKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKGlzT2JqZWN0KGN1cnJlbnRGaWVsZCkpIHtcbiAgICAgICAgICAgICAgICBpZiAoaXRlcmF0ZUZpZWxkc0J5QWN0aW9uKGN1cnJlbnRGaWVsZCwgYWN0aW9uKSkge1xuICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuO1xufTtcblxuZnVuY3Rpb24gc2NoZW1hRXJyb3JMb29rdXAoZXJyb3JzLCBfZmllbGRzLCBuYW1lKSB7XG4gICAgY29uc3QgZXJyb3IgPSBnZXQoZXJyb3JzLCBuYW1lKTtcbiAgICBpZiAoZXJyb3IgfHwgaXNLZXkobmFtZSkpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGVycm9yLFxuICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgfTtcbiAgICB9XG4gICAgY29uc3QgbmFtZXMgPSBuYW1lLnNwbGl0KCcuJyk7XG4gICAgd2hpbGUgKG5hbWVzLmxlbmd0aCkge1xuICAgICAgICBjb25zdCBmaWVsZE5hbWUgPSBuYW1lcy5qb2luKCcuJyk7XG4gICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KF9maWVsZHMsIGZpZWxkTmFtZSk7XG4gICAgICAgIGNvbnN0IGZvdW5kRXJyb3IgPSBnZXQoZXJyb3JzLCBmaWVsZE5hbWUpO1xuICAgICAgICBpZiAoZmllbGQgJiYgIUFycmF5LmlzQXJyYXkoZmllbGQpICYmIG5hbWUgIT09IGZpZWxkTmFtZSkge1xuICAgICAgICAgICAgcmV0dXJuIHsgbmFtZSB9O1xuICAgICAgICB9XG4gICAgICAgIGlmIChmb3VuZEVycm9yICYmIGZvdW5kRXJyb3IudHlwZSkge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBuYW1lOiBmaWVsZE5hbWUsXG4gICAgICAgICAgICAgICAgZXJyb3I6IGZvdW5kRXJyb3IsXG4gICAgICAgICAgICB9O1xuICAgICAgICB9XG4gICAgICAgIG5hbWVzLnBvcCgpO1xuICAgIH1cbiAgICByZXR1cm4ge1xuICAgICAgICBuYW1lLFxuICAgIH07XG59XG5cbnZhciBzaG91bGRSZW5kZXJGb3JtU3RhdGUgPSAoZm9ybVN0YXRlRGF0YSwgX3Byb3h5Rm9ybVN0YXRlLCB1cGRhdGVGb3JtU3RhdGUsIGlzUm9vdCkgPT4ge1xuICAgIHVwZGF0ZUZvcm1TdGF0ZShmb3JtU3RhdGVEYXRhKTtcbiAgICBjb25zdCB7IG5hbWUsIC4uLmZvcm1TdGF0ZSB9ID0gZm9ybVN0YXRlRGF0YTtcbiAgICByZXR1cm4gKGlzRW1wdHlPYmplY3QoZm9ybVN0YXRlKSB8fFxuICAgICAgICBPYmplY3Qua2V5cyhmb3JtU3RhdGUpLmxlbmd0aCA+PSBPYmplY3Qua2V5cyhfcHJveHlGb3JtU3RhdGUpLmxlbmd0aCB8fFxuICAgICAgICBPYmplY3Qua2V5cyhmb3JtU3RhdGUpLmZpbmQoKGtleSkgPT4gX3Byb3h5Rm9ybVN0YXRlW2tleV0gPT09XG4gICAgICAgICAgICAoIWlzUm9vdCB8fCBWQUxJREFUSU9OX01PREUuYWxsKSkpO1xufTtcblxudmFyIHNob3VsZFN1YnNjcmliZUJ5TmFtZSA9IChuYW1lLCBzaWduYWxOYW1lLCBleGFjdCkgPT4gIW5hbWUgfHxcbiAgICAhc2lnbmFsTmFtZSB8fFxuICAgIG5hbWUgPT09IHNpZ25hbE5hbWUgfHxcbiAgICBjb252ZXJ0VG9BcnJheVBheWxvYWQobmFtZSkuc29tZSgoY3VycmVudE5hbWUpID0+IGN1cnJlbnROYW1lICYmXG4gICAgICAgIChleGFjdFxuICAgICAgICAgICAgPyBjdXJyZW50TmFtZSA9PT0gc2lnbmFsTmFtZVxuICAgICAgICAgICAgOiBjdXJyZW50TmFtZS5zdGFydHNXaXRoKHNpZ25hbE5hbWUpIHx8XG4gICAgICAgICAgICAgICAgc2lnbmFsTmFtZS5zdGFydHNXaXRoKGN1cnJlbnROYW1lKSkpO1xuXG52YXIgc2tpcFZhbGlkYXRpb24gPSAoaXNCbHVyRXZlbnQsIGlzVG91Y2hlZCwgaXNTdWJtaXR0ZWQsIHJlVmFsaWRhdGVNb2RlLCBtb2RlKSA9PiB7XG4gICAgaWYgKG1vZGUuaXNPbkFsbCkge1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIGVsc2UgaWYgKCFpc1N1Ym1pdHRlZCAmJiBtb2RlLmlzT25Ub3VjaCkge1xuICAgICAgICByZXR1cm4gIShpc1RvdWNoZWQgfHwgaXNCbHVyRXZlbnQpO1xuICAgIH1cbiAgICBlbHNlIGlmIChpc1N1Ym1pdHRlZCA/IHJlVmFsaWRhdGVNb2RlLmlzT25CbHVyIDogbW9kZS5pc09uQmx1cikge1xuICAgICAgICByZXR1cm4gIWlzQmx1ckV2ZW50O1xuICAgIH1cbiAgICBlbHNlIGlmIChpc1N1Ym1pdHRlZCA/IHJlVmFsaWRhdGVNb2RlLmlzT25DaGFuZ2UgOiBtb2RlLmlzT25DaGFuZ2UpIHtcbiAgICAgICAgcmV0dXJuIGlzQmx1ckV2ZW50O1xuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn07XG5cbnZhciB1bnNldEVtcHR5QXJyYXkgPSAocmVmLCBuYW1lKSA9PiAhY29tcGFjdChnZXQocmVmLCBuYW1lKSkubGVuZ3RoICYmIHVuc2V0KHJlZiwgbmFtZSk7XG5cbnZhciB1cGRhdGVGaWVsZEFycmF5Um9vdEVycm9yID0gKGVycm9ycywgZXJyb3IsIG5hbWUpID0+IHtcbiAgICBjb25zdCBmaWVsZEFycmF5RXJyb3JzID0gY29udmVydFRvQXJyYXlQYXlsb2FkKGdldChlcnJvcnMsIG5hbWUpKTtcbiAgICBzZXQoZmllbGRBcnJheUVycm9ycywgJ3Jvb3QnLCBlcnJvcltuYW1lXSk7XG4gICAgc2V0KGVycm9ycywgbmFtZSwgZmllbGRBcnJheUVycm9ycyk7XG4gICAgcmV0dXJuIGVycm9ycztcbn07XG5cbnZhciBpc01lc3NhZ2UgPSAodmFsdWUpID0+IGlzU3RyaW5nKHZhbHVlKTtcblxuZnVuY3Rpb24gZ2V0VmFsaWRhdGVFcnJvcihyZXN1bHQsIHJlZiwgdHlwZSA9ICd2YWxpZGF0ZScpIHtcbiAgICBpZiAoaXNNZXNzYWdlKHJlc3VsdCkgfHxcbiAgICAgICAgKEFycmF5LmlzQXJyYXkocmVzdWx0KSAmJiByZXN1bHQuZXZlcnkoaXNNZXNzYWdlKSkgfHxcbiAgICAgICAgKGlzQm9vbGVhbihyZXN1bHQpICYmICFyZXN1bHQpKSB7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB0eXBlLFxuICAgICAgICAgICAgbWVzc2FnZTogaXNNZXNzYWdlKHJlc3VsdCkgPyByZXN1bHQgOiAnJyxcbiAgICAgICAgICAgIHJlZixcbiAgICAgICAgfTtcbiAgICB9XG59XG5cbnZhciBnZXRWYWx1ZUFuZE1lc3NhZ2UgPSAodmFsaWRhdGlvbkRhdGEpID0+IGlzT2JqZWN0KHZhbGlkYXRpb25EYXRhKSAmJiAhaXNSZWdleCh2YWxpZGF0aW9uRGF0YSlcbiAgICA/IHZhbGlkYXRpb25EYXRhXG4gICAgOiB7XG4gICAgICAgIHZhbHVlOiB2YWxpZGF0aW9uRGF0YSxcbiAgICAgICAgbWVzc2FnZTogJycsXG4gICAgfTtcblxudmFyIHZhbGlkYXRlRmllbGQgPSBhc3luYyAoZmllbGQsIGRpc2FibGVkRmllbGROYW1lcywgZm9ybVZhbHVlcywgdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhLCBzaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uLCBpc0ZpZWxkQXJyYXkpID0+IHtcbiAgICBjb25zdCB7IHJlZiwgcmVmcywgcmVxdWlyZWQsIG1heExlbmd0aCwgbWluTGVuZ3RoLCBtaW4sIG1heCwgcGF0dGVybiwgdmFsaWRhdGUsIG5hbWUsIHZhbHVlQXNOdW1iZXIsIG1vdW50LCB9ID0gZmllbGQuX2Y7XG4gICAgY29uc3QgaW5wdXRWYWx1ZSA9IGdldChmb3JtVmFsdWVzLCBuYW1lKTtcbiAgICBpZiAoIW1vdW50IHx8IGRpc2FibGVkRmllbGROYW1lcy5oYXMobmFtZSkpIHtcbiAgICAgICAgcmV0dXJuIHt9O1xuICAgIH1cbiAgICBjb25zdCBpbnB1dFJlZiA9IHJlZnMgPyByZWZzWzBdIDogcmVmO1xuICAgIGNvbnN0IHNldEN1c3RvbVZhbGlkaXR5ID0gKG1lc3NhZ2UpID0+IHtcbiAgICAgICAgaWYgKHNob3VsZFVzZU5hdGl2ZVZhbGlkYXRpb24gJiYgaW5wdXRSZWYucmVwb3J0VmFsaWRpdHkpIHtcbiAgICAgICAgICAgIGlucHV0UmVmLnNldEN1c3RvbVZhbGlkaXR5KGlzQm9vbGVhbihtZXNzYWdlKSA/ICcnIDogbWVzc2FnZSB8fCAnJyk7XG4gICAgICAgICAgICBpbnB1dFJlZi5yZXBvcnRWYWxpZGl0eSgpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBlcnJvciA9IHt9O1xuICAgIGNvbnN0IGlzUmFkaW8gPSBpc1JhZGlvSW5wdXQocmVmKTtcbiAgICBjb25zdCBpc0NoZWNrQm94ID0gaXNDaGVja0JveElucHV0KHJlZik7XG4gICAgY29uc3QgaXNSYWRpb09yQ2hlY2tib3ggPSBpc1JhZGlvIHx8IGlzQ2hlY2tCb3g7XG4gICAgY29uc3QgaXNFbXB0eSA9ICgodmFsdWVBc051bWJlciB8fCBpc0ZpbGVJbnB1dChyZWYpKSAmJlxuICAgICAgICBpc1VuZGVmaW5lZChyZWYudmFsdWUpICYmXG4gICAgICAgIGlzVW5kZWZpbmVkKGlucHV0VmFsdWUpKSB8fFxuICAgICAgICAoaXNIVE1MRWxlbWVudChyZWYpICYmIHJlZi52YWx1ZSA9PT0gJycpIHx8XG4gICAgICAgIGlucHV0VmFsdWUgPT09ICcnIHx8XG4gICAgICAgIChBcnJheS5pc0FycmF5KGlucHV0VmFsdWUpICYmICFpbnB1dFZhbHVlLmxlbmd0aCk7XG4gICAgY29uc3QgYXBwZW5kRXJyb3JzQ3VycnkgPSBhcHBlbmRFcnJvcnMuYmluZChudWxsLCBuYW1lLCB2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEsIGVycm9yKTtcbiAgICBjb25zdCBnZXRNaW5NYXhNZXNzYWdlID0gKGV4Y2VlZE1heCwgbWF4TGVuZ3RoTWVzc2FnZSwgbWluTGVuZ3RoTWVzc2FnZSwgbWF4VHlwZSA9IElOUFVUX1ZBTElEQVRJT05fUlVMRVMubWF4TGVuZ3RoLCBtaW5UeXBlID0gSU5QVVRfVkFMSURBVElPTl9SVUxFUy5taW5MZW5ndGgpID0+IHtcbiAgICAgICAgY29uc3QgbWVzc2FnZSA9IGV4Y2VlZE1heCA/IG1heExlbmd0aE1lc3NhZ2UgOiBtaW5MZW5ndGhNZXNzYWdlO1xuICAgICAgICBlcnJvcltuYW1lXSA9IHtcbiAgICAgICAgICAgIHR5cGU6IGV4Y2VlZE1heCA/IG1heFR5cGUgOiBtaW5UeXBlLFxuICAgICAgICAgICAgbWVzc2FnZSxcbiAgICAgICAgICAgIHJlZixcbiAgICAgICAgICAgIC4uLmFwcGVuZEVycm9yc0N1cnJ5KGV4Y2VlZE1heCA/IG1heFR5cGUgOiBtaW5UeXBlLCBtZXNzYWdlKSxcbiAgICAgICAgfTtcbiAgICB9O1xuICAgIGlmIChpc0ZpZWxkQXJyYXlcbiAgICAgICAgPyAhQXJyYXkuaXNBcnJheShpbnB1dFZhbHVlKSB8fCAhaW5wdXRWYWx1ZS5sZW5ndGhcbiAgICAgICAgOiByZXF1aXJlZCAmJlxuICAgICAgICAgICAgKCghaXNSYWRpb09yQ2hlY2tib3ggJiYgKGlzRW1wdHkgfHwgaXNOdWxsT3JVbmRlZmluZWQoaW5wdXRWYWx1ZSkpKSB8fFxuICAgICAgICAgICAgICAgIChpc0Jvb2xlYW4oaW5wdXRWYWx1ZSkgJiYgIWlucHV0VmFsdWUpIHx8XG4gICAgICAgICAgICAgICAgKGlzQ2hlY2tCb3ggJiYgIWdldENoZWNrYm94VmFsdWUocmVmcykuaXNWYWxpZCkgfHxcbiAgICAgICAgICAgICAgICAoaXNSYWRpbyAmJiAhZ2V0UmFkaW9WYWx1ZShyZWZzKS5pc1ZhbGlkKSkpIHtcbiAgICAgICAgY29uc3QgeyB2YWx1ZSwgbWVzc2FnZSB9ID0gaXNNZXNzYWdlKHJlcXVpcmVkKVxuICAgICAgICAgICAgPyB7IHZhbHVlOiAhIXJlcXVpcmVkLCBtZXNzYWdlOiByZXF1aXJlZCB9XG4gICAgICAgICAgICA6IGdldFZhbHVlQW5kTWVzc2FnZShyZXF1aXJlZCk7XG4gICAgICAgIGlmICh2YWx1ZSkge1xuICAgICAgICAgICAgZXJyb3JbbmFtZV0gPSB7XG4gICAgICAgICAgICAgICAgdHlwZTogSU5QVVRfVkFMSURBVElPTl9SVUxFUy5yZXF1aXJlZCxcbiAgICAgICAgICAgICAgICBtZXNzYWdlLFxuICAgICAgICAgICAgICAgIHJlZjogaW5wdXRSZWYsXG4gICAgICAgICAgICAgICAgLi4uYXBwZW5kRXJyb3JzQ3VycnkoSU5QVVRfVkFMSURBVElPTl9SVUxFUy5yZXF1aXJlZCwgbWVzc2FnZSksXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgaWYgKCF2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEpIHtcbiAgICAgICAgICAgICAgICBzZXRDdXN0b21WYWxpZGl0eShtZXNzYWdlKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gZXJyb3I7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgaWYgKCFpc0VtcHR5ICYmICghaXNOdWxsT3JVbmRlZmluZWQobWluKSB8fCAhaXNOdWxsT3JVbmRlZmluZWQobWF4KSkpIHtcbiAgICAgICAgbGV0IGV4Y2VlZE1heDtcbiAgICAgICAgbGV0IGV4Y2VlZE1pbjtcbiAgICAgICAgY29uc3QgbWF4T3V0cHV0ID0gZ2V0VmFsdWVBbmRNZXNzYWdlKG1heCk7XG4gICAgICAgIGNvbnN0IG1pbk91dHB1dCA9IGdldFZhbHVlQW5kTWVzc2FnZShtaW4pO1xuICAgICAgICBpZiAoIWlzTnVsbE9yVW5kZWZpbmVkKGlucHV0VmFsdWUpICYmICFpc05hTihpbnB1dFZhbHVlKSkge1xuICAgICAgICAgICAgY29uc3QgdmFsdWVOdW1iZXIgPSByZWYudmFsdWVBc051bWJlciB8fFxuICAgICAgICAgICAgICAgIChpbnB1dFZhbHVlID8gK2lucHV0VmFsdWUgOiBpbnB1dFZhbHVlKTtcbiAgICAgICAgICAgIGlmICghaXNOdWxsT3JVbmRlZmluZWQobWF4T3V0cHV0LnZhbHVlKSkge1xuICAgICAgICAgICAgICAgIGV4Y2VlZE1heCA9IHZhbHVlTnVtYmVyID4gbWF4T3V0cHV0LnZhbHVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFpc051bGxPclVuZGVmaW5lZChtaW5PdXRwdXQudmFsdWUpKSB7XG4gICAgICAgICAgICAgICAgZXhjZWVkTWluID0gdmFsdWVOdW1iZXIgPCBtaW5PdXRwdXQudmFsdWU7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICBjb25zdCB2YWx1ZURhdGUgPSByZWYudmFsdWVBc0RhdGUgfHwgbmV3IERhdGUoaW5wdXRWYWx1ZSk7XG4gICAgICAgICAgICBjb25zdCBjb252ZXJ0VGltZVRvRGF0ZSA9ICh0aW1lKSA9PiBuZXcgRGF0ZShuZXcgRGF0ZSgpLnRvRGF0ZVN0cmluZygpICsgJyAnICsgdGltZSk7XG4gICAgICAgICAgICBjb25zdCBpc1RpbWUgPSByZWYudHlwZSA9PSAndGltZSc7XG4gICAgICAgICAgICBjb25zdCBpc1dlZWsgPSByZWYudHlwZSA9PSAnd2Vlayc7XG4gICAgICAgICAgICBpZiAoaXNTdHJpbmcobWF4T3V0cHV0LnZhbHVlKSAmJiBpbnB1dFZhbHVlKSB7XG4gICAgICAgICAgICAgICAgZXhjZWVkTWF4ID0gaXNUaW1lXG4gICAgICAgICAgICAgICAgICAgID8gY29udmVydFRpbWVUb0RhdGUoaW5wdXRWYWx1ZSkgPiBjb252ZXJ0VGltZVRvRGF0ZShtYXhPdXRwdXQudmFsdWUpXG4gICAgICAgICAgICAgICAgICAgIDogaXNXZWVrXG4gICAgICAgICAgICAgICAgICAgICAgICA/IGlucHV0VmFsdWUgPiBtYXhPdXRwdXQudmFsdWVcbiAgICAgICAgICAgICAgICAgICAgICAgIDogdmFsdWVEYXRlID4gbmV3IERhdGUobWF4T3V0cHV0LnZhbHVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChpc1N0cmluZyhtaW5PdXRwdXQudmFsdWUpICYmIGlucHV0VmFsdWUpIHtcbiAgICAgICAgICAgICAgICBleGNlZWRNaW4gPSBpc1RpbWVcbiAgICAgICAgICAgICAgICAgICAgPyBjb252ZXJ0VGltZVRvRGF0ZShpbnB1dFZhbHVlKSA8IGNvbnZlcnRUaW1lVG9EYXRlKG1pbk91dHB1dC52YWx1ZSlcbiAgICAgICAgICAgICAgICAgICAgOiBpc1dlZWtcbiAgICAgICAgICAgICAgICAgICAgICAgID8gaW5wdXRWYWx1ZSA8IG1pbk91dHB1dC52YWx1ZVxuICAgICAgICAgICAgICAgICAgICAgICAgOiB2YWx1ZURhdGUgPCBuZXcgRGF0ZShtaW5PdXRwdXQudmFsdWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGlmIChleGNlZWRNYXggfHwgZXhjZWVkTWluKSB7XG4gICAgICAgICAgICBnZXRNaW5NYXhNZXNzYWdlKCEhZXhjZWVkTWF4LCBtYXhPdXRwdXQubWVzc2FnZSwgbWluT3V0cHV0Lm1lc3NhZ2UsIElOUFVUX1ZBTElEQVRJT05fUlVMRVMubWF4LCBJTlBVVF9WQUxJREFUSU9OX1JVTEVTLm1pbik7XG4gICAgICAgICAgICBpZiAoIXZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xuICAgICAgICAgICAgICAgIHNldEN1c3RvbVZhbGlkaXR5KGVycm9yW25hbWVdLm1lc3NhZ2UpO1xuICAgICAgICAgICAgICAgIHJldHVybiBlcnJvcjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBpZiAoKG1heExlbmd0aCB8fCBtaW5MZW5ndGgpICYmXG4gICAgICAgICFpc0VtcHR5ICYmXG4gICAgICAgIChpc1N0cmluZyhpbnB1dFZhbHVlKSB8fCAoaXNGaWVsZEFycmF5ICYmIEFycmF5LmlzQXJyYXkoaW5wdXRWYWx1ZSkpKSkge1xuICAgICAgICBjb25zdCBtYXhMZW5ndGhPdXRwdXQgPSBnZXRWYWx1ZUFuZE1lc3NhZ2UobWF4TGVuZ3RoKTtcbiAgICAgICAgY29uc3QgbWluTGVuZ3RoT3V0cHV0ID0gZ2V0VmFsdWVBbmRNZXNzYWdlKG1pbkxlbmd0aCk7XG4gICAgICAgIGNvbnN0IGV4Y2VlZE1heCA9ICFpc051bGxPclVuZGVmaW5lZChtYXhMZW5ndGhPdXRwdXQudmFsdWUpICYmXG4gICAgICAgICAgICBpbnB1dFZhbHVlLmxlbmd0aCA+ICttYXhMZW5ndGhPdXRwdXQudmFsdWU7XG4gICAgICAgIGNvbnN0IGV4Y2VlZE1pbiA9ICFpc051bGxPclVuZGVmaW5lZChtaW5MZW5ndGhPdXRwdXQudmFsdWUpICYmXG4gICAgICAgICAgICBpbnB1dFZhbHVlLmxlbmd0aCA8ICttaW5MZW5ndGhPdXRwdXQudmFsdWU7XG4gICAgICAgIGlmIChleGNlZWRNYXggfHwgZXhjZWVkTWluKSB7XG4gICAgICAgICAgICBnZXRNaW5NYXhNZXNzYWdlKGV4Y2VlZE1heCwgbWF4TGVuZ3RoT3V0cHV0Lm1lc3NhZ2UsIG1pbkxlbmd0aE91dHB1dC5tZXNzYWdlKTtcbiAgICAgICAgICAgIGlmICghdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSB7XG4gICAgICAgICAgICAgICAgc2V0Q3VzdG9tVmFsaWRpdHkoZXJyb3JbbmFtZV0ubWVzc2FnZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGVycm9yO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGlmIChwYXR0ZXJuICYmICFpc0VtcHR5ICYmIGlzU3RyaW5nKGlucHV0VmFsdWUpKSB7XG4gICAgICAgIGNvbnN0IHsgdmFsdWU6IHBhdHRlcm5WYWx1ZSwgbWVzc2FnZSB9ID0gZ2V0VmFsdWVBbmRNZXNzYWdlKHBhdHRlcm4pO1xuICAgICAgICBpZiAoaXNSZWdleChwYXR0ZXJuVmFsdWUpICYmICFpbnB1dFZhbHVlLm1hdGNoKHBhdHRlcm5WYWx1ZSkpIHtcbiAgICAgICAgICAgIGVycm9yW25hbWVdID0ge1xuICAgICAgICAgICAgICAgIHR5cGU6IElOUFVUX1ZBTElEQVRJT05fUlVMRVMucGF0dGVybixcbiAgICAgICAgICAgICAgICBtZXNzYWdlLFxuICAgICAgICAgICAgICAgIHJlZixcbiAgICAgICAgICAgICAgICAuLi5hcHBlbmRFcnJvcnNDdXJyeShJTlBVVF9WQUxJREFUSU9OX1JVTEVTLnBhdHRlcm4sIG1lc3NhZ2UpLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGlmICghdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSB7XG4gICAgICAgICAgICAgICAgc2V0Q3VzdG9tVmFsaWRpdHkobWVzc2FnZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGVycm9yO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIGlmICh2YWxpZGF0ZSkge1xuICAgICAgICBpZiAoaXNGdW5jdGlvbih2YWxpZGF0ZSkpIHtcbiAgICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHZhbGlkYXRlKGlucHV0VmFsdWUsIGZvcm1WYWx1ZXMpO1xuICAgICAgICAgICAgY29uc3QgdmFsaWRhdGVFcnJvciA9IGdldFZhbGlkYXRlRXJyb3IocmVzdWx0LCBpbnB1dFJlZik7XG4gICAgICAgICAgICBpZiAodmFsaWRhdGVFcnJvcikge1xuICAgICAgICAgICAgICAgIGVycm9yW25hbWVdID0ge1xuICAgICAgICAgICAgICAgICAgICAuLi52YWxpZGF0ZUVycm9yLFxuICAgICAgICAgICAgICAgICAgICAuLi5hcHBlbmRFcnJvcnNDdXJyeShJTlBVVF9WQUxJREFUSU9OX1JVTEVTLnZhbGlkYXRlLCB2YWxpZGF0ZUVycm9yLm1lc3NhZ2UpLFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICAgICAgaWYgKCF2YWxpZGF0ZUFsbEZpZWxkQ3JpdGVyaWEpIHtcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3VzdG9tVmFsaWRpdHkodmFsaWRhdGVFcnJvci5tZXNzYWdlKTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGVycm9yO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChpc09iamVjdCh2YWxpZGF0ZSkpIHtcbiAgICAgICAgICAgIGxldCB2YWxpZGF0aW9uUmVzdWx0ID0ge307XG4gICAgICAgICAgICBmb3IgKGNvbnN0IGtleSBpbiB2YWxpZGF0ZSkge1xuICAgICAgICAgICAgICAgIGlmICghaXNFbXB0eU9iamVjdCh2YWxpZGF0aW9uUmVzdWx0KSAmJiAhdmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhKSB7XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCB2YWxpZGF0ZUVycm9yID0gZ2V0VmFsaWRhdGVFcnJvcihhd2FpdCB2YWxpZGF0ZVtrZXldKGlucHV0VmFsdWUsIGZvcm1WYWx1ZXMpLCBpbnB1dFJlZiwga2V5KTtcbiAgICAgICAgICAgICAgICBpZiAodmFsaWRhdGVFcnJvcikge1xuICAgICAgICAgICAgICAgICAgICB2YWxpZGF0aW9uUmVzdWx0ID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgLi4udmFsaWRhdGVFcnJvcixcbiAgICAgICAgICAgICAgICAgICAgICAgIC4uLmFwcGVuZEVycm9yc0N1cnJ5KGtleSwgdmFsaWRhdGVFcnJvci5tZXNzYWdlKSxcbiAgICAgICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICAgICAgc2V0Q3VzdG9tVmFsaWRpdHkodmFsaWRhdGVFcnJvci5tZXNzYWdlKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgZXJyb3JbbmFtZV0gPSB2YWxpZGF0aW9uUmVzdWx0O1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFpc0VtcHR5T2JqZWN0KHZhbGlkYXRpb25SZXN1bHQpKSB7XG4gICAgICAgICAgICAgICAgZXJyb3JbbmFtZV0gPSB7XG4gICAgICAgICAgICAgICAgICAgIHJlZjogaW5wdXRSZWYsXG4gICAgICAgICAgICAgICAgICAgIC4uLnZhbGlkYXRpb25SZXN1bHQsXG4gICAgICAgICAgICAgICAgfTtcbiAgICAgICAgICAgICAgICBpZiAoIXZhbGlkYXRlQWxsRmllbGRDcml0ZXJpYSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm4gZXJyb3I7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHNldEN1c3RvbVZhbGlkaXR5KHRydWUpO1xuICAgIHJldHVybiBlcnJvcjtcbn07XG5cbmNvbnN0IGRlZmF1bHRPcHRpb25zID0ge1xuICAgIG1vZGU6IFZBTElEQVRJT05fTU9ERS5vblN1Ym1pdCxcbiAgICByZVZhbGlkYXRlTW9kZTogVkFMSURBVElPTl9NT0RFLm9uQ2hhbmdlLFxuICAgIHNob3VsZEZvY3VzRXJyb3I6IHRydWUsXG59O1xuZnVuY3Rpb24gY3JlYXRlRm9ybUNvbnRyb2wocHJvcHMgPSB7fSkge1xuICAgIGxldCBfb3B0aW9ucyA9IHtcbiAgICAgICAgLi4uZGVmYXVsdE9wdGlvbnMsXG4gICAgICAgIC4uLnByb3BzLFxuICAgIH07XG4gICAgbGV0IF9mb3JtU3RhdGUgPSB7XG4gICAgICAgIHN1Ym1pdENvdW50OiAwLFxuICAgICAgICBpc0RpcnR5OiBmYWxzZSxcbiAgICAgICAgaXNSZWFkeTogZmFsc2UsXG4gICAgICAgIGlzTG9hZGluZzogaXNGdW5jdGlvbihfb3B0aW9ucy5kZWZhdWx0VmFsdWVzKSxcbiAgICAgICAgaXNWYWxpZGF0aW5nOiBmYWxzZSxcbiAgICAgICAgaXNTdWJtaXR0ZWQ6IGZhbHNlLFxuICAgICAgICBpc1N1Ym1pdHRpbmc6IGZhbHNlLFxuICAgICAgICBpc1N1Ym1pdFN1Y2Nlc3NmdWw6IGZhbHNlLFxuICAgICAgICBpc1ZhbGlkOiBmYWxzZSxcbiAgICAgICAgdG91Y2hlZEZpZWxkczoge30sXG4gICAgICAgIGRpcnR5RmllbGRzOiB7fSxcbiAgICAgICAgdmFsaWRhdGluZ0ZpZWxkczoge30sXG4gICAgICAgIGVycm9yczogX29wdGlvbnMuZXJyb3JzIHx8IHt9LFxuICAgICAgICBkaXNhYmxlZDogX29wdGlvbnMuZGlzYWJsZWQgfHwgZmFsc2UsXG4gICAgfTtcbiAgICBjb25zdCBfZmllbGRzID0ge307XG4gICAgbGV0IF9kZWZhdWx0VmFsdWVzID0gaXNPYmplY3QoX29wdGlvbnMuZGVmYXVsdFZhbHVlcykgfHwgaXNPYmplY3QoX29wdGlvbnMudmFsdWVzKVxuICAgICAgICA/IGNsb25lT2JqZWN0KF9vcHRpb25zLmRlZmF1bHRWYWx1ZXMgfHwgX29wdGlvbnMudmFsdWVzKSB8fCB7fVxuICAgICAgICA6IHt9O1xuICAgIGxldCBfZm9ybVZhbHVlcyA9IF9vcHRpb25zLnNob3VsZFVucmVnaXN0ZXJcbiAgICAgICAgPyB7fVxuICAgICAgICA6IGNsb25lT2JqZWN0KF9kZWZhdWx0VmFsdWVzKTtcbiAgICBsZXQgX3N0YXRlID0ge1xuICAgICAgICBhY3Rpb246IGZhbHNlLFxuICAgICAgICBtb3VudDogZmFsc2UsXG4gICAgICAgIHdhdGNoOiBmYWxzZSxcbiAgICB9O1xuICAgIGxldCBfbmFtZXMgPSB7XG4gICAgICAgIG1vdW50OiBuZXcgU2V0KCksXG4gICAgICAgIGRpc2FibGVkOiBuZXcgU2V0KCksXG4gICAgICAgIHVuTW91bnQ6IG5ldyBTZXQoKSxcbiAgICAgICAgYXJyYXk6IG5ldyBTZXQoKSxcbiAgICAgICAgd2F0Y2g6IG5ldyBTZXQoKSxcbiAgICB9O1xuICAgIGxldCBkZWxheUVycm9yQ2FsbGJhY2s7XG4gICAgbGV0IHRpbWVyID0gMDtcbiAgICBjb25zdCBfcHJveHlGb3JtU3RhdGUgPSB7XG4gICAgICAgIGlzRGlydHk6IGZhbHNlLFxuICAgICAgICBkaXJ0eUZpZWxkczogZmFsc2UsXG4gICAgICAgIHZhbGlkYXRpbmdGaWVsZHM6IGZhbHNlLFxuICAgICAgICB0b3VjaGVkRmllbGRzOiBmYWxzZSxcbiAgICAgICAgaXNWYWxpZGF0aW5nOiBmYWxzZSxcbiAgICAgICAgaXNWYWxpZDogZmFsc2UsXG4gICAgICAgIGVycm9yczogZmFsc2UsXG4gICAgfTtcbiAgICBsZXQgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlID0ge1xuICAgICAgICAuLi5fcHJveHlGb3JtU3RhdGUsXG4gICAgfTtcbiAgICBjb25zdCBfc3ViamVjdHMgPSB7XG4gICAgICAgIGFycmF5OiBjcmVhdGVTdWJqZWN0KCksXG4gICAgICAgIHN0YXRlOiBjcmVhdGVTdWJqZWN0KCksXG4gICAgfTtcbiAgICBjb25zdCBzaG91bGREaXNwbGF5QWxsQXNzb2NpYXRlZEVycm9ycyA9IF9vcHRpb25zLmNyaXRlcmlhTW9kZSA9PT0gVkFMSURBVElPTl9NT0RFLmFsbDtcbiAgICBjb25zdCBkZWJvdW5jZSA9IChjYWxsYmFjaykgPT4gKHdhaXQpID0+IHtcbiAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVyKTtcbiAgICAgICAgdGltZXIgPSBzZXRUaW1lb3V0KGNhbGxiYWNrLCB3YWl0KTtcbiAgICB9O1xuICAgIGNvbnN0IF9zZXRWYWxpZCA9IGFzeW5jIChzaG91bGRVcGRhdGVWYWxpZCkgPT4ge1xuICAgICAgICBpZiAoIV9vcHRpb25zLmRpc2FibGVkICYmXG4gICAgICAgICAgICAoX3Byb3h5Rm9ybVN0YXRlLmlzVmFsaWQgfHxcbiAgICAgICAgICAgICAgICBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUuaXNWYWxpZCB8fFxuICAgICAgICAgICAgICAgIHNob3VsZFVwZGF0ZVZhbGlkKSkge1xuICAgICAgICAgICAgY29uc3QgaXNWYWxpZCA9IF9vcHRpb25zLnJlc29sdmVyXG4gICAgICAgICAgICAgICAgPyBpc0VtcHR5T2JqZWN0KChhd2FpdCBfcnVuU2NoZW1hKCkpLmVycm9ycylcbiAgICAgICAgICAgICAgICA6IGF3YWl0IGV4ZWN1dGVCdWlsdEluVmFsaWRhdGlvbihfZmllbGRzLCB0cnVlKTtcbiAgICAgICAgICAgIGlmIChpc1ZhbGlkICE9PSBfZm9ybVN0YXRlLmlzVmFsaWQpIHtcbiAgICAgICAgICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgICAgIGlzVmFsaWQsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IF91cGRhdGVJc1ZhbGlkYXRpbmcgPSAobmFtZXMsIGlzVmFsaWRhdGluZykgPT4ge1xuICAgICAgICBpZiAoIV9vcHRpb25zLmRpc2FibGVkICYmXG4gICAgICAgICAgICAoX3Byb3h5Rm9ybVN0YXRlLmlzVmFsaWRhdGluZyB8fFxuICAgICAgICAgICAgICAgIF9wcm94eUZvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzIHx8XG4gICAgICAgICAgICAgICAgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLmlzVmFsaWRhdGluZyB8fFxuICAgICAgICAgICAgICAgIF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzKSkge1xuICAgICAgICAgICAgKG5hbWVzIHx8IEFycmF5LmZyb20oX25hbWVzLm1vdW50KSkuZm9yRWFjaCgobmFtZSkgPT4ge1xuICAgICAgICAgICAgICAgIGlmIChuYW1lKSB7XG4gICAgICAgICAgICAgICAgICAgIGlzVmFsaWRhdGluZ1xuICAgICAgICAgICAgICAgICAgICAgICAgPyBzZXQoX2Zvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzLCBuYW1lLCBpc1ZhbGlkYXRpbmcpXG4gICAgICAgICAgICAgICAgICAgICAgICA6IHVuc2V0KF9mb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgdmFsaWRhdGluZ0ZpZWxkczogX2Zvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzLFxuICAgICAgICAgICAgICAgIGlzVmFsaWRhdGluZzogIWlzRW1wdHlPYmplY3QoX2Zvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzKSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBfc2V0RmllbGRBcnJheSA9IChuYW1lLCB2YWx1ZXMgPSBbXSwgbWV0aG9kLCBhcmdzLCBzaG91bGRTZXRWYWx1ZXMgPSB0cnVlLCBzaG91bGRVcGRhdGVGaWVsZHNBbmRTdGF0ZSA9IHRydWUpID0+IHtcbiAgICAgICAgaWYgKGFyZ3MgJiYgbWV0aG9kICYmICFfb3B0aW9ucy5kaXNhYmxlZCkge1xuICAgICAgICAgICAgX3N0YXRlLmFjdGlvbiA9IHRydWU7XG4gICAgICAgICAgICBpZiAoc2hvdWxkVXBkYXRlRmllbGRzQW5kU3RhdGUgJiYgQXJyYXkuaXNBcnJheShnZXQoX2ZpZWxkcywgbmFtZSkpKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZmllbGRWYWx1ZXMgPSBtZXRob2QoZ2V0KF9maWVsZHMsIG5hbWUpLCBhcmdzLmFyZ0EsIGFyZ3MuYXJnQik7XG4gICAgICAgICAgICAgICAgc2hvdWxkU2V0VmFsdWVzICYmIHNldChfZmllbGRzLCBuYW1lLCBmaWVsZFZhbHVlcyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoc2hvdWxkVXBkYXRlRmllbGRzQW5kU3RhdGUgJiZcbiAgICAgICAgICAgICAgICBBcnJheS5pc0FycmF5KGdldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSkpKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZXJyb3JzID0gbWV0aG9kKGdldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSksIGFyZ3MuYXJnQSwgYXJncy5hcmdCKTtcbiAgICAgICAgICAgICAgICBzaG91bGRTZXRWYWx1ZXMgJiYgc2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBuYW1lLCBlcnJvcnMpO1xuICAgICAgICAgICAgICAgIHVuc2V0RW1wdHlBcnJheShfZm9ybVN0YXRlLmVycm9ycywgbmFtZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoKF9wcm94eUZvcm1TdGF0ZS50b3VjaGVkRmllbGRzIHx8XG4gICAgICAgICAgICAgICAgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMpICYmXG4gICAgICAgICAgICAgICAgc2hvdWxkVXBkYXRlRmllbGRzQW5kU3RhdGUgJiZcbiAgICAgICAgICAgICAgICBBcnJheS5pc0FycmF5KGdldChfZm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMsIG5hbWUpKSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IHRvdWNoZWRGaWVsZHMgPSBtZXRob2QoZ2V0KF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkcywgbmFtZSksIGFyZ3MuYXJnQSwgYXJncy5hcmdCKTtcbiAgICAgICAgICAgICAgICBzaG91bGRTZXRWYWx1ZXMgJiYgc2V0KF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkcywgbmFtZSwgdG91Y2hlZEZpZWxkcyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoX3Byb3h5Rm9ybVN0YXRlLmRpcnR5RmllbGRzIHx8IF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS5kaXJ0eUZpZWxkcykge1xuICAgICAgICAgICAgICAgIF9mb3JtU3RhdGUuZGlydHlGaWVsZHMgPSBnZXREaXJ0eUZpZWxkcyhfZGVmYXVsdFZhbHVlcywgX2Zvcm1WYWx1ZXMpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgICAgaXNEaXJ0eTogX2dldERpcnR5KG5hbWUsIHZhbHVlcyksXG4gICAgICAgICAgICAgICAgZGlydHlGaWVsZHM6IF9mb3JtU3RhdGUuZGlydHlGaWVsZHMsXG4gICAgICAgICAgICAgICAgZXJyb3JzOiBfZm9ybVN0YXRlLmVycm9ycyxcbiAgICAgICAgICAgICAgICBpc1ZhbGlkOiBfZm9ybVN0YXRlLmlzVmFsaWQsXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHNldChfZm9ybVZhbHVlcywgbmFtZSwgdmFsdWVzKTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgY29uc3QgdXBkYXRlRXJyb3JzID0gKG5hbWUsIGVycm9yKSA9PiB7XG4gICAgICAgIHNldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSwgZXJyb3IpO1xuICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICBlcnJvcnM6IF9mb3JtU3RhdGUuZXJyb3JzLFxuICAgICAgICB9KTtcbiAgICB9O1xuICAgIGNvbnN0IF9zZXRFcnJvcnMgPSAoZXJyb3JzKSA9PiB7XG4gICAgICAgIF9mb3JtU3RhdGUuZXJyb3JzID0gZXJyb3JzO1xuICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICBlcnJvcnM6IF9mb3JtU3RhdGUuZXJyb3JzLFxuICAgICAgICAgICAgaXNWYWxpZDogZmFsc2UsXG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgY29uc3QgdXBkYXRlVmFsaWRBbmRWYWx1ZSA9IChuYW1lLCBzaG91bGRTa2lwU2V0VmFsdWVBcywgdmFsdWUsIHJlZikgPT4ge1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgaWYgKGZpZWxkKSB7XG4gICAgICAgICAgICBjb25zdCBkZWZhdWx0VmFsdWUgPSBnZXQoX2Zvcm1WYWx1ZXMsIG5hbWUsIGlzVW5kZWZpbmVkKHZhbHVlKSA/IGdldChfZGVmYXVsdFZhbHVlcywgbmFtZSkgOiB2YWx1ZSk7XG4gICAgICAgICAgICBpc1VuZGVmaW5lZChkZWZhdWx0VmFsdWUpIHx8XG4gICAgICAgICAgICAgICAgKHJlZiAmJiByZWYuZGVmYXVsdENoZWNrZWQpIHx8XG4gICAgICAgICAgICAgICAgc2hvdWxkU2tpcFNldFZhbHVlQXNcbiAgICAgICAgICAgICAgICA/IHNldChfZm9ybVZhbHVlcywgbmFtZSwgc2hvdWxkU2tpcFNldFZhbHVlQXMgPyBkZWZhdWx0VmFsdWUgOiBnZXRGaWVsZFZhbHVlKGZpZWxkLl9mKSlcbiAgICAgICAgICAgICAgICA6IHNldEZpZWxkVmFsdWUobmFtZSwgZGVmYXVsdFZhbHVlKTtcbiAgICAgICAgICAgIF9zdGF0ZS5tb3VudCAmJiBfc2V0VmFsaWQoKTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgY29uc3QgdXBkYXRlVG91Y2hBbmREaXJ0eSA9IChuYW1lLCBmaWVsZFZhbHVlLCBpc0JsdXJFdmVudCwgc2hvdWxkRGlydHksIHNob3VsZFJlbmRlcikgPT4ge1xuICAgICAgICBsZXQgc2hvdWxkVXBkYXRlRmllbGQgPSBmYWxzZTtcbiAgICAgICAgbGV0IGlzUHJldmlvdXNEaXJ0eSA9IGZhbHNlO1xuICAgICAgICBjb25zdCBvdXRwdXQgPSB7XG4gICAgICAgICAgICBuYW1lLFxuICAgICAgICB9O1xuICAgICAgICBpZiAoIV9vcHRpb25zLmRpc2FibGVkKSB7XG4gICAgICAgICAgICBpZiAoIWlzQmx1ckV2ZW50IHx8IHNob3VsZERpcnR5KSB7XG4gICAgICAgICAgICAgICAgaWYgKF9wcm94eUZvcm1TdGF0ZS5pc0RpcnR5IHx8IF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS5pc0RpcnR5KSB7XG4gICAgICAgICAgICAgICAgICAgIGlzUHJldmlvdXNEaXJ0eSA9IF9mb3JtU3RhdGUuaXNEaXJ0eTtcbiAgICAgICAgICAgICAgICAgICAgX2Zvcm1TdGF0ZS5pc0RpcnR5ID0gb3V0cHV0LmlzRGlydHkgPSBfZ2V0RGlydHkoKTtcbiAgICAgICAgICAgICAgICAgICAgc2hvdWxkVXBkYXRlRmllbGQgPSBpc1ByZXZpb3VzRGlydHkgIT09IG91dHB1dC5pc0RpcnR5O1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjb25zdCBpc0N1cnJlbnRGaWVsZFByaXN0aW5lID0gZGVlcEVxdWFsKGdldChfZGVmYXVsdFZhbHVlcywgbmFtZSksIGZpZWxkVmFsdWUpO1xuICAgICAgICAgICAgICAgIGlzUHJldmlvdXNEaXJ0eSA9ICEhZ2V0KF9mb3JtU3RhdGUuZGlydHlGaWVsZHMsIG5hbWUpO1xuICAgICAgICAgICAgICAgIGlzQ3VycmVudEZpZWxkUHJpc3RpbmVcbiAgICAgICAgICAgICAgICAgICAgPyB1bnNldChfZm9ybVN0YXRlLmRpcnR5RmllbGRzLCBuYW1lKVxuICAgICAgICAgICAgICAgICAgICA6IHNldChfZm9ybVN0YXRlLmRpcnR5RmllbGRzLCBuYW1lLCB0cnVlKTtcbiAgICAgICAgICAgICAgICBvdXRwdXQuZGlydHlGaWVsZHMgPSBfZm9ybVN0YXRlLmRpcnR5RmllbGRzO1xuICAgICAgICAgICAgICAgIHNob3VsZFVwZGF0ZUZpZWxkID1cbiAgICAgICAgICAgICAgICAgICAgc2hvdWxkVXBkYXRlRmllbGQgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICgoX3Byb3h5Rm9ybVN0YXRlLmRpcnR5RmllbGRzIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLmRpcnR5RmllbGRzKSAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzUHJldmlvdXNEaXJ0eSAhPT0gIWlzQ3VycmVudEZpZWxkUHJpc3RpbmUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGlzQmx1ckV2ZW50KSB7XG4gICAgICAgICAgICAgICAgY29uc3QgaXNQcmV2aW91c0ZpZWxkVG91Y2hlZCA9IGdldChfZm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMsIG5hbWUpO1xuICAgICAgICAgICAgICAgIGlmICghaXNQcmV2aW91c0ZpZWxkVG91Y2hlZCkge1xuICAgICAgICAgICAgICAgICAgICBzZXQoX2Zvcm1TdGF0ZS50b3VjaGVkRmllbGRzLCBuYW1lLCBpc0JsdXJFdmVudCk7XG4gICAgICAgICAgICAgICAgICAgIG91dHB1dC50b3VjaGVkRmllbGRzID0gX2Zvcm1TdGF0ZS50b3VjaGVkRmllbGRzO1xuICAgICAgICAgICAgICAgICAgICBzaG91bGRVcGRhdGVGaWVsZCA9XG4gICAgICAgICAgICAgICAgICAgICAgICBzaG91bGRVcGRhdGVGaWVsZCB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICgoX3Byb3h5Rm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMgfHxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLnRvdWNoZWRGaWVsZHMpICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzUHJldmlvdXNGaWVsZFRvdWNoZWQgIT09IGlzQmx1ckV2ZW50KTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBzaG91bGRVcGRhdGVGaWVsZCAmJiBzaG91bGRSZW5kZXIgJiYgX3N1YmplY3RzLnN0YXRlLm5leHQob3V0cHV0KTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gc2hvdWxkVXBkYXRlRmllbGQgPyBvdXRwdXQgOiB7fTtcbiAgICB9O1xuICAgIGNvbnN0IHNob3VsZFJlbmRlckJ5RXJyb3IgPSAobmFtZSwgaXNWYWxpZCwgZXJyb3IsIGZpZWxkU3RhdGUpID0+IHtcbiAgICAgICAgY29uc3QgcHJldmlvdXNGaWVsZEVycm9yID0gZ2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBuYW1lKTtcbiAgICAgICAgY29uc3Qgc2hvdWxkVXBkYXRlVmFsaWQgPSAoX3Byb3h5Rm9ybVN0YXRlLmlzVmFsaWQgfHwgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLmlzVmFsaWQpICYmXG4gICAgICAgICAgICBpc0Jvb2xlYW4oaXNWYWxpZCkgJiZcbiAgICAgICAgICAgIF9mb3JtU3RhdGUuaXNWYWxpZCAhPT0gaXNWYWxpZDtcbiAgICAgICAgaWYgKF9vcHRpb25zLmRlbGF5RXJyb3IgJiYgZXJyb3IpIHtcbiAgICAgICAgICAgIGRlbGF5RXJyb3JDYWxsYmFjayA9IGRlYm91bmNlKCgpID0+IHVwZGF0ZUVycm9ycyhuYW1lLCBlcnJvcikpO1xuICAgICAgICAgICAgZGVsYXlFcnJvckNhbGxiYWNrKF9vcHRpb25zLmRlbGF5RXJyb3IpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgY2xlYXJUaW1lb3V0KHRpbWVyKTtcbiAgICAgICAgICAgIGRlbGF5RXJyb3JDYWxsYmFjayA9IG51bGw7XG4gICAgICAgICAgICBlcnJvclxuICAgICAgICAgICAgICAgID8gc2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBuYW1lLCBlcnJvcilcbiAgICAgICAgICAgICAgICA6IHVuc2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBuYW1lKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoKGVycm9yID8gIWRlZXBFcXVhbChwcmV2aW91c0ZpZWxkRXJyb3IsIGVycm9yKSA6IHByZXZpb3VzRmllbGRFcnJvcikgfHxcbiAgICAgICAgICAgICFpc0VtcHR5T2JqZWN0KGZpZWxkU3RhdGUpIHx8XG4gICAgICAgICAgICBzaG91bGRVcGRhdGVWYWxpZCkge1xuICAgICAgICAgICAgY29uc3QgdXBkYXRlZEZvcm1TdGF0ZSA9IHtcbiAgICAgICAgICAgICAgICAuLi5maWVsZFN0YXRlLFxuICAgICAgICAgICAgICAgIC4uLihzaG91bGRVcGRhdGVWYWxpZCAmJiBpc0Jvb2xlYW4oaXNWYWxpZCkgPyB7IGlzVmFsaWQgfSA6IHt9KSxcbiAgICAgICAgICAgICAgICBlcnJvcnM6IF9mb3JtU3RhdGUuZXJyb3JzLFxuICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgX2Zvcm1TdGF0ZSA9IHtcbiAgICAgICAgICAgICAgICAuLi5fZm9ybVN0YXRlLFxuICAgICAgICAgICAgICAgIC4uLnVwZGF0ZWRGb3JtU3RhdGUsXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQodXBkYXRlZEZvcm1TdGF0ZSk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IF9ydW5TY2hlbWEgPSBhc3luYyAobmFtZSkgPT4ge1xuICAgICAgICBfdXBkYXRlSXNWYWxpZGF0aW5nKG5hbWUsIHRydWUpO1xuICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBfb3B0aW9ucy5yZXNvbHZlcihfZm9ybVZhbHVlcywgX29wdGlvbnMuY29udGV4dCwgZ2V0UmVzb2x2ZXJPcHRpb25zKG5hbWUgfHwgX25hbWVzLm1vdW50LCBfZmllbGRzLCBfb3B0aW9ucy5jcml0ZXJpYU1vZGUsIF9vcHRpb25zLnNob3VsZFVzZU5hdGl2ZVZhbGlkYXRpb24pKTtcbiAgICAgICAgX3VwZGF0ZUlzVmFsaWRhdGluZyhuYW1lKTtcbiAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9O1xuICAgIGNvbnN0IGV4ZWN1dGVTY2hlbWFBbmRVcGRhdGVTdGF0ZSA9IGFzeW5jIChuYW1lcykgPT4ge1xuICAgICAgICBjb25zdCB7IGVycm9ycyB9ID0gYXdhaXQgX3J1blNjaGVtYShuYW1lcyk7XG4gICAgICAgIGlmIChuYW1lcykge1xuICAgICAgICAgICAgZm9yIChjb25zdCBuYW1lIG9mIG5hbWVzKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZXJyb3IgPSBnZXQoZXJyb3JzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICBlcnJvclxuICAgICAgICAgICAgICAgICAgICA/IHNldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSwgZXJyb3IpXG4gICAgICAgICAgICAgICAgICAgIDogdW5zZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgX2Zvcm1TdGF0ZS5lcnJvcnMgPSBlcnJvcnM7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGVycm9ycztcbiAgICB9O1xuICAgIGNvbnN0IGV4ZWN1dGVCdWlsdEluVmFsaWRhdGlvbiA9IGFzeW5jIChmaWVsZHMsIHNob3VsZE9ubHlDaGVja1ZhbGlkLCBjb250ZXh0ID0ge1xuICAgICAgICB2YWxpZDogdHJ1ZSxcbiAgICB9KSA9PiB7XG4gICAgICAgIGZvciAoY29uc3QgbmFtZSBpbiBmaWVsZHMpIHtcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZmllbGRzW25hbWVdO1xuICAgICAgICAgICAgaWYgKGZpZWxkKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgeyBfZiwgLi4uZmllbGRWYWx1ZSB9ID0gZmllbGQ7XG4gICAgICAgICAgICAgICAgaWYgKF9mKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGlzRmllbGRBcnJheVJvb3QgPSBfbmFtZXMuYXJyYXkuaGFzKF9mLm5hbWUpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBpc1Byb21pc2VGdW5jdGlvbiA9IGZpZWxkLl9mICYmIGhhc1Byb21pc2VWYWxpZGF0aW9uKGZpZWxkLl9mKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGlzUHJvbWlzZUZ1bmN0aW9uICYmIF9wcm94eUZvcm1TdGF0ZS52YWxpZGF0aW5nRmllbGRzKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBfdXBkYXRlSXNWYWxpZGF0aW5nKFtuYW1lXSwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZmllbGRFcnJvciA9IGF3YWl0IHZhbGlkYXRlRmllbGQoZmllbGQsIF9uYW1lcy5kaXNhYmxlZCwgX2Zvcm1WYWx1ZXMsIHNob3VsZERpc3BsYXlBbGxBc3NvY2lhdGVkRXJyb3JzLCBfb3B0aW9ucy5zaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uICYmICFzaG91bGRPbmx5Q2hlY2tWYWxpZCwgaXNGaWVsZEFycmF5Um9vdCk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChpc1Byb21pc2VGdW5jdGlvbiAmJiBfcHJveHlGb3JtU3RhdGUudmFsaWRhdGluZ0ZpZWxkcykge1xuICAgICAgICAgICAgICAgICAgICAgICAgX3VwZGF0ZUlzVmFsaWRhdGluZyhbbmFtZV0pO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGlmIChmaWVsZEVycm9yW19mLm5hbWVdKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb250ZXh0LnZhbGlkID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoc2hvdWxkT25seUNoZWNrVmFsaWQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBicmVhaztcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAhc2hvdWxkT25seUNoZWNrVmFsaWQgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgIChnZXQoZmllbGRFcnJvciwgX2YubmFtZSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IGlzRmllbGRBcnJheVJvb3RcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyB1cGRhdGVGaWVsZEFycmF5Um9vdEVycm9yKF9mb3JtU3RhdGUuZXJyb3JzLCBmaWVsZEVycm9yLCBfZi5uYW1lKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHNldChfZm9ybVN0YXRlLmVycm9ycywgX2YubmFtZSwgZmllbGRFcnJvcltfZi5uYW1lXSlcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHVuc2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBfZi5uYW1lKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICFpc0VtcHR5T2JqZWN0KGZpZWxkVmFsdWUpICYmXG4gICAgICAgICAgICAgICAgICAgIChhd2FpdCBleGVjdXRlQnVpbHRJblZhbGlkYXRpb24oZmllbGRWYWx1ZSwgc2hvdWxkT25seUNoZWNrVmFsaWQsIGNvbnRleHQpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gY29udGV4dC52YWxpZDtcbiAgICB9O1xuICAgIGNvbnN0IF9yZW1vdmVVbm1vdW50ZWQgPSAoKSA9PiB7XG4gICAgICAgIGZvciAoY29uc3QgbmFtZSBvZiBfbmFtZXMudW5Nb3VudCkge1xuICAgICAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICBmaWVsZCAmJlxuICAgICAgICAgICAgICAgIChmaWVsZC5fZi5yZWZzXG4gICAgICAgICAgICAgICAgICAgID8gZmllbGQuX2YucmVmcy5ldmVyeSgocmVmKSA9PiAhbGl2ZShyZWYpKVxuICAgICAgICAgICAgICAgICAgICA6ICFsaXZlKGZpZWxkLl9mLnJlZikpICYmXG4gICAgICAgICAgICAgICAgdW5yZWdpc3RlcihuYW1lKTtcbiAgICAgICAgfVxuICAgICAgICBfbmFtZXMudW5Nb3VudCA9IG5ldyBTZXQoKTtcbiAgICB9O1xuICAgIGNvbnN0IF9nZXREaXJ0eSA9IChuYW1lLCBkYXRhKSA9PiAhX29wdGlvbnMuZGlzYWJsZWQgJiZcbiAgICAgICAgKG5hbWUgJiYgZGF0YSAmJiBzZXQoX2Zvcm1WYWx1ZXMsIG5hbWUsIGRhdGEpLFxuICAgICAgICAgICAgIWRlZXBFcXVhbChnZXRWYWx1ZXMoKSwgX2RlZmF1bHRWYWx1ZXMpKTtcbiAgICBjb25zdCBfZ2V0V2F0Y2ggPSAobmFtZXMsIGRlZmF1bHRWYWx1ZSwgaXNHbG9iYWwpID0+IGdlbmVyYXRlV2F0Y2hPdXRwdXQobmFtZXMsIF9uYW1lcywge1xuICAgICAgICAuLi4oX3N0YXRlLm1vdW50XG4gICAgICAgICAgICA/IF9mb3JtVmFsdWVzXG4gICAgICAgICAgICA6IGlzVW5kZWZpbmVkKGRlZmF1bHRWYWx1ZSlcbiAgICAgICAgICAgICAgICA/IF9kZWZhdWx0VmFsdWVzXG4gICAgICAgICAgICAgICAgOiBpc1N0cmluZyhuYW1lcylcbiAgICAgICAgICAgICAgICAgICAgPyB7IFtuYW1lc106IGRlZmF1bHRWYWx1ZSB9XG4gICAgICAgICAgICAgICAgICAgIDogZGVmYXVsdFZhbHVlKSxcbiAgICB9LCBpc0dsb2JhbCwgZGVmYXVsdFZhbHVlKTtcbiAgICBjb25zdCBfZ2V0RmllbGRBcnJheSA9IChuYW1lKSA9PiBjb21wYWN0KGdldChfc3RhdGUubW91bnQgPyBfZm9ybVZhbHVlcyA6IF9kZWZhdWx0VmFsdWVzLCBuYW1lLCBfb3B0aW9ucy5zaG91bGRVbnJlZ2lzdGVyID8gZ2V0KF9kZWZhdWx0VmFsdWVzLCBuYW1lLCBbXSkgOiBbXSkpO1xuICAgIGNvbnN0IHNldEZpZWxkVmFsdWUgPSAobmFtZSwgdmFsdWUsIG9wdGlvbnMgPSB7fSkgPT4ge1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgbGV0IGZpZWxkVmFsdWUgPSB2YWx1ZTtcbiAgICAgICAgaWYgKGZpZWxkKSB7XG4gICAgICAgICAgICBjb25zdCBmaWVsZFJlZmVyZW5jZSA9IGZpZWxkLl9mO1xuICAgICAgICAgICAgaWYgKGZpZWxkUmVmZXJlbmNlKSB7XG4gICAgICAgICAgICAgICAgIWZpZWxkUmVmZXJlbmNlLmRpc2FibGVkICYmXG4gICAgICAgICAgICAgICAgICAgIHNldChfZm9ybVZhbHVlcywgbmFtZSwgZ2V0RmllbGRWYWx1ZUFzKHZhbHVlLCBmaWVsZFJlZmVyZW5jZSkpO1xuICAgICAgICAgICAgICAgIGZpZWxkVmFsdWUgPVxuICAgICAgICAgICAgICAgICAgICBpc0hUTUxFbGVtZW50KGZpZWxkUmVmZXJlbmNlLnJlZikgJiYgaXNOdWxsT3JVbmRlZmluZWQodmFsdWUpXG4gICAgICAgICAgICAgICAgICAgICAgICA/ICcnXG4gICAgICAgICAgICAgICAgICAgICAgICA6IHZhbHVlO1xuICAgICAgICAgICAgICAgIGlmIChpc011bHRpcGxlU2VsZWN0KGZpZWxkUmVmZXJlbmNlLnJlZikpIHtcbiAgICAgICAgICAgICAgICAgICAgWy4uLmZpZWxkUmVmZXJlbmNlLnJlZi5vcHRpb25zXS5mb3JFYWNoKChvcHRpb25SZWYpID0+IChvcHRpb25SZWYuc2VsZWN0ZWQgPSBmaWVsZFZhbHVlLmluY2x1ZGVzKG9wdGlvblJlZi52YWx1ZSkpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSBpZiAoZmllbGRSZWZlcmVuY2UucmVmcykge1xuICAgICAgICAgICAgICAgICAgICBpZiAoaXNDaGVja0JveElucHV0KGZpZWxkUmVmZXJlbmNlLnJlZikpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpZWxkUmVmZXJlbmNlLnJlZnMuZm9yRWFjaCgoY2hlY2tib3hSZWYpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWNoZWNrYm94UmVmLmRlZmF1bHRDaGVja2VkIHx8ICFjaGVja2JveFJlZi5kaXNhYmxlZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShmaWVsZFZhbHVlKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tib3hSZWYuY2hlY2tlZCA9ICEhZmllbGRWYWx1ZS5maW5kKChkYXRhKSA9PiBkYXRhID09PSBjaGVja2JveFJlZi52YWx1ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjaGVja2JveFJlZi5jaGVja2VkID1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWVsZFZhbHVlID09PSBjaGVja2JveFJlZi52YWx1ZSB8fCAhIWZpZWxkVmFsdWU7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGZpZWxkUmVmZXJlbmNlLnJlZnMuZm9yRWFjaCgocmFkaW9SZWYpID0+IChyYWRpb1JlZi5jaGVja2VkID0gcmFkaW9SZWYudmFsdWUgPT09IGZpZWxkVmFsdWUpKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmIChpc0ZpbGVJbnB1dChmaWVsZFJlZmVyZW5jZS5yZWYpKSB7XG4gICAgICAgICAgICAgICAgICAgIGZpZWxkUmVmZXJlbmNlLnJlZi52YWx1ZSA9ICcnO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgZmllbGRSZWZlcmVuY2UucmVmLnZhbHVlID0gZmllbGRWYWx1ZTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKCFmaWVsZFJlZmVyZW5jZS5yZWYudHlwZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFsdWVzOiBjbG9uZU9iamVjdChfZm9ybVZhbHVlcyksXG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICAob3B0aW9ucy5zaG91bGREaXJ0eSB8fCBvcHRpb25zLnNob3VsZFRvdWNoKSAmJlxuICAgICAgICAgICAgdXBkYXRlVG91Y2hBbmREaXJ0eShuYW1lLCBmaWVsZFZhbHVlLCBvcHRpb25zLnNob3VsZFRvdWNoLCBvcHRpb25zLnNob3VsZERpcnR5LCB0cnVlKTtcbiAgICAgICAgb3B0aW9ucy5zaG91bGRWYWxpZGF0ZSAmJiB0cmlnZ2VyKG5hbWUpO1xuICAgIH07XG4gICAgY29uc3Qgc2V0VmFsdWVzID0gKG5hbWUsIHZhbHVlLCBvcHRpb25zKSA9PiB7XG4gICAgICAgIGZvciAoY29uc3QgZmllbGRLZXkgaW4gdmFsdWUpIHtcbiAgICAgICAgICAgIGlmICghdmFsdWUuaGFzT3duUHJvcGVydHkoZmllbGRLZXkpKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgZmllbGRWYWx1ZSA9IHZhbHVlW2ZpZWxkS2V5XTtcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkTmFtZSA9IGAke25hbWV9LiR7ZmllbGRLZXl9YDtcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KF9maWVsZHMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICAoX25hbWVzLmFycmF5LmhhcyhuYW1lKSB8fFxuICAgICAgICAgICAgICAgIGlzT2JqZWN0KGZpZWxkVmFsdWUpIHx8XG4gICAgICAgICAgICAgICAgKGZpZWxkICYmICFmaWVsZC5fZikpICYmXG4gICAgICAgICAgICAgICAgIWlzRGF0ZU9iamVjdChmaWVsZFZhbHVlKVxuICAgICAgICAgICAgICAgID8gc2V0VmFsdWVzKGZpZWxkTmFtZSwgZmllbGRWYWx1ZSwgb3B0aW9ucylcbiAgICAgICAgICAgICAgICA6IHNldEZpZWxkVmFsdWUoZmllbGROYW1lLCBmaWVsZFZhbHVlLCBvcHRpb25zKTtcbiAgICAgICAgfVxuICAgIH07XG4gICAgY29uc3Qgc2V0VmFsdWUgPSAobmFtZSwgdmFsdWUsIG9wdGlvbnMgPSB7fSkgPT4ge1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgY29uc3QgaXNGaWVsZEFycmF5ID0gX25hbWVzLmFycmF5LmhhcyhuYW1lKTtcbiAgICAgICAgY29uc3QgY2xvbmVWYWx1ZSA9IGNsb25lT2JqZWN0KHZhbHVlKTtcbiAgICAgICAgc2V0KF9mb3JtVmFsdWVzLCBuYW1lLCBjbG9uZVZhbHVlKTtcbiAgICAgICAgaWYgKGlzRmllbGRBcnJheSkge1xuICAgICAgICAgICAgX3N1YmplY3RzLmFycmF5Lm5leHQoe1xuICAgICAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICAgICAgdmFsdWVzOiBjbG9uZU9iamVjdChfZm9ybVZhbHVlcyksXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGlmICgoX3Byb3h5Rm9ybVN0YXRlLmlzRGlydHkgfHxcbiAgICAgICAgICAgICAgICBfcHJveHlGb3JtU3RhdGUuZGlydHlGaWVsZHMgfHxcbiAgICAgICAgICAgICAgICBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUuaXNEaXJ0eSB8fFxuICAgICAgICAgICAgICAgIF9wcm94eVN1YnNjcmliZUZvcm1TdGF0ZS5kaXJ0eUZpZWxkcykgJiZcbiAgICAgICAgICAgICAgICBvcHRpb25zLnNob3VsZERpcnR5KSB7XG4gICAgICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgICAgICAgICBkaXJ0eUZpZWxkczogZ2V0RGlydHlGaWVsZHMoX2RlZmF1bHRWYWx1ZXMsIF9mb3JtVmFsdWVzKSxcbiAgICAgICAgICAgICAgICAgICAgaXNEaXJ0eTogX2dldERpcnR5KG5hbWUsIGNsb25lVmFsdWUpLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgZmllbGQgJiYgIWZpZWxkLl9mICYmICFpc051bGxPclVuZGVmaW5lZChjbG9uZVZhbHVlKVxuICAgICAgICAgICAgICAgID8gc2V0VmFsdWVzKG5hbWUsIGNsb25lVmFsdWUsIG9wdGlvbnMpXG4gICAgICAgICAgICAgICAgOiBzZXRGaWVsZFZhbHVlKG5hbWUsIGNsb25lVmFsdWUsIG9wdGlvbnMpO1xuICAgICAgICB9XG4gICAgICAgIGlzV2F0Y2hlZChuYW1lLCBfbmFtZXMpICYmIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHsgLi4uX2Zvcm1TdGF0ZSB9KTtcbiAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgbmFtZTogX3N0YXRlLm1vdW50ID8gbmFtZSA6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIHZhbHVlczogY2xvbmVPYmplY3QoX2Zvcm1WYWx1ZXMpLFxuICAgICAgICB9KTtcbiAgICB9O1xuICAgIGNvbnN0IG9uQ2hhbmdlID0gYXN5bmMgKGV2ZW50KSA9PiB7XG4gICAgICAgIF9zdGF0ZS5tb3VudCA9IHRydWU7XG4gICAgICAgIGNvbnN0IHRhcmdldCA9IGV2ZW50LnRhcmdldDtcbiAgICAgICAgbGV0IG5hbWUgPSB0YXJnZXQubmFtZTtcbiAgICAgICAgbGV0IGlzRmllbGRWYWx1ZVVwZGF0ZWQgPSB0cnVlO1xuICAgICAgICBjb25zdCBmaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgY29uc3QgX3VwZGF0ZUlzRmllbGRWYWx1ZVVwZGF0ZWQgPSAoZmllbGRWYWx1ZSkgPT4ge1xuICAgICAgICAgICAgaXNGaWVsZFZhbHVlVXBkYXRlZCA9XG4gICAgICAgICAgICAgICAgTnVtYmVyLmlzTmFOKGZpZWxkVmFsdWUpIHx8XG4gICAgICAgICAgICAgICAgICAgIChpc0RhdGVPYmplY3QoZmllbGRWYWx1ZSkgJiYgaXNOYU4oZmllbGRWYWx1ZS5nZXRUaW1lKCkpKSB8fFxuICAgICAgICAgICAgICAgICAgICBkZWVwRXF1YWwoZmllbGRWYWx1ZSwgZ2V0KF9mb3JtVmFsdWVzLCBuYW1lLCBmaWVsZFZhbHVlKSk7XG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IHZhbGlkYXRpb25Nb2RlQmVmb3JlU3VibWl0ID0gZ2V0VmFsaWRhdGlvbk1vZGVzKF9vcHRpb25zLm1vZGUpO1xuICAgICAgICBjb25zdCB2YWxpZGF0aW9uTW9kZUFmdGVyU3VibWl0ID0gZ2V0VmFsaWRhdGlvbk1vZGVzKF9vcHRpb25zLnJlVmFsaWRhdGVNb2RlKTtcbiAgICAgICAgaWYgKGZpZWxkKSB7XG4gICAgICAgICAgICBsZXQgZXJyb3I7XG4gICAgICAgICAgICBsZXQgaXNWYWxpZDtcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkVmFsdWUgPSB0YXJnZXQudHlwZVxuICAgICAgICAgICAgICAgID8gZ2V0RmllbGRWYWx1ZShmaWVsZC5fZilcbiAgICAgICAgICAgICAgICA6IGdldEV2ZW50VmFsdWUoZXZlbnQpO1xuICAgICAgICAgICAgY29uc3QgaXNCbHVyRXZlbnQgPSBldmVudC50eXBlID09PSBFVkVOVFMuQkxVUiB8fCBldmVudC50eXBlID09PSBFVkVOVFMuRk9DVVNfT1VUO1xuICAgICAgICAgICAgY29uc3Qgc2hvdWxkU2tpcFZhbGlkYXRpb24gPSAoIWhhc1ZhbGlkYXRpb24oZmllbGQuX2YpICYmXG4gICAgICAgICAgICAgICAgIV9vcHRpb25zLnJlc29sdmVyICYmXG4gICAgICAgICAgICAgICAgIWdldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSkgJiZcbiAgICAgICAgICAgICAgICAhZmllbGQuX2YuZGVwcykgfHxcbiAgICAgICAgICAgICAgICBza2lwVmFsaWRhdGlvbihpc0JsdXJFdmVudCwgZ2V0KF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkcywgbmFtZSksIF9mb3JtU3RhdGUuaXNTdWJtaXR0ZWQsIHZhbGlkYXRpb25Nb2RlQWZ0ZXJTdWJtaXQsIHZhbGlkYXRpb25Nb2RlQmVmb3JlU3VibWl0KTtcbiAgICAgICAgICAgIGNvbnN0IHdhdGNoZWQgPSBpc1dhdGNoZWQobmFtZSwgX25hbWVzLCBpc0JsdXJFdmVudCk7XG4gICAgICAgICAgICBzZXQoX2Zvcm1WYWx1ZXMsIG5hbWUsIGZpZWxkVmFsdWUpO1xuICAgICAgICAgICAgaWYgKGlzQmx1ckV2ZW50KSB7XG4gICAgICAgICAgICAgICAgZmllbGQuX2Yub25CbHVyICYmIGZpZWxkLl9mLm9uQmx1cihldmVudCk7XG4gICAgICAgICAgICAgICAgZGVsYXlFcnJvckNhbGxiYWNrICYmIGRlbGF5RXJyb3JDYWxsYmFjaygwKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGVsc2UgaWYgKGZpZWxkLl9mLm9uQ2hhbmdlKSB7XG4gICAgICAgICAgICAgICAgZmllbGQuX2Yub25DaGFuZ2UoZXZlbnQpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgZmllbGRTdGF0ZSA9IHVwZGF0ZVRvdWNoQW5kRGlydHkobmFtZSwgZmllbGRWYWx1ZSwgaXNCbHVyRXZlbnQpO1xuICAgICAgICAgICAgY29uc3Qgc2hvdWxkUmVuZGVyID0gIWlzRW1wdHlPYmplY3QoZmllbGRTdGF0ZSkgfHwgd2F0Y2hlZDtcbiAgICAgICAgICAgICFpc0JsdXJFdmVudCAmJlxuICAgICAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogZXZlbnQudHlwZSxcbiAgICAgICAgICAgICAgICAgICAgdmFsdWVzOiBjbG9uZU9iamVjdChfZm9ybVZhbHVlcyksXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBpZiAoc2hvdWxkU2tpcFZhbGlkYXRpb24pIHtcbiAgICAgICAgICAgICAgICBpZiAoX3Byb3h5Rm9ybVN0YXRlLmlzVmFsaWQgfHwgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLmlzVmFsaWQpIHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKF9vcHRpb25zLm1vZGUgPT09ICdvbkJsdXInKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoaXNCbHVyRXZlbnQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBfc2V0VmFsaWQoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIGlmICghaXNCbHVyRXZlbnQpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIF9zZXRWYWxpZCgpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybiAoc2hvdWxkUmVuZGVyICYmXG4gICAgICAgICAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHsgbmFtZSwgLi4uKHdhdGNoZWQgPyB7fSA6IGZpZWxkU3RhdGUpIH0pKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgICFpc0JsdXJFdmVudCAmJiB3YXRjaGVkICYmIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHsgLi4uX2Zvcm1TdGF0ZSB9KTtcbiAgICAgICAgICAgIGlmIChfb3B0aW9ucy5yZXNvbHZlcikge1xuICAgICAgICAgICAgICAgIGNvbnN0IHsgZXJyb3JzIH0gPSBhd2FpdCBfcnVuU2NoZW1hKFtuYW1lXSk7XG4gICAgICAgICAgICAgICAgX3VwZGF0ZUlzRmllbGRWYWx1ZVVwZGF0ZWQoZmllbGRWYWx1ZSk7XG4gICAgICAgICAgICAgICAgaWYgKGlzRmllbGRWYWx1ZVVwZGF0ZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcHJldmlvdXNFcnJvckxvb2t1cFJlc3VsdCA9IHNjaGVtYUVycm9yTG9va3VwKF9mb3JtU3RhdGUuZXJyb3JzLCBfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZXJyb3JMb29rdXBSZXN1bHQgPSBzY2hlbWFFcnJvckxvb2t1cChlcnJvcnMsIF9maWVsZHMsIHByZXZpb3VzRXJyb3JMb29rdXBSZXN1bHQubmFtZSB8fCBuYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgZXJyb3IgPSBlcnJvckxvb2t1cFJlc3VsdC5lcnJvcjtcbiAgICAgICAgICAgICAgICAgICAgbmFtZSA9IGVycm9yTG9va3VwUmVzdWx0Lm5hbWU7XG4gICAgICAgICAgICAgICAgICAgIGlzVmFsaWQgPSBpc0VtcHR5T2JqZWN0KGVycm9ycyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgX3VwZGF0ZUlzVmFsaWRhdGluZyhbbmFtZV0sIHRydWUpO1xuICAgICAgICAgICAgICAgIGVycm9yID0gKGF3YWl0IHZhbGlkYXRlRmllbGQoZmllbGQsIF9uYW1lcy5kaXNhYmxlZCwgX2Zvcm1WYWx1ZXMsIHNob3VsZERpc3BsYXlBbGxBc3NvY2lhdGVkRXJyb3JzLCBfb3B0aW9ucy5zaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uKSlbbmFtZV07XG4gICAgICAgICAgICAgICAgX3VwZGF0ZUlzVmFsaWRhdGluZyhbbmFtZV0pO1xuICAgICAgICAgICAgICAgIF91cGRhdGVJc0ZpZWxkVmFsdWVVcGRhdGVkKGZpZWxkVmFsdWUpO1xuICAgICAgICAgICAgICAgIGlmIChpc0ZpZWxkVmFsdWVVcGRhdGVkKSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChlcnJvcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgaXNWYWxpZCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2UgaWYgKF9wcm94eUZvcm1TdGF0ZS5pc1ZhbGlkIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUuaXNWYWxpZCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgaXNWYWxpZCA9IGF3YWl0IGV4ZWN1dGVCdWlsdEluVmFsaWRhdGlvbihfZmllbGRzLCB0cnVlKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChpc0ZpZWxkVmFsdWVVcGRhdGVkKSB7XG4gICAgICAgICAgICAgICAgZmllbGQuX2YuZGVwcyAmJlxuICAgICAgICAgICAgICAgICAgICB0cmlnZ2VyKGZpZWxkLl9mLmRlcHMpO1xuICAgICAgICAgICAgICAgIHNob3VsZFJlbmRlckJ5RXJyb3IobmFtZSwgaXNWYWxpZCwgZXJyb3IsIGZpZWxkU3RhdGUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBfZm9jdXNJbnB1dCA9IChyZWYsIGtleSkgPT4ge1xuICAgICAgICBpZiAoZ2V0KF9mb3JtU3RhdGUuZXJyb3JzLCBrZXkpICYmIHJlZi5mb2N1cykge1xuICAgICAgICAgICAgcmVmLmZvY3VzKCk7XG4gICAgICAgICAgICByZXR1cm4gMTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm47XG4gICAgfTtcbiAgICBjb25zdCB0cmlnZ2VyID0gYXN5bmMgKG5hbWUsIG9wdGlvbnMgPSB7fSkgPT4ge1xuICAgICAgICBsZXQgaXNWYWxpZDtcbiAgICAgICAgbGV0IHZhbGlkYXRpb25SZXN1bHQ7XG4gICAgICAgIGNvbnN0IGZpZWxkTmFtZXMgPSBjb252ZXJ0VG9BcnJheVBheWxvYWQobmFtZSk7XG4gICAgICAgIGlmIChfb3B0aW9ucy5yZXNvbHZlcikge1xuICAgICAgICAgICAgY29uc3QgZXJyb3JzID0gYXdhaXQgZXhlY3V0ZVNjaGVtYUFuZFVwZGF0ZVN0YXRlKGlzVW5kZWZpbmVkKG5hbWUpID8gbmFtZSA6IGZpZWxkTmFtZXMpO1xuICAgICAgICAgICAgaXNWYWxpZCA9IGlzRW1wdHlPYmplY3QoZXJyb3JzKTtcbiAgICAgICAgICAgIHZhbGlkYXRpb25SZXN1bHQgPSBuYW1lXG4gICAgICAgICAgICAgICAgPyAhZmllbGROYW1lcy5zb21lKChuYW1lKSA9PiBnZXQoZXJyb3JzLCBuYW1lKSlcbiAgICAgICAgICAgICAgICA6IGlzVmFsaWQ7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAobmFtZSkge1xuICAgICAgICAgICAgdmFsaWRhdGlvblJlc3VsdCA9IChhd2FpdCBQcm9taXNlLmFsbChmaWVsZE5hbWVzLm1hcChhc3luYyAoZmllbGROYW1lKSA9PiB7XG4gICAgICAgICAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoX2ZpZWxkcywgZmllbGROYW1lKTtcbiAgICAgICAgICAgICAgICByZXR1cm4gYXdhaXQgZXhlY3V0ZUJ1aWx0SW5WYWxpZGF0aW9uKGZpZWxkICYmIGZpZWxkLl9mID8geyBbZmllbGROYW1lXTogZmllbGQgfSA6IGZpZWxkKTtcbiAgICAgICAgICAgIH0pKSkuZXZlcnkoQm9vbGVhbik7XG4gICAgICAgICAgICAhKCF2YWxpZGF0aW9uUmVzdWx0ICYmICFfZm9ybVN0YXRlLmlzVmFsaWQpICYmIF9zZXRWYWxpZCgpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdmFsaWRhdGlvblJlc3VsdCA9IGlzVmFsaWQgPSBhd2FpdCBleGVjdXRlQnVpbHRJblZhbGlkYXRpb24oX2ZpZWxkcyk7XG4gICAgICAgIH1cbiAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgLi4uKCFpc1N0cmluZyhuYW1lKSB8fFxuICAgICAgICAgICAgICAgICgoX3Byb3h5Rm9ybVN0YXRlLmlzVmFsaWQgfHwgX3Byb3h5U3Vic2NyaWJlRm9ybVN0YXRlLmlzVmFsaWQpICYmXG4gICAgICAgICAgICAgICAgICAgIGlzVmFsaWQgIT09IF9mb3JtU3RhdGUuaXNWYWxpZClcbiAgICAgICAgICAgICAgICA/IHt9XG4gICAgICAgICAgICAgICAgOiB7IG5hbWUgfSksXG4gICAgICAgICAgICAuLi4oX29wdGlvbnMucmVzb2x2ZXIgfHwgIW5hbWUgPyB7IGlzVmFsaWQgfSA6IHt9KSxcbiAgICAgICAgICAgIGVycm9yczogX2Zvcm1TdGF0ZS5lcnJvcnMsXG4gICAgICAgIH0pO1xuICAgICAgICBvcHRpb25zLnNob3VsZEZvY3VzICYmXG4gICAgICAgICAgICAhdmFsaWRhdGlvblJlc3VsdCAmJlxuICAgICAgICAgICAgaXRlcmF0ZUZpZWxkc0J5QWN0aW9uKF9maWVsZHMsIF9mb2N1c0lucHV0LCBuYW1lID8gZmllbGROYW1lcyA6IF9uYW1lcy5tb3VudCk7XG4gICAgICAgIHJldHVybiB2YWxpZGF0aW9uUmVzdWx0O1xuICAgIH07XG4gICAgY29uc3QgZ2V0VmFsdWVzID0gKGZpZWxkTmFtZXMpID0+IHtcbiAgICAgICAgY29uc3QgdmFsdWVzID0ge1xuICAgICAgICAgICAgLi4uKF9zdGF0ZS5tb3VudCA/IF9mb3JtVmFsdWVzIDogX2RlZmF1bHRWYWx1ZXMpLFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gaXNVbmRlZmluZWQoZmllbGROYW1lcylcbiAgICAgICAgICAgID8gdmFsdWVzXG4gICAgICAgICAgICA6IGlzU3RyaW5nKGZpZWxkTmFtZXMpXG4gICAgICAgICAgICAgICAgPyBnZXQodmFsdWVzLCBmaWVsZE5hbWVzKVxuICAgICAgICAgICAgICAgIDogZmllbGROYW1lcy5tYXAoKG5hbWUpID0+IGdldCh2YWx1ZXMsIG5hbWUpKTtcbiAgICB9O1xuICAgIGNvbnN0IGdldEZpZWxkU3RhdGUgPSAobmFtZSwgZm9ybVN0YXRlKSA9PiAoe1xuICAgICAgICBpbnZhbGlkOiAhIWdldCgoZm9ybVN0YXRlIHx8IF9mb3JtU3RhdGUpLmVycm9ycywgbmFtZSksXG4gICAgICAgIGlzRGlydHk6ICEhZ2V0KChmb3JtU3RhdGUgfHwgX2Zvcm1TdGF0ZSkuZGlydHlGaWVsZHMsIG5hbWUpLFxuICAgICAgICBlcnJvcjogZ2V0KChmb3JtU3RhdGUgfHwgX2Zvcm1TdGF0ZSkuZXJyb3JzLCBuYW1lKSxcbiAgICAgICAgaXNWYWxpZGF0aW5nOiAhIWdldChfZm9ybVN0YXRlLnZhbGlkYXRpbmdGaWVsZHMsIG5hbWUpLFxuICAgICAgICBpc1RvdWNoZWQ6ICEhZ2V0KChmb3JtU3RhdGUgfHwgX2Zvcm1TdGF0ZSkudG91Y2hlZEZpZWxkcywgbmFtZSksXG4gICAgfSk7XG4gICAgY29uc3QgY2xlYXJFcnJvcnMgPSAobmFtZSkgPT4ge1xuICAgICAgICBuYW1lICYmXG4gICAgICAgICAgICBjb252ZXJ0VG9BcnJheVBheWxvYWQobmFtZSkuZm9yRWFjaCgoaW5wdXROYW1lKSA9PiB1bnNldChfZm9ybVN0YXRlLmVycm9ycywgaW5wdXROYW1lKSk7XG4gICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgIGVycm9yczogbmFtZSA/IF9mb3JtU3RhdGUuZXJyb3JzIDoge30sXG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgY29uc3Qgc2V0RXJyb3IgPSAobmFtZSwgZXJyb3IsIG9wdGlvbnMpID0+IHtcbiAgICAgICAgY29uc3QgcmVmID0gKGdldChfZmllbGRzLCBuYW1lLCB7IF9mOiB7fSB9KS5fZiB8fCB7fSkucmVmO1xuICAgICAgICBjb25zdCBjdXJyZW50RXJyb3IgPSBnZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUpIHx8IHt9O1xuICAgICAgICAvLyBEb24ndCBvdmVycmlkZSBleGlzdGluZyBlcnJvciBtZXNzYWdlcyBlbHNld2hlcmUgaW4gdGhlIG9iamVjdCB0cmVlLlxuICAgICAgICBjb25zdCB7IHJlZjogY3VycmVudFJlZiwgbWVzc2FnZSwgdHlwZSwgLi4ucmVzdE9mRXJyb3JUcmVlIH0gPSBjdXJyZW50RXJyb3I7XG4gICAgICAgIHNldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSwge1xuICAgICAgICAgICAgLi4ucmVzdE9mRXJyb3JUcmVlLFxuICAgICAgICAgICAgLi4uZXJyb3IsXG4gICAgICAgICAgICByZWYsXG4gICAgICAgIH0pO1xuICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICBuYW1lLFxuICAgICAgICAgICAgZXJyb3JzOiBfZm9ybVN0YXRlLmVycm9ycyxcbiAgICAgICAgICAgIGlzVmFsaWQ6IGZhbHNlLFxuICAgICAgICB9KTtcbiAgICAgICAgb3B0aW9ucyAmJiBvcHRpb25zLnNob3VsZEZvY3VzICYmIHJlZiAmJiByZWYuZm9jdXMgJiYgcmVmLmZvY3VzKCk7XG4gICAgfTtcbiAgICBjb25zdCB3YXRjaCA9IChuYW1lLCBkZWZhdWx0VmFsdWUpID0+IGlzRnVuY3Rpb24obmFtZSlcbiAgICAgICAgPyBfc3ViamVjdHMuc3RhdGUuc3Vic2NyaWJlKHtcbiAgICAgICAgICAgIG5leHQ6IChwYXlsb2FkKSA9PiBuYW1lKF9nZXRXYXRjaCh1bmRlZmluZWQsIGRlZmF1bHRWYWx1ZSksIHBheWxvYWQpLFxuICAgICAgICB9KVxuICAgICAgICA6IF9nZXRXYXRjaChuYW1lLCBkZWZhdWx0VmFsdWUsIHRydWUpO1xuICAgIGNvbnN0IF9zdWJzY3JpYmUgPSAocHJvcHMpID0+IF9zdWJqZWN0cy5zdGF0ZS5zdWJzY3JpYmUoe1xuICAgICAgICBuZXh0OiAoZm9ybVN0YXRlKSA9PiB7XG4gICAgICAgICAgICBpZiAoc2hvdWxkU3Vic2NyaWJlQnlOYW1lKHByb3BzLm5hbWUsIGZvcm1TdGF0ZS5uYW1lLCBwcm9wcy5leGFjdCkgJiZcbiAgICAgICAgICAgICAgICBzaG91bGRSZW5kZXJGb3JtU3RhdGUoZm9ybVN0YXRlLCBwcm9wcy5mb3JtU3RhdGUgfHwgX3Byb3h5Rm9ybVN0YXRlLCBfc2V0Rm9ybVN0YXRlLCBwcm9wcy5yZVJlbmRlclJvb3QpKSB7XG4gICAgICAgICAgICAgICAgcHJvcHMuY2FsbGJhY2soe1xuICAgICAgICAgICAgICAgICAgICB2YWx1ZXM6IHsgLi4uX2Zvcm1WYWx1ZXMgfSxcbiAgICAgICAgICAgICAgICAgICAgLi4uX2Zvcm1TdGF0ZSxcbiAgICAgICAgICAgICAgICAgICAgLi4uZm9ybVN0YXRlLFxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgIH0pLnVuc3Vic2NyaWJlO1xuICAgIGNvbnN0IHN1YnNjcmliZSA9IChwcm9wcykgPT4ge1xuICAgICAgICBfc3RhdGUubW91bnQgPSB0cnVlO1xuICAgICAgICBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUgPSB7XG4gICAgICAgICAgICAuLi5fcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUsXG4gICAgICAgICAgICAuLi5wcm9wcy5mb3JtU3RhdGUsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiBfc3Vic2NyaWJlKHtcbiAgICAgICAgICAgIC4uLnByb3BzLFxuICAgICAgICAgICAgZm9ybVN0YXRlOiBfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUsXG4gICAgICAgIH0pO1xuICAgIH07XG4gICAgY29uc3QgdW5yZWdpc3RlciA9IChuYW1lLCBvcHRpb25zID0ge30pID0+IHtcbiAgICAgICAgZm9yIChjb25zdCBmaWVsZE5hbWUgb2YgbmFtZSA/IGNvbnZlcnRUb0FycmF5UGF5bG9hZChuYW1lKSA6IF9uYW1lcy5tb3VudCkge1xuICAgICAgICAgICAgX25hbWVzLm1vdW50LmRlbGV0ZShmaWVsZE5hbWUpO1xuICAgICAgICAgICAgX25hbWVzLmFycmF5LmRlbGV0ZShmaWVsZE5hbWUpO1xuICAgICAgICAgICAgaWYgKCFvcHRpb25zLmtlZXBWYWx1ZSkge1xuICAgICAgICAgICAgICAgIHVuc2V0KF9maWVsZHMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICAgICAgdW5zZXQoX2Zvcm1WYWx1ZXMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAhb3B0aW9ucy5rZWVwRXJyb3IgJiYgdW5zZXQoX2Zvcm1TdGF0ZS5lcnJvcnMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICAhb3B0aW9ucy5rZWVwRGlydHkgJiYgdW5zZXQoX2Zvcm1TdGF0ZS5kaXJ0eUZpZWxkcywgZmllbGROYW1lKTtcbiAgICAgICAgICAgICFvcHRpb25zLmtlZXBUb3VjaGVkICYmIHVuc2V0KF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkcywgZmllbGROYW1lKTtcbiAgICAgICAgICAgICFvcHRpb25zLmtlZXBJc1ZhbGlkYXRpbmcgJiZcbiAgICAgICAgICAgICAgICB1bnNldChfZm9ybVN0YXRlLnZhbGlkYXRpbmdGaWVsZHMsIGZpZWxkTmFtZSk7XG4gICAgICAgICAgICAhX29wdGlvbnMuc2hvdWxkVW5yZWdpc3RlciAmJlxuICAgICAgICAgICAgICAgICFvcHRpb25zLmtlZXBEZWZhdWx0VmFsdWUgJiZcbiAgICAgICAgICAgICAgICB1bnNldChfZGVmYXVsdFZhbHVlcywgZmllbGROYW1lKTtcbiAgICAgICAgfVxuICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICB2YWx1ZXM6IGNsb25lT2JqZWN0KF9mb3JtVmFsdWVzKSxcbiAgICAgICAgfSk7XG4gICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgIC4uLl9mb3JtU3RhdGUsXG4gICAgICAgICAgICAuLi4oIW9wdGlvbnMua2VlcERpcnR5ID8ge30gOiB7IGlzRGlydHk6IF9nZXREaXJ0eSgpIH0pLFxuICAgICAgICB9KTtcbiAgICAgICAgIW9wdGlvbnMua2VlcElzVmFsaWQgJiYgX3NldFZhbGlkKCk7XG4gICAgfTtcbiAgICBjb25zdCBfc2V0RGlzYWJsZWRGaWVsZCA9ICh7IGRpc2FibGVkLCBuYW1lLCB9KSA9PiB7XG4gICAgICAgIGlmICgoaXNCb29sZWFuKGRpc2FibGVkKSAmJiBfc3RhdGUubW91bnQpIHx8XG4gICAgICAgICAgICAhIWRpc2FibGVkIHx8XG4gICAgICAgICAgICBfbmFtZXMuZGlzYWJsZWQuaGFzKG5hbWUpKSB7XG4gICAgICAgICAgICBkaXNhYmxlZCA/IF9uYW1lcy5kaXNhYmxlZC5hZGQobmFtZSkgOiBfbmFtZXMuZGlzYWJsZWQuZGVsZXRlKG5hbWUpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCByZWdpc3RlciA9IChuYW1lLCBvcHRpb25zID0ge30pID0+IHtcbiAgICAgICAgbGV0IGZpZWxkID0gZ2V0KF9maWVsZHMsIG5hbWUpO1xuICAgICAgICBjb25zdCBkaXNhYmxlZElzRGVmaW5lZCA9IGlzQm9vbGVhbihvcHRpb25zLmRpc2FibGVkKSB8fCBpc0Jvb2xlYW4oX29wdGlvbnMuZGlzYWJsZWQpO1xuICAgICAgICBzZXQoX2ZpZWxkcywgbmFtZSwge1xuICAgICAgICAgICAgLi4uKGZpZWxkIHx8IHt9KSxcbiAgICAgICAgICAgIF9mOiB7XG4gICAgICAgICAgICAgICAgLi4uKGZpZWxkICYmIGZpZWxkLl9mID8gZmllbGQuX2YgOiB7IHJlZjogeyBuYW1lIH0gfSksXG4gICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgICAgICBtb3VudDogdHJ1ZSxcbiAgICAgICAgICAgICAgICAuLi5vcHRpb25zLFxuICAgICAgICAgICAgfSxcbiAgICAgICAgfSk7XG4gICAgICAgIF9uYW1lcy5tb3VudC5hZGQobmFtZSk7XG4gICAgICAgIGlmIChmaWVsZCkge1xuICAgICAgICAgICAgX3NldERpc2FibGVkRmllbGQoe1xuICAgICAgICAgICAgICAgIGRpc2FibGVkOiBpc0Jvb2xlYW4ob3B0aW9ucy5kaXNhYmxlZClcbiAgICAgICAgICAgICAgICAgICAgPyBvcHRpb25zLmRpc2FibGVkXG4gICAgICAgICAgICAgICAgICAgIDogX29wdGlvbnMuZGlzYWJsZWQsXG4gICAgICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgdXBkYXRlVmFsaWRBbmRWYWx1ZShuYW1lLCB0cnVlLCBvcHRpb25zLnZhbHVlKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgLi4uKGRpc2FibGVkSXNEZWZpbmVkXG4gICAgICAgICAgICAgICAgPyB7IGRpc2FibGVkOiBvcHRpb25zLmRpc2FibGVkIHx8IF9vcHRpb25zLmRpc2FibGVkIH1cbiAgICAgICAgICAgICAgICA6IHt9KSxcbiAgICAgICAgICAgIC4uLihfb3B0aW9ucy5wcm9ncmVzc2l2ZVxuICAgICAgICAgICAgICAgID8ge1xuICAgICAgICAgICAgICAgICAgICByZXF1aXJlZDogISFvcHRpb25zLnJlcXVpcmVkLFxuICAgICAgICAgICAgICAgICAgICBtaW46IGdldFJ1bGVWYWx1ZShvcHRpb25zLm1pbiksXG4gICAgICAgICAgICAgICAgICAgIG1heDogZ2V0UnVsZVZhbHVlKG9wdGlvbnMubWF4KSxcbiAgICAgICAgICAgICAgICAgICAgbWluTGVuZ3RoOiBnZXRSdWxlVmFsdWUob3B0aW9ucy5taW5MZW5ndGgpLFxuICAgICAgICAgICAgICAgICAgICBtYXhMZW5ndGg6IGdldFJ1bGVWYWx1ZShvcHRpb25zLm1heExlbmd0aCksXG4gICAgICAgICAgICAgICAgICAgIHBhdHRlcm46IGdldFJ1bGVWYWx1ZShvcHRpb25zLnBhdHRlcm4pLFxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICA6IHt9KSxcbiAgICAgICAgICAgIG5hbWUsXG4gICAgICAgICAgICBvbkNoYW5nZSxcbiAgICAgICAgICAgIG9uQmx1cjogb25DaGFuZ2UsXG4gICAgICAgICAgICByZWY6IChyZWYpID0+IHtcbiAgICAgICAgICAgICAgICBpZiAocmVmKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlZ2lzdGVyKG5hbWUsIG9wdGlvbnMpO1xuICAgICAgICAgICAgICAgICAgICBmaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZmllbGRSZWYgPSBpc1VuZGVmaW5lZChyZWYudmFsdWUpXG4gICAgICAgICAgICAgICAgICAgICAgICA/IHJlZi5xdWVyeVNlbGVjdG9yQWxsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyByZWYucXVlcnlTZWxlY3RvckFsbCgnaW5wdXQsc2VsZWN0LHRleHRhcmVhJylbMF0gfHwgcmVmXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiByZWZcbiAgICAgICAgICAgICAgICAgICAgICAgIDogcmVmO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByYWRpb09yQ2hlY2tib3ggPSBpc1JhZGlvT3JDaGVja2JveChmaWVsZFJlZik7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHJlZnMgPSBmaWVsZC5fZi5yZWZzIHx8IFtdO1xuICAgICAgICAgICAgICAgICAgICBpZiAocmFkaW9PckNoZWNrYm94XG4gICAgICAgICAgICAgICAgICAgICAgICA/IHJlZnMuZmluZCgob3B0aW9uKSA9PiBvcHRpb24gPT09IGZpZWxkUmVmKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiBmaWVsZFJlZiA9PT0gZmllbGQuX2YucmVmKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgc2V0KF9maWVsZHMsIG5hbWUsIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIF9mOiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uZmllbGQuX2YsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgLi4uKHJhZGlvT3JDaGVja2JveFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlZnM6IFtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5yZWZzLmZpbHRlcihsaXZlKSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBmaWVsZFJlZixcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi4oQXJyYXkuaXNBcnJheShnZXQoX2RlZmF1bHRWYWx1ZXMsIG5hbWUpKSA/IFt7fV0gOiBbXSksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBdLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVmOiB7IHR5cGU6IGZpZWxkUmVmLnR5cGUsIG5hbWUgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHsgcmVmOiBmaWVsZFJlZiB9KSxcbiAgICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgICAgICB1cGRhdGVWYWxpZEFuZFZhbHVlKG5hbWUsIGZhbHNlLCB1bmRlZmluZWQsIGZpZWxkUmVmKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGZpZWxkID0gZ2V0KF9maWVsZHMsIG5hbWUsIHt9KTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGZpZWxkLl9mKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBmaWVsZC5fZi5tb3VudCA9IGZhbHNlO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIChfb3B0aW9ucy5zaG91bGRVbnJlZ2lzdGVyIHx8IG9wdGlvbnMuc2hvdWxkVW5yZWdpc3RlcikgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgICEoaXNOYW1lSW5GaWVsZEFycmF5KF9uYW1lcy5hcnJheSwgbmFtZSkgJiYgX3N0YXRlLmFjdGlvbikgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgIF9uYW1lcy51bk1vdW50LmFkZChuYW1lKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICB9O1xuICAgIH07XG4gICAgY29uc3QgX2ZvY3VzRXJyb3IgPSAoKSA9PiBfb3B0aW9ucy5zaG91bGRGb2N1c0Vycm9yICYmXG4gICAgICAgIGl0ZXJhdGVGaWVsZHNCeUFjdGlvbihfZmllbGRzLCBfZm9jdXNJbnB1dCwgX25hbWVzLm1vdW50KTtcbiAgICBjb25zdCBfZGlzYWJsZUZvcm0gPSAoZGlzYWJsZWQpID0+IHtcbiAgICAgICAgaWYgKGlzQm9vbGVhbihkaXNhYmxlZCkpIHtcbiAgICAgICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHsgZGlzYWJsZWQgfSk7XG4gICAgICAgICAgICBpdGVyYXRlRmllbGRzQnlBY3Rpb24oX2ZpZWxkcywgKHJlZiwgbmFtZSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGN1cnJlbnRGaWVsZCA9IGdldChfZmllbGRzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICBpZiAoY3VycmVudEZpZWxkKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlZi5kaXNhYmxlZCA9IGN1cnJlbnRGaWVsZC5fZi5kaXNhYmxlZCB8fCBkaXNhYmxlZDtcbiAgICAgICAgICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoY3VycmVudEZpZWxkLl9mLnJlZnMpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjdXJyZW50RmllbGQuX2YucmVmcy5mb3JFYWNoKChpbnB1dFJlZikgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlucHV0UmVmLmRpc2FibGVkID0gY3VycmVudEZpZWxkLl9mLmRpc2FibGVkIHx8IGRpc2FibGVkO1xuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LCAwLCBmYWxzZSk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IChvblZhbGlkLCBvbkludmFsaWQpID0+IGFzeW5jIChlKSA9PiB7XG4gICAgICAgIGxldCBvblZhbGlkRXJyb3IgPSB1bmRlZmluZWQ7XG4gICAgICAgIGlmIChlKSB7XG4gICAgICAgICAgICBlLnByZXZlbnREZWZhdWx0ICYmIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgIGUucGVyc2lzdCAmJlxuICAgICAgICAgICAgICAgIGUucGVyc2lzdCgpO1xuICAgICAgICB9XG4gICAgICAgIGxldCBmaWVsZFZhbHVlcyA9IGNsb25lT2JqZWN0KF9mb3JtVmFsdWVzKTtcbiAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgaXNTdWJtaXR0aW5nOiB0cnVlLFxuICAgICAgICB9KTtcbiAgICAgICAgaWYgKF9vcHRpb25zLnJlc29sdmVyKSB7XG4gICAgICAgICAgICBjb25zdCB7IGVycm9ycywgdmFsdWVzIH0gPSBhd2FpdCBfcnVuU2NoZW1hKCk7XG4gICAgICAgICAgICBfZm9ybVN0YXRlLmVycm9ycyA9IGVycm9ycztcbiAgICAgICAgICAgIGZpZWxkVmFsdWVzID0gdmFsdWVzO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgYXdhaXQgZXhlY3V0ZUJ1aWx0SW5WYWxpZGF0aW9uKF9maWVsZHMpO1xuICAgICAgICB9XG4gICAgICAgIGlmIChfbmFtZXMuZGlzYWJsZWQuc2l6ZSkge1xuICAgICAgICAgICAgZm9yIChjb25zdCBuYW1lIG9mIF9uYW1lcy5kaXNhYmxlZCkge1xuICAgICAgICAgICAgICAgIHNldChmaWVsZFZhbHVlcywgbmFtZSwgdW5kZWZpbmVkKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICB1bnNldChfZm9ybVN0YXRlLmVycm9ycywgJ3Jvb3QnKTtcbiAgICAgICAgaWYgKGlzRW1wdHlPYmplY3QoX2Zvcm1TdGF0ZS5lcnJvcnMpKSB7XG4gICAgICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgZXJyb3JzOiB7fSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBhd2FpdCBvblZhbGlkKGZpZWxkVmFsdWVzLCBlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIG9uVmFsaWRFcnJvciA9IGVycm9yO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgaWYgKG9uSW52YWxpZCkge1xuICAgICAgICAgICAgICAgIGF3YWl0IG9uSW52YWxpZCh7IC4uLl9mb3JtU3RhdGUuZXJyb3JzIH0sIGUpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgX2ZvY3VzRXJyb3IoKTtcbiAgICAgICAgICAgIHNldFRpbWVvdXQoX2ZvY3VzRXJyb3IpO1xuICAgICAgICB9XG4gICAgICAgIF9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgIGlzU3VibWl0dGVkOiB0cnVlLFxuICAgICAgICAgICAgaXNTdWJtaXR0aW5nOiBmYWxzZSxcbiAgICAgICAgICAgIGlzU3VibWl0U3VjY2Vzc2Z1bDogaXNFbXB0eU9iamVjdChfZm9ybVN0YXRlLmVycm9ycykgJiYgIW9uVmFsaWRFcnJvcixcbiAgICAgICAgICAgIHN1Ym1pdENvdW50OiBfZm9ybVN0YXRlLnN1Ym1pdENvdW50ICsgMSxcbiAgICAgICAgICAgIGVycm9yczogX2Zvcm1TdGF0ZS5lcnJvcnMsXG4gICAgICAgIH0pO1xuICAgICAgICBpZiAob25WYWxpZEVycm9yKSB7XG4gICAgICAgICAgICB0aHJvdyBvblZhbGlkRXJyb3I7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IHJlc2V0RmllbGQgPSAobmFtZSwgb3B0aW9ucyA9IHt9KSA9PiB7XG4gICAgICAgIGlmIChnZXQoX2ZpZWxkcywgbmFtZSkpIHtcbiAgICAgICAgICAgIGlmIChpc1VuZGVmaW5lZChvcHRpb25zLmRlZmF1bHRWYWx1ZSkpIHtcbiAgICAgICAgICAgICAgICBzZXRWYWx1ZShuYW1lLCBjbG9uZU9iamVjdChnZXQoX2RlZmF1bHRWYWx1ZXMsIG5hbWUpKSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBzZXRWYWx1ZShuYW1lLCBvcHRpb25zLmRlZmF1bHRWYWx1ZSk7XG4gICAgICAgICAgICAgICAgc2V0KF9kZWZhdWx0VmFsdWVzLCBuYW1lLCBjbG9uZU9iamVjdChvcHRpb25zLmRlZmF1bHRWYWx1ZSkpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKCFvcHRpb25zLmtlZXBUb3VjaGVkKSB7XG4gICAgICAgICAgICAgICAgdW5zZXQoX2Zvcm1TdGF0ZS50b3VjaGVkRmllbGRzLCBuYW1lKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghb3B0aW9ucy5rZWVwRGlydHkpIHtcbiAgICAgICAgICAgICAgICB1bnNldChfZm9ybVN0YXRlLmRpcnR5RmllbGRzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICBfZm9ybVN0YXRlLmlzRGlydHkgPSBvcHRpb25zLmRlZmF1bHRWYWx1ZVxuICAgICAgICAgICAgICAgICAgICA/IF9nZXREaXJ0eShuYW1lLCBjbG9uZU9iamVjdChnZXQoX2RlZmF1bHRWYWx1ZXMsIG5hbWUpKSlcbiAgICAgICAgICAgICAgICAgICAgOiBfZ2V0RGlydHkoKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmICghb3B0aW9ucy5rZWVwRXJyb3IpIHtcbiAgICAgICAgICAgICAgICB1bnNldChfZm9ybVN0YXRlLmVycm9ycywgbmFtZSk7XG4gICAgICAgICAgICAgICAgX3Byb3h5Rm9ybVN0YXRlLmlzVmFsaWQgJiYgX3NldFZhbGlkKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7IC4uLl9mb3JtU3RhdGUgfSk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IF9yZXNldCA9IChmb3JtVmFsdWVzLCBrZWVwU3RhdGVPcHRpb25zID0ge30pID0+IHtcbiAgICAgICAgY29uc3QgdXBkYXRlZFZhbHVlcyA9IGZvcm1WYWx1ZXMgPyBjbG9uZU9iamVjdChmb3JtVmFsdWVzKSA6IF9kZWZhdWx0VmFsdWVzO1xuICAgICAgICBjb25zdCBjbG9uZVVwZGF0ZWRWYWx1ZXMgPSBjbG9uZU9iamVjdCh1cGRhdGVkVmFsdWVzKTtcbiAgICAgICAgY29uc3QgaXNFbXB0eVJlc2V0VmFsdWVzID0gaXNFbXB0eU9iamVjdChmb3JtVmFsdWVzKTtcbiAgICAgICAgY29uc3QgdmFsdWVzID0gaXNFbXB0eVJlc2V0VmFsdWVzID8gX2RlZmF1bHRWYWx1ZXMgOiBjbG9uZVVwZGF0ZWRWYWx1ZXM7XG4gICAgICAgIGlmICgha2VlcFN0YXRlT3B0aW9ucy5rZWVwRGVmYXVsdFZhbHVlcykge1xuICAgICAgICAgICAgX2RlZmF1bHRWYWx1ZXMgPSB1cGRhdGVkVmFsdWVzO1xuICAgICAgICB9XG4gICAgICAgIGlmICgha2VlcFN0YXRlT3B0aW9ucy5rZWVwVmFsdWVzKSB7XG4gICAgICAgICAgICBpZiAoa2VlcFN0YXRlT3B0aW9ucy5rZWVwRGlydHlWYWx1ZXMpIHtcbiAgICAgICAgICAgICAgICBjb25zdCBmaWVsZHNUb0NoZWNrID0gbmV3IFNldChbXG4gICAgICAgICAgICAgICAgICAgIC4uLl9uYW1lcy5tb3VudCxcbiAgICAgICAgICAgICAgICAgICAgLi4uT2JqZWN0LmtleXMoZ2V0RGlydHlGaWVsZHMoX2RlZmF1bHRWYWx1ZXMsIF9mb3JtVmFsdWVzKSksXG4gICAgICAgICAgICAgICAgXSk7XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCBmaWVsZE5hbWUgb2YgQXJyYXkuZnJvbShmaWVsZHNUb0NoZWNrKSkge1xuICAgICAgICAgICAgICAgICAgICBnZXQoX2Zvcm1TdGF0ZS5kaXJ0eUZpZWxkcywgZmllbGROYW1lKVxuICAgICAgICAgICAgICAgICAgICAgICAgPyBzZXQodmFsdWVzLCBmaWVsZE5hbWUsIGdldChfZm9ybVZhbHVlcywgZmllbGROYW1lKSlcbiAgICAgICAgICAgICAgICAgICAgICAgIDogc2V0VmFsdWUoZmllbGROYW1lLCBnZXQodmFsdWVzLCBmaWVsZE5hbWUpKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBpZiAoaXNXZWIgJiYgaXNVbmRlZmluZWQoZm9ybVZhbHVlcykpIHtcbiAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBuYW1lIG9mIF9uYW1lcy5tb3VudCkge1xuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoZmllbGQgJiYgZmllbGQuX2YpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBmaWVsZFJlZmVyZW5jZSA9IEFycmF5LmlzQXJyYXkoZmllbGQuX2YucmVmcylcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBmaWVsZC5fZi5yZWZzWzBdXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogZmllbGQuX2YucmVmO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChpc0hUTUxFbGVtZW50KGZpZWxkUmVmZXJlbmNlKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBmb3JtID0gZmllbGRSZWZlcmVuY2UuY2xvc2VzdCgnZm9ybScpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZm9ybSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9ybS5yZXNldCgpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZm9yIChjb25zdCBmaWVsZE5hbWUgb2YgX25hbWVzLm1vdW50KSB7XG4gICAgICAgICAgICAgICAgICAgIHNldFZhbHVlKGZpZWxkTmFtZSwgZ2V0KHZhbHVlcywgZmllbGROYW1lKSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgX2Zvcm1WYWx1ZXMgPSBjbG9uZU9iamVjdCh2YWx1ZXMpO1xuICAgICAgICAgICAgX3N1YmplY3RzLmFycmF5Lm5leHQoe1xuICAgICAgICAgICAgICAgIHZhbHVlczogeyAuLi52YWx1ZXMgfSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgIHZhbHVlczogeyAuLi52YWx1ZXMgfSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIF9uYW1lcyA9IHtcbiAgICAgICAgICAgIG1vdW50OiBrZWVwU3RhdGVPcHRpb25zLmtlZXBEaXJ0eVZhbHVlcyA/IF9uYW1lcy5tb3VudCA6IG5ldyBTZXQoKSxcbiAgICAgICAgICAgIHVuTW91bnQ6IG5ldyBTZXQoKSxcbiAgICAgICAgICAgIGFycmF5OiBuZXcgU2V0KCksXG4gICAgICAgICAgICBkaXNhYmxlZDogbmV3IFNldCgpLFxuICAgICAgICAgICAgd2F0Y2g6IG5ldyBTZXQoKSxcbiAgICAgICAgICAgIHdhdGNoQWxsOiBmYWxzZSxcbiAgICAgICAgICAgIGZvY3VzOiAnJyxcbiAgICAgICAgfTtcbiAgICAgICAgX3N0YXRlLm1vdW50ID1cbiAgICAgICAgICAgICFfcHJveHlGb3JtU3RhdGUuaXNWYWxpZCB8fFxuICAgICAgICAgICAgICAgICEha2VlcFN0YXRlT3B0aW9ucy5rZWVwSXNWYWxpZCB8fFxuICAgICAgICAgICAgICAgICEha2VlcFN0YXRlT3B0aW9ucy5rZWVwRGlydHlWYWx1ZXM7XG4gICAgICAgIF9zdGF0ZS53YXRjaCA9ICEhX29wdGlvbnMuc2hvdWxkVW5yZWdpc3RlcjtcbiAgICAgICAgX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgc3VibWl0Q291bnQ6IGtlZXBTdGF0ZU9wdGlvbnMua2VlcFN1Ym1pdENvdW50XG4gICAgICAgICAgICAgICAgPyBfZm9ybVN0YXRlLnN1Ym1pdENvdW50XG4gICAgICAgICAgICAgICAgOiAwLFxuICAgICAgICAgICAgaXNEaXJ0eTogaXNFbXB0eVJlc2V0VmFsdWVzXG4gICAgICAgICAgICAgICAgPyBmYWxzZVxuICAgICAgICAgICAgICAgIDoga2VlcFN0YXRlT3B0aW9ucy5rZWVwRGlydHlcbiAgICAgICAgICAgICAgICAgICAgPyBfZm9ybVN0YXRlLmlzRGlydHlcbiAgICAgICAgICAgICAgICAgICAgOiAhIShrZWVwU3RhdGVPcHRpb25zLmtlZXBEZWZhdWx0VmFsdWVzICYmXG4gICAgICAgICAgICAgICAgICAgICAgICAhZGVlcEVxdWFsKGZvcm1WYWx1ZXMsIF9kZWZhdWx0VmFsdWVzKSksXG4gICAgICAgICAgICBpc1N1Ym1pdHRlZDoga2VlcFN0YXRlT3B0aW9ucy5rZWVwSXNTdWJtaXR0ZWRcbiAgICAgICAgICAgICAgICA/IF9mb3JtU3RhdGUuaXNTdWJtaXR0ZWRcbiAgICAgICAgICAgICAgICA6IGZhbHNlLFxuICAgICAgICAgICAgZGlydHlGaWVsZHM6IGlzRW1wdHlSZXNldFZhbHVlc1xuICAgICAgICAgICAgICAgID8ge31cbiAgICAgICAgICAgICAgICA6IGtlZXBTdGF0ZU9wdGlvbnMua2VlcERpcnR5VmFsdWVzXG4gICAgICAgICAgICAgICAgICAgID8ga2VlcFN0YXRlT3B0aW9ucy5rZWVwRGVmYXVsdFZhbHVlcyAmJiBfZm9ybVZhbHVlc1xuICAgICAgICAgICAgICAgICAgICAgICAgPyBnZXREaXJ0eUZpZWxkcyhfZGVmYXVsdFZhbHVlcywgX2Zvcm1WYWx1ZXMpXG4gICAgICAgICAgICAgICAgICAgICAgICA6IF9mb3JtU3RhdGUuZGlydHlGaWVsZHNcbiAgICAgICAgICAgICAgICAgICAgOiBrZWVwU3RhdGVPcHRpb25zLmtlZXBEZWZhdWx0VmFsdWVzICYmIGZvcm1WYWx1ZXNcbiAgICAgICAgICAgICAgICAgICAgICAgID8gZ2V0RGlydHlGaWVsZHMoX2RlZmF1bHRWYWx1ZXMsIGZvcm1WYWx1ZXMpXG4gICAgICAgICAgICAgICAgICAgICAgICA6IGtlZXBTdGF0ZU9wdGlvbnMua2VlcERpcnR5XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBfZm9ybVN0YXRlLmRpcnR5RmllbGRzXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiB7fSxcbiAgICAgICAgICAgIHRvdWNoZWRGaWVsZHM6IGtlZXBTdGF0ZU9wdGlvbnMua2VlcFRvdWNoZWRcbiAgICAgICAgICAgICAgICA/IF9mb3JtU3RhdGUudG91Y2hlZEZpZWxkc1xuICAgICAgICAgICAgICAgIDoge30sXG4gICAgICAgICAgICBlcnJvcnM6IGtlZXBTdGF0ZU9wdGlvbnMua2VlcEVycm9ycyA/IF9mb3JtU3RhdGUuZXJyb3JzIDoge30sXG4gICAgICAgICAgICBpc1N1Ym1pdFN1Y2Nlc3NmdWw6IGtlZXBTdGF0ZU9wdGlvbnMua2VlcElzU3VibWl0U3VjY2Vzc2Z1bFxuICAgICAgICAgICAgICAgID8gX2Zvcm1TdGF0ZS5pc1N1Ym1pdFN1Y2Nlc3NmdWxcbiAgICAgICAgICAgICAgICA6IGZhbHNlLFxuICAgICAgICAgICAgaXNTdWJtaXR0aW5nOiBmYWxzZSxcbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICBjb25zdCByZXNldCA9IChmb3JtVmFsdWVzLCBrZWVwU3RhdGVPcHRpb25zKSA9PiBfcmVzZXQoaXNGdW5jdGlvbihmb3JtVmFsdWVzKVxuICAgICAgICA/IGZvcm1WYWx1ZXMoX2Zvcm1WYWx1ZXMpXG4gICAgICAgIDogZm9ybVZhbHVlcywga2VlcFN0YXRlT3B0aW9ucyk7XG4gICAgY29uc3Qgc2V0Rm9jdXMgPSAobmFtZSwgb3B0aW9ucyA9IHt9KSA9PiB7XG4gICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KF9maWVsZHMsIG5hbWUpO1xuICAgICAgICBjb25zdCBmaWVsZFJlZmVyZW5jZSA9IGZpZWxkICYmIGZpZWxkLl9mO1xuICAgICAgICBpZiAoZmllbGRSZWZlcmVuY2UpIHtcbiAgICAgICAgICAgIGNvbnN0IGZpZWxkUmVmID0gZmllbGRSZWZlcmVuY2UucmVmc1xuICAgICAgICAgICAgICAgID8gZmllbGRSZWZlcmVuY2UucmVmc1swXVxuICAgICAgICAgICAgICAgIDogZmllbGRSZWZlcmVuY2UucmVmO1xuICAgICAgICAgICAgaWYgKGZpZWxkUmVmLmZvY3VzKSB7XG4gICAgICAgICAgICAgICAgZmllbGRSZWYuZm9jdXMoKTtcbiAgICAgICAgICAgICAgICBvcHRpb25zLnNob3VsZFNlbGVjdCAmJlxuICAgICAgICAgICAgICAgICAgICBpc0Z1bmN0aW9uKGZpZWxkUmVmLnNlbGVjdCkgJiZcbiAgICAgICAgICAgICAgICAgICAgZmllbGRSZWYuc2VsZWN0KCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IF9zZXRGb3JtU3RhdGUgPSAodXBkYXRlZEZvcm1TdGF0ZSkgPT4ge1xuICAgICAgICBfZm9ybVN0YXRlID0ge1xuICAgICAgICAgICAgLi4uX2Zvcm1TdGF0ZSxcbiAgICAgICAgICAgIC4uLnVwZGF0ZWRGb3JtU3RhdGUsXG4gICAgICAgIH07XG4gICAgfTtcbiAgICBjb25zdCBfcmVzZXREZWZhdWx0VmFsdWVzID0gKCkgPT4gaXNGdW5jdGlvbihfb3B0aW9ucy5kZWZhdWx0VmFsdWVzKSAmJlxuICAgICAgICBfb3B0aW9ucy5kZWZhdWx0VmFsdWVzKCkudGhlbigodmFsdWVzKSA9PiB7XG4gICAgICAgICAgICByZXNldCh2YWx1ZXMsIF9vcHRpb25zLnJlc2V0T3B0aW9ucyk7XG4gICAgICAgICAgICBfc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgaXNMb2FkaW5nOiBmYWxzZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9KTtcbiAgICBjb25zdCBtZXRob2RzID0ge1xuICAgICAgICBjb250cm9sOiB7XG4gICAgICAgICAgICByZWdpc3RlcixcbiAgICAgICAgICAgIHVucmVnaXN0ZXIsXG4gICAgICAgICAgICBnZXRGaWVsZFN0YXRlLFxuICAgICAgICAgICAgaGFuZGxlU3VibWl0LFxuICAgICAgICAgICAgc2V0RXJyb3IsXG4gICAgICAgICAgICBfc3Vic2NyaWJlLFxuICAgICAgICAgICAgX3J1blNjaGVtYSxcbiAgICAgICAgICAgIF9nZXRXYXRjaCxcbiAgICAgICAgICAgIF9nZXREaXJ0eSxcbiAgICAgICAgICAgIF9zZXRWYWxpZCxcbiAgICAgICAgICAgIF9zZXRGaWVsZEFycmF5LFxuICAgICAgICAgICAgX3NldERpc2FibGVkRmllbGQsXG4gICAgICAgICAgICBfc2V0RXJyb3JzLFxuICAgICAgICAgICAgX2dldEZpZWxkQXJyYXksXG4gICAgICAgICAgICBfcmVzZXQsXG4gICAgICAgICAgICBfcmVzZXREZWZhdWx0VmFsdWVzLFxuICAgICAgICAgICAgX3JlbW92ZVVubW91bnRlZCxcbiAgICAgICAgICAgIF9kaXNhYmxlRm9ybSxcbiAgICAgICAgICAgIF9zdWJqZWN0cyxcbiAgICAgICAgICAgIF9wcm94eUZvcm1TdGF0ZSxcbiAgICAgICAgICAgIGdldCBfZmllbGRzKCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBfZmllbGRzO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGdldCBfZm9ybVZhbHVlcygpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gX2Zvcm1WYWx1ZXM7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZ2V0IF9zdGF0ZSgpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gX3N0YXRlO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHNldCBfc3RhdGUodmFsdWUpIHtcbiAgICAgICAgICAgICAgICBfc3RhdGUgPSB2YWx1ZTtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBnZXQgX2RlZmF1bHRWYWx1ZXMoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIF9kZWZhdWx0VmFsdWVzO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGdldCBfbmFtZXMoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIF9uYW1lcztcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBzZXQgX25hbWVzKHZhbHVlKSB7XG4gICAgICAgICAgICAgICAgX25hbWVzID0gdmFsdWU7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZ2V0IF9mb3JtU3RhdGUoKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIF9mb3JtU3RhdGU7XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgZ2V0IF9vcHRpb25zKCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBfb3B0aW9ucztcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBzZXQgX29wdGlvbnModmFsdWUpIHtcbiAgICAgICAgICAgICAgICBfb3B0aW9ucyA9IHtcbiAgICAgICAgICAgICAgICAgICAgLi4uX29wdGlvbnMsXG4gICAgICAgICAgICAgICAgICAgIC4uLnZhbHVlLFxuICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgICBzdWJzY3JpYmUsXG4gICAgICAgIHRyaWdnZXIsXG4gICAgICAgIHJlZ2lzdGVyLFxuICAgICAgICBoYW5kbGVTdWJtaXQsXG4gICAgICAgIHdhdGNoLFxuICAgICAgICBzZXRWYWx1ZSxcbiAgICAgICAgZ2V0VmFsdWVzLFxuICAgICAgICByZXNldCxcbiAgICAgICAgcmVzZXRGaWVsZCxcbiAgICAgICAgY2xlYXJFcnJvcnMsXG4gICAgICAgIHVucmVnaXN0ZXIsXG4gICAgICAgIHNldEVycm9yLFxuICAgICAgICBzZXRGb2N1cyxcbiAgICAgICAgZ2V0RmllbGRTdGF0ZSxcbiAgICB9O1xuICAgIHJldHVybiB7XG4gICAgICAgIC4uLm1ldGhvZHMsXG4gICAgICAgIGZvcm1Db250cm9sOiBtZXRob2RzLFxuICAgIH07XG59XG5cbnZhciBnZW5lcmF0ZUlkID0gKCkgPT4ge1xuICAgIGNvbnN0IGQgPSB0eXBlb2YgcGVyZm9ybWFuY2UgPT09ICd1bmRlZmluZWQnID8gRGF0ZS5ub3coKSA6IHBlcmZvcm1hbmNlLm5vdygpICogMTAwMDtcbiAgICByZXR1cm4gJ3h4eHh4eHh4LXh4eHgtNHh4eC15eHh4LXh4eHh4eHh4eHh4eCcucmVwbGFjZSgvW3h5XS9nLCAoYykgPT4ge1xuICAgICAgICBjb25zdCByID0gKE1hdGgucmFuZG9tKCkgKiAxNiArIGQpICUgMTYgfCAwO1xuICAgICAgICByZXR1cm4gKGMgPT0gJ3gnID8gciA6IChyICYgMHgzKSB8IDB4OCkudG9TdHJpbmcoMTYpO1xuICAgIH0pO1xufTtcblxudmFyIGdldEZvY3VzRmllbGROYW1lID0gKG5hbWUsIGluZGV4LCBvcHRpb25zID0ge30pID0+IG9wdGlvbnMuc2hvdWxkRm9jdXMgfHwgaXNVbmRlZmluZWQob3B0aW9ucy5zaG91bGRGb2N1cylcbiAgICA/IG9wdGlvbnMuZm9jdXNOYW1lIHx8XG4gICAgICAgIGAke25hbWV9LiR7aXNVbmRlZmluZWQob3B0aW9ucy5mb2N1c0luZGV4KSA/IGluZGV4IDogb3B0aW9ucy5mb2N1c0luZGV4fS5gXG4gICAgOiAnJztcblxudmFyIGFwcGVuZEF0ID0gKGRhdGEsIHZhbHVlKSA9PiBbXG4gICAgLi4uZGF0YSxcbiAgICAuLi5jb252ZXJ0VG9BcnJheVBheWxvYWQodmFsdWUpLFxuXTtcblxudmFyIGZpbGxFbXB0eUFycmF5ID0gKHZhbHVlKSA9PiBBcnJheS5pc0FycmF5KHZhbHVlKSA/IHZhbHVlLm1hcCgoKSA9PiB1bmRlZmluZWQpIDogdW5kZWZpbmVkO1xuXG5mdW5jdGlvbiBpbnNlcnQoZGF0YSwgaW5kZXgsIHZhbHVlKSB7XG4gICAgcmV0dXJuIFtcbiAgICAgICAgLi4uZGF0YS5zbGljZSgwLCBpbmRleCksXG4gICAgICAgIC4uLmNvbnZlcnRUb0FycmF5UGF5bG9hZCh2YWx1ZSksXG4gICAgICAgIC4uLmRhdGEuc2xpY2UoaW5kZXgpLFxuICAgIF07XG59XG5cbnZhciBtb3ZlQXJyYXlBdCA9IChkYXRhLCBmcm9tLCB0bykgPT4ge1xuICAgIGlmICghQXJyYXkuaXNBcnJheShkYXRhKSkge1xuICAgICAgICByZXR1cm4gW107XG4gICAgfVxuICAgIGlmIChpc1VuZGVmaW5lZChkYXRhW3RvXSkpIHtcbiAgICAgICAgZGF0YVt0b10gPSB1bmRlZmluZWQ7XG4gICAgfVxuICAgIGRhdGEuc3BsaWNlKHRvLCAwLCBkYXRhLnNwbGljZShmcm9tLCAxKVswXSk7XG4gICAgcmV0dXJuIGRhdGE7XG59O1xuXG52YXIgcHJlcGVuZEF0ID0gKGRhdGEsIHZhbHVlKSA9PiBbXG4gICAgLi4uY29udmVydFRvQXJyYXlQYXlsb2FkKHZhbHVlKSxcbiAgICAuLi5jb252ZXJ0VG9BcnJheVBheWxvYWQoZGF0YSksXG5dO1xuXG5mdW5jdGlvbiByZW1vdmVBdEluZGV4ZXMoZGF0YSwgaW5kZXhlcykge1xuICAgIGxldCBpID0gMDtcbiAgICBjb25zdCB0ZW1wID0gWy4uLmRhdGFdO1xuICAgIGZvciAoY29uc3QgaW5kZXggb2YgaW5kZXhlcykge1xuICAgICAgICB0ZW1wLnNwbGljZShpbmRleCAtIGksIDEpO1xuICAgICAgICBpKys7XG4gICAgfVxuICAgIHJldHVybiBjb21wYWN0KHRlbXApLmxlbmd0aCA/IHRlbXAgOiBbXTtcbn1cbnZhciByZW1vdmVBcnJheUF0ID0gKGRhdGEsIGluZGV4KSA9PiBpc1VuZGVmaW5lZChpbmRleClcbiAgICA/IFtdXG4gICAgOiByZW1vdmVBdEluZGV4ZXMoZGF0YSwgY29udmVydFRvQXJyYXlQYXlsb2FkKGluZGV4KS5zb3J0KChhLCBiKSA9PiBhIC0gYikpO1xuXG52YXIgc3dhcEFycmF5QXQgPSAoZGF0YSwgaW5kZXhBLCBpbmRleEIpID0+IHtcbiAgICBbZGF0YVtpbmRleEFdLCBkYXRhW2luZGV4Ql1dID0gW2RhdGFbaW5kZXhCXSwgZGF0YVtpbmRleEFdXTtcbn07XG5cbnZhciB1cGRhdGVBdCA9IChmaWVsZFZhbHVlcywgaW5kZXgsIHZhbHVlKSA9PiB7XG4gICAgZmllbGRWYWx1ZXNbaW5kZXhdID0gdmFsdWU7XG4gICAgcmV0dXJuIGZpZWxkVmFsdWVzO1xufTtcblxuLyoqXG4gKiBBIGN1c3RvbSBob29rIHRoYXQgZXhwb3NlcyBjb252ZW5pZW50IG1ldGhvZHMgdG8gcGVyZm9ybSBvcGVyYXRpb25zIHdpdGggYSBsaXN0IG9mIGR5bmFtaWMgaW5wdXRzIHRoYXQgbmVlZCB0byBiZSBhcHBlbmRlZCwgdXBkYXRlZCwgcmVtb3ZlZCBldGMuIOKAoiBbRGVtb10oaHR0cHM6Ly9jb2Rlc2FuZGJveC5pby9zL3JlYWN0LWhvb2stZm9ybS11c2VmaWVsZGFycmF5LXNzdWduKSDigKIgW1ZpZGVvXShodHRwczovL3lvdXR1LmJlLzRNcmJmR1NGWTJBKVxuICpcbiAqIEByZW1hcmtzXG4gKiBbQVBJXShodHRwczovL3JlYWN0LWhvb2stZm9ybS5jb20vZG9jcy91c2VmaWVsZGFycmF5KSDigKIgW0RlbW9dKGh0dHBzOi8vY29kZXNhbmRib3guaW8vcy9yZWFjdC1ob29rLWZvcm0tdXNlZmllbGRhcnJheS1zc3VnbilcbiAqXG4gKiBAcGFyYW0gcHJvcHMgLSB1c2VGaWVsZEFycmF5IHByb3BzXG4gKlxuICogQHJldHVybnMgbWV0aG9kcyAtIGZ1bmN0aW9ucyB0byBtYW5pcHVsYXRlIHdpdGggdGhlIEZpZWxkIEFycmF5cyAoZHluYW1pYyBpbnB1dHMpIHtAbGluayBVc2VGaWVsZEFycmF5UmV0dXJufVxuICpcbiAqIEBleGFtcGxlXG4gKiBgYGB0c3hcbiAqIGZ1bmN0aW9uIEFwcCgpIHtcbiAqICAgY29uc3QgeyByZWdpc3RlciwgY29udHJvbCwgaGFuZGxlU3VibWl0LCByZXNldCwgdHJpZ2dlciwgc2V0RXJyb3IgfSA9IHVzZUZvcm0oe1xuICogICAgIGRlZmF1bHRWYWx1ZXM6IHtcbiAqICAgICAgIHRlc3Q6IFtdXG4gKiAgICAgfVxuICogICB9KTtcbiAqICAgY29uc3QgeyBmaWVsZHMsIGFwcGVuZCB9ID0gdXNlRmllbGRBcnJheSh7XG4gKiAgICAgY29udHJvbCxcbiAqICAgICBuYW1lOiBcInRlc3RcIlxuICogICB9KTtcbiAqXG4gKiAgIHJldHVybiAoXG4gKiAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdChkYXRhID0+IGNvbnNvbGUubG9nKGRhdGEpKX0+XG4gKiAgICAgICB7ZmllbGRzLm1hcCgoaXRlbSwgaW5kZXgpID0+IChcbiAqICAgICAgICAgIDxpbnB1dCBrZXk9e2l0ZW0uaWR9IHsuLi5yZWdpc3RlcihgdGVzdC4ke2luZGV4fS5maXJzdE5hbWVgKX0gIC8+XG4gKiAgICAgICApKX1cbiAqICAgICAgIDxidXR0b24gdHlwZT1cImJ1dHRvblwiIG9uQ2xpY2s9eygpID0+IGFwcGVuZCh7IGZpcnN0TmFtZTogXCJiaWxsXCIgfSl9PlxuICogICAgICAgICBhcHBlbmRcbiAqICAgICAgIDwvYnV0dG9uPlxuICogICAgICAgPGlucHV0IHR5cGU9XCJzdWJtaXRcIiAvPlxuICogICAgIDwvZm9ybT5cbiAqICAgKTtcbiAqIH1cbiAqIGBgYFxuICovXG5mdW5jdGlvbiB1c2VGaWVsZEFycmF5KHByb3BzKSB7XG4gICAgY29uc3QgbWV0aG9kcyA9IHVzZUZvcm1Db250ZXh0KCk7XG4gICAgY29uc3QgeyBjb250cm9sID0gbWV0aG9kcy5jb250cm9sLCBuYW1lLCBrZXlOYW1lID0gJ2lkJywgc2hvdWxkVW5yZWdpc3RlciwgcnVsZXMsIH0gPSBwcm9wcztcbiAgICBjb25zdCBbZmllbGRzLCBzZXRGaWVsZHNdID0gUmVhY3RfX2RlZmF1bHQudXNlU3RhdGUoY29udHJvbC5fZ2V0RmllbGRBcnJheShuYW1lKSk7XG4gICAgY29uc3QgaWRzID0gUmVhY3RfX2RlZmF1bHQudXNlUmVmKGNvbnRyb2wuX2dldEZpZWxkQXJyYXkobmFtZSkubWFwKGdlbmVyYXRlSWQpKTtcbiAgICBjb25zdCBfZmllbGRJZHMgPSBSZWFjdF9fZGVmYXVsdC51c2VSZWYoZmllbGRzKTtcbiAgICBjb25zdCBfbmFtZSA9IFJlYWN0X19kZWZhdWx0LnVzZVJlZihuYW1lKTtcbiAgICBjb25zdCBfYWN0aW9uZWQgPSBSZWFjdF9fZGVmYXVsdC51c2VSZWYoZmFsc2UpO1xuICAgIF9uYW1lLmN1cnJlbnQgPSBuYW1lO1xuICAgIF9maWVsZElkcy5jdXJyZW50ID0gZmllbGRzO1xuICAgIGNvbnRyb2wuX25hbWVzLmFycmF5LmFkZChuYW1lKTtcbiAgICBydWxlcyAmJlxuICAgICAgICBjb250cm9sLnJlZ2lzdGVyKG5hbWUsIHJ1bGVzKTtcbiAgICBSZWFjdF9fZGVmYXVsdC51c2VFZmZlY3QoKCkgPT4gY29udHJvbC5fc3ViamVjdHMuYXJyYXkuc3Vic2NyaWJlKHtcbiAgICAgICAgbmV4dDogKHsgdmFsdWVzLCBuYW1lOiBmaWVsZEFycmF5TmFtZSwgfSkgPT4ge1xuICAgICAgICAgICAgaWYgKGZpZWxkQXJyYXlOYW1lID09PSBfbmFtZS5jdXJyZW50IHx8ICFmaWVsZEFycmF5TmFtZSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkVmFsdWVzID0gZ2V0KHZhbHVlcywgX25hbWUuY3VycmVudCk7XG4gICAgICAgICAgICAgICAgaWYgKEFycmF5LmlzQXJyYXkoZmllbGRWYWx1ZXMpKSB7XG4gICAgICAgICAgICAgICAgICAgIHNldEZpZWxkcyhmaWVsZFZhbHVlcyk7XG4gICAgICAgICAgICAgICAgICAgIGlkcy5jdXJyZW50ID0gZmllbGRWYWx1ZXMubWFwKGdlbmVyYXRlSWQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfSxcbiAgICB9KS51bnN1YnNjcmliZSwgW2NvbnRyb2xdKTtcbiAgICBjb25zdCB1cGRhdGVWYWx1ZXMgPSBSZWFjdF9fZGVmYXVsdC51c2VDYWxsYmFjaygodXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMpID0+IHtcbiAgICAgICAgX2FjdGlvbmVkLmN1cnJlbnQgPSB0cnVlO1xuICAgICAgICBjb250cm9sLl9zZXRGaWVsZEFycmF5KG5hbWUsIHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICB9LCBbY29udHJvbCwgbmFtZV0pO1xuICAgIGNvbnN0IGFwcGVuZCA9ICh2YWx1ZSwgb3B0aW9ucykgPT4ge1xuICAgICAgICBjb25zdCBhcHBlbmRWYWx1ZSA9IGNvbnZlcnRUb0FycmF5UGF5bG9hZChjbG9uZU9iamVjdCh2YWx1ZSkpO1xuICAgICAgICBjb25zdCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcyA9IGFwcGVuZEF0KGNvbnRyb2wuX2dldEZpZWxkQXJyYXkobmFtZSksIGFwcGVuZFZhbHVlKTtcbiAgICAgICAgY29udHJvbC5fbmFtZXMuZm9jdXMgPSBnZXRGb2N1c0ZpZWxkTmFtZShuYW1lLCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcy5sZW5ndGggLSAxLCBvcHRpb25zKTtcbiAgICAgICAgaWRzLmN1cnJlbnQgPSBhcHBlbmRBdChpZHMuY3VycmVudCwgYXBwZW5kVmFsdWUubWFwKGdlbmVyYXRlSWQpKTtcbiAgICAgICAgdXBkYXRlVmFsdWVzKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICAgICAgc2V0RmllbGRzKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICAgICAgY29udHJvbC5fc2V0RmllbGRBcnJheShuYW1lLCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcywgYXBwZW5kQXQsIHtcbiAgICAgICAgICAgIGFyZ0E6IGZpbGxFbXB0eUFycmF5KHZhbHVlKSxcbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICBjb25zdCBwcmVwZW5kID0gKHZhbHVlLCBvcHRpb25zKSA9PiB7XG4gICAgICAgIGNvbnN0IHByZXBlbmRWYWx1ZSA9IGNvbnZlcnRUb0FycmF5UGF5bG9hZChjbG9uZU9iamVjdCh2YWx1ZSkpO1xuICAgICAgICBjb25zdCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcyA9IHByZXBlbmRBdChjb250cm9sLl9nZXRGaWVsZEFycmF5KG5hbWUpLCBwcmVwZW5kVmFsdWUpO1xuICAgICAgICBjb250cm9sLl9uYW1lcy5mb2N1cyA9IGdldEZvY3VzRmllbGROYW1lKG5hbWUsIDAsIG9wdGlvbnMpO1xuICAgICAgICBpZHMuY3VycmVudCA9IHByZXBlbmRBdChpZHMuY3VycmVudCwgcHJlcGVuZFZhbHVlLm1hcChnZW5lcmF0ZUlkKSk7XG4gICAgICAgIHVwZGF0ZVZhbHVlcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIHNldEZpZWxkcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIGNvbnRyb2wuX3NldEZpZWxkQXJyYXkobmFtZSwgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMsIHByZXBlbmRBdCwge1xuICAgICAgICAgICAgYXJnQTogZmlsbEVtcHR5QXJyYXkodmFsdWUpLFxuICAgICAgICB9KTtcbiAgICB9O1xuICAgIGNvbnN0IHJlbW92ZSA9IChpbmRleCkgPT4ge1xuICAgICAgICBjb25zdCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcyA9IHJlbW92ZUFycmF5QXQoY29udHJvbC5fZ2V0RmllbGRBcnJheShuYW1lKSwgaW5kZXgpO1xuICAgICAgICBpZHMuY3VycmVudCA9IHJlbW92ZUFycmF5QXQoaWRzLmN1cnJlbnQsIGluZGV4KTtcbiAgICAgICAgdXBkYXRlVmFsdWVzKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICAgICAgc2V0RmllbGRzKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICAgICAgIUFycmF5LmlzQXJyYXkoZ2V0KGNvbnRyb2wuX2ZpZWxkcywgbmFtZSkpICYmXG4gICAgICAgICAgICBzZXQoY29udHJvbC5fZmllbGRzLCBuYW1lLCB1bmRlZmluZWQpO1xuICAgICAgICBjb250cm9sLl9zZXRGaWVsZEFycmF5KG5hbWUsIHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzLCByZW1vdmVBcnJheUF0LCB7XG4gICAgICAgICAgICBhcmdBOiBpbmRleCxcbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICBjb25zdCBpbnNlcnQkMSA9IChpbmRleCwgdmFsdWUsIG9wdGlvbnMpID0+IHtcbiAgICAgICAgY29uc3QgaW5zZXJ0VmFsdWUgPSBjb252ZXJ0VG9BcnJheVBheWxvYWQoY2xvbmVPYmplY3QodmFsdWUpKTtcbiAgICAgICAgY29uc3QgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMgPSBpbnNlcnQoY29udHJvbC5fZ2V0RmllbGRBcnJheShuYW1lKSwgaW5kZXgsIGluc2VydFZhbHVlKTtcbiAgICAgICAgY29udHJvbC5fbmFtZXMuZm9jdXMgPSBnZXRGb2N1c0ZpZWxkTmFtZShuYW1lLCBpbmRleCwgb3B0aW9ucyk7XG4gICAgICAgIGlkcy5jdXJyZW50ID0gaW5zZXJ0KGlkcy5jdXJyZW50LCBpbmRleCwgaW5zZXJ0VmFsdWUubWFwKGdlbmVyYXRlSWQpKTtcbiAgICAgICAgdXBkYXRlVmFsdWVzKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICAgICAgc2V0RmllbGRzKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICAgICAgY29udHJvbC5fc2V0RmllbGRBcnJheShuYW1lLCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcywgaW5zZXJ0LCB7XG4gICAgICAgICAgICBhcmdBOiBpbmRleCxcbiAgICAgICAgICAgIGFyZ0I6IGZpbGxFbXB0eUFycmF5KHZhbHVlKSxcbiAgICAgICAgfSk7XG4gICAgfTtcbiAgICBjb25zdCBzd2FwID0gKGluZGV4QSwgaW5kZXhCKSA9PiB7XG4gICAgICAgIGNvbnN0IHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzID0gY29udHJvbC5fZ2V0RmllbGRBcnJheShuYW1lKTtcbiAgICAgICAgc3dhcEFycmF5QXQodXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMsIGluZGV4QSwgaW5kZXhCKTtcbiAgICAgICAgc3dhcEFycmF5QXQoaWRzLmN1cnJlbnQsIGluZGV4QSwgaW5kZXhCKTtcbiAgICAgICAgdXBkYXRlVmFsdWVzKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICAgICAgc2V0RmllbGRzKHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzKTtcbiAgICAgICAgY29udHJvbC5fc2V0RmllbGRBcnJheShuYW1lLCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcywgc3dhcEFycmF5QXQsIHtcbiAgICAgICAgICAgIGFyZ0E6IGluZGV4QSxcbiAgICAgICAgICAgIGFyZ0I6IGluZGV4QixcbiAgICAgICAgfSwgZmFsc2UpO1xuICAgIH07XG4gICAgY29uc3QgbW92ZSA9IChmcm9tLCB0bykgPT4ge1xuICAgICAgICBjb25zdCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcyA9IGNvbnRyb2wuX2dldEZpZWxkQXJyYXkobmFtZSk7XG4gICAgICAgIG1vdmVBcnJheUF0KHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzLCBmcm9tLCB0byk7XG4gICAgICAgIG1vdmVBcnJheUF0KGlkcy5jdXJyZW50LCBmcm9tLCB0byk7XG4gICAgICAgIHVwZGF0ZVZhbHVlcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIHNldEZpZWxkcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIGNvbnRyb2wuX3NldEZpZWxkQXJyYXkobmFtZSwgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMsIG1vdmVBcnJheUF0LCB7XG4gICAgICAgICAgICBhcmdBOiBmcm9tLFxuICAgICAgICAgICAgYXJnQjogdG8sXG4gICAgICAgIH0sIGZhbHNlKTtcbiAgICB9O1xuICAgIGNvbnN0IHVwZGF0ZSA9IChpbmRleCwgdmFsdWUpID0+IHtcbiAgICAgICAgY29uc3QgdXBkYXRlVmFsdWUgPSBjbG9uZU9iamVjdCh2YWx1ZSk7XG4gICAgICAgIGNvbnN0IHVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzID0gdXBkYXRlQXQoY29udHJvbC5fZ2V0RmllbGRBcnJheShuYW1lKSwgaW5kZXgsIHVwZGF0ZVZhbHVlKTtcbiAgICAgICAgaWRzLmN1cnJlbnQgPSBbLi4udXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXNdLm1hcCgoaXRlbSwgaSkgPT4gIWl0ZW0gfHwgaSA9PT0gaW5kZXggPyBnZW5lcmF0ZUlkKCkgOiBpZHMuY3VycmVudFtpXSk7XG4gICAgICAgIHVwZGF0ZVZhbHVlcyh1cGRhdGVkRmllbGRBcnJheVZhbHVlcyk7XG4gICAgICAgIHNldEZpZWxkcyhbLi4udXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXNdKTtcbiAgICAgICAgY29udHJvbC5fc2V0RmllbGRBcnJheShuYW1lLCB1cGRhdGVkRmllbGRBcnJheVZhbHVlcywgdXBkYXRlQXQsIHtcbiAgICAgICAgICAgIGFyZ0E6IGluZGV4LFxuICAgICAgICAgICAgYXJnQjogdXBkYXRlVmFsdWUsXG4gICAgICAgIH0sIHRydWUsIGZhbHNlKTtcbiAgICB9O1xuICAgIGNvbnN0IHJlcGxhY2UgPSAodmFsdWUpID0+IHtcbiAgICAgICAgY29uc3QgdXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXMgPSBjb252ZXJ0VG9BcnJheVBheWxvYWQoY2xvbmVPYmplY3QodmFsdWUpKTtcbiAgICAgICAgaWRzLmN1cnJlbnQgPSB1cGRhdGVkRmllbGRBcnJheVZhbHVlcy5tYXAoZ2VuZXJhdGVJZCk7XG4gICAgICAgIHVwZGF0ZVZhbHVlcyhbLi4udXBkYXRlZEZpZWxkQXJyYXlWYWx1ZXNdKTtcbiAgICAgICAgc2V0RmllbGRzKFsuLi51cGRhdGVkRmllbGRBcnJheVZhbHVlc10pO1xuICAgICAgICBjb250cm9sLl9zZXRGaWVsZEFycmF5KG5hbWUsIFsuLi51cGRhdGVkRmllbGRBcnJheVZhbHVlc10sIChkYXRhKSA9PiBkYXRhLCB7fSwgdHJ1ZSwgZmFsc2UpO1xuICAgIH07XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgY29udHJvbC5fc3RhdGUuYWN0aW9uID0gZmFsc2U7XG4gICAgICAgIGlzV2F0Y2hlZChuYW1lLCBjb250cm9sLl9uYW1lcykgJiZcbiAgICAgICAgICAgIGNvbnRyb2wuX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgIC4uLmNvbnRyb2wuX2Zvcm1TdGF0ZSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICBpZiAoX2FjdGlvbmVkLmN1cnJlbnQgJiZcbiAgICAgICAgICAgICghZ2V0VmFsaWRhdGlvbk1vZGVzKGNvbnRyb2wuX29wdGlvbnMubW9kZSkuaXNPblN1Ym1pdCB8fFxuICAgICAgICAgICAgICAgIGNvbnRyb2wuX2Zvcm1TdGF0ZS5pc1N1Ym1pdHRlZCkgJiZcbiAgICAgICAgICAgICFnZXRWYWxpZGF0aW9uTW9kZXMoY29udHJvbC5fb3B0aW9ucy5yZVZhbGlkYXRlTW9kZSkuaXNPblN1Ym1pdCkge1xuICAgICAgICAgICAgaWYgKGNvbnRyb2wuX29wdGlvbnMucmVzb2x2ZXIpIHtcbiAgICAgICAgICAgICAgICBjb250cm9sLl9ydW5TY2hlbWEoW25hbWVdKS50aGVuKChyZXN1bHQpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgZXJyb3IgPSBnZXQocmVzdWx0LmVycm9ycywgbmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGV4aXN0aW5nRXJyb3IgPSBnZXQoY29udHJvbC5fZm9ybVN0YXRlLmVycm9ycywgbmFtZSk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChleGlzdGluZ0Vycm9yXG4gICAgICAgICAgICAgICAgICAgICAgICA/ICghZXJyb3IgJiYgZXhpc3RpbmdFcnJvci50eXBlKSB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIChlcnJvciAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAoZXhpc3RpbmdFcnJvci50eXBlICE9PSBlcnJvci50eXBlIHx8XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBleGlzdGluZ0Vycm9yLm1lc3NhZ2UgIT09IGVycm9yLm1lc3NhZ2UpKVxuICAgICAgICAgICAgICAgICAgICAgICAgOiBlcnJvciAmJiBlcnJvci50eXBlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBlcnJvclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gc2V0KGNvbnRyb2wuX2Zvcm1TdGF0ZS5lcnJvcnMsIG5hbWUsIGVycm9yKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogdW5zZXQoY29udHJvbC5fZm9ybVN0YXRlLmVycm9ycywgbmFtZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb250cm9sLl9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcnJvcnM6IGNvbnRyb2wuX2Zvcm1TdGF0ZS5lcnJvcnMsXG4gICAgICAgICAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZmllbGQgPSBnZXQoY29udHJvbC5fZmllbGRzLCBuYW1lKTtcbiAgICAgICAgICAgICAgICBpZiAoZmllbGQgJiZcbiAgICAgICAgICAgICAgICAgICAgZmllbGQuX2YgJiZcbiAgICAgICAgICAgICAgICAgICAgIShnZXRWYWxpZGF0aW9uTW9kZXMoY29udHJvbC5fb3B0aW9ucy5yZVZhbGlkYXRlTW9kZSkuaXNPblN1Ym1pdCAmJlxuICAgICAgICAgICAgICAgICAgICAgICAgZ2V0VmFsaWRhdGlvbk1vZGVzKGNvbnRyb2wuX29wdGlvbnMubW9kZSkuaXNPblN1Ym1pdCkpIHtcbiAgICAgICAgICAgICAgICAgICAgdmFsaWRhdGVGaWVsZChmaWVsZCwgY29udHJvbC5fbmFtZXMuZGlzYWJsZWQsIGNvbnRyb2wuX2Zvcm1WYWx1ZXMsIGNvbnRyb2wuX29wdGlvbnMuY3JpdGVyaWFNb2RlID09PSBWQUxJREFUSU9OX01PREUuYWxsLCBjb250cm9sLl9vcHRpb25zLnNob3VsZFVzZU5hdGl2ZVZhbGlkYXRpb24sIHRydWUpLnRoZW4oKGVycm9yKSA9PiAhaXNFbXB0eU9iamVjdChlcnJvcikgJiZcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnRyb2wuX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVycm9yczogdXBkYXRlRmllbGRBcnJheVJvb3RFcnJvcihjb250cm9sLl9mb3JtU3RhdGUuZXJyb3JzLCBlcnJvciwgbmFtZSksXG4gICAgICAgICAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGNvbnRyb2wuX3N1YmplY3RzLnN0YXRlLm5leHQoe1xuICAgICAgICAgICAgbmFtZSxcbiAgICAgICAgICAgIHZhbHVlczogY2xvbmVPYmplY3QoY29udHJvbC5fZm9ybVZhbHVlcyksXG4gICAgICAgIH0pO1xuICAgICAgICBjb250cm9sLl9uYW1lcy5mb2N1cyAmJlxuICAgICAgICAgICAgaXRlcmF0ZUZpZWxkc0J5QWN0aW9uKGNvbnRyb2wuX2ZpZWxkcywgKHJlZiwga2V5KSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKGNvbnRyb2wuX25hbWVzLmZvY3VzICYmXG4gICAgICAgICAgICAgICAgICAgIGtleS5zdGFydHNXaXRoKGNvbnRyb2wuX25hbWVzLmZvY3VzKSAmJlxuICAgICAgICAgICAgICAgICAgICByZWYuZm9jdXMpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVmLmZvY3VzKCk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiAxO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgY29udHJvbC5fbmFtZXMuZm9jdXMgPSAnJztcbiAgICAgICAgY29udHJvbC5fc2V0VmFsaWQoKTtcbiAgICAgICAgX2FjdGlvbmVkLmN1cnJlbnQgPSBmYWxzZTtcbiAgICB9LCBbZmllbGRzLCBuYW1lLCBjb250cm9sXSk7XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgIWdldChjb250cm9sLl9mb3JtVmFsdWVzLCBuYW1lKSAmJiBjb250cm9sLl9zZXRGaWVsZEFycmF5KG5hbWUpO1xuICAgICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICAgICAgY29uc3QgdXBkYXRlTW91bnRlZCA9IChuYW1lLCB2YWx1ZSkgPT4ge1xuICAgICAgICAgICAgICAgIGNvbnN0IGZpZWxkID0gZ2V0KGNvbnRyb2wuX2ZpZWxkcywgbmFtZSk7XG4gICAgICAgICAgICAgICAgaWYgKGZpZWxkICYmIGZpZWxkLl9mKSB7XG4gICAgICAgICAgICAgICAgICAgIGZpZWxkLl9mLm1vdW50ID0gdmFsdWU7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGNvbnRyb2wuX29wdGlvbnMuc2hvdWxkVW5yZWdpc3RlciB8fCBzaG91bGRVbnJlZ2lzdGVyXG4gICAgICAgICAgICAgICAgPyBjb250cm9sLnVucmVnaXN0ZXIobmFtZSlcbiAgICAgICAgICAgICAgICA6IHVwZGF0ZU1vdW50ZWQobmFtZSwgZmFsc2UpO1xuICAgICAgICB9O1xuICAgIH0sIFtuYW1lLCBjb250cm9sLCBrZXlOYW1lLCBzaG91bGRVbnJlZ2lzdGVyXSk7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgc3dhcDogUmVhY3RfX2RlZmF1bHQudXNlQ2FsbGJhY2soc3dhcCwgW3VwZGF0ZVZhbHVlcywgbmFtZSwgY29udHJvbF0pLFxuICAgICAgICBtb3ZlOiBSZWFjdF9fZGVmYXVsdC51c2VDYWxsYmFjayhtb3ZlLCBbdXBkYXRlVmFsdWVzLCBuYW1lLCBjb250cm9sXSksXG4gICAgICAgIHByZXBlbmQ6IFJlYWN0X19kZWZhdWx0LnVzZUNhbGxiYWNrKHByZXBlbmQsIFt1cGRhdGVWYWx1ZXMsIG5hbWUsIGNvbnRyb2xdKSxcbiAgICAgICAgYXBwZW5kOiBSZWFjdF9fZGVmYXVsdC51c2VDYWxsYmFjayhhcHBlbmQsIFt1cGRhdGVWYWx1ZXMsIG5hbWUsIGNvbnRyb2xdKSxcbiAgICAgICAgcmVtb3ZlOiBSZWFjdF9fZGVmYXVsdC51c2VDYWxsYmFjayhyZW1vdmUsIFt1cGRhdGVWYWx1ZXMsIG5hbWUsIGNvbnRyb2xdKSxcbiAgICAgICAgaW5zZXJ0OiBSZWFjdF9fZGVmYXVsdC51c2VDYWxsYmFjayhpbnNlcnQkMSwgW3VwZGF0ZVZhbHVlcywgbmFtZSwgY29udHJvbF0pLFxuICAgICAgICB1cGRhdGU6IFJlYWN0X19kZWZhdWx0LnVzZUNhbGxiYWNrKHVwZGF0ZSwgW3VwZGF0ZVZhbHVlcywgbmFtZSwgY29udHJvbF0pLFxuICAgICAgICByZXBsYWNlOiBSZWFjdF9fZGVmYXVsdC51c2VDYWxsYmFjayhyZXBsYWNlLCBbdXBkYXRlVmFsdWVzLCBuYW1lLCBjb250cm9sXSksXG4gICAgICAgIGZpZWxkczogUmVhY3RfX2RlZmF1bHQudXNlTWVtbygoKSA9PiBmaWVsZHMubWFwKChmaWVsZCwgaW5kZXgpID0+ICh7XG4gICAgICAgICAgICAuLi5maWVsZCxcbiAgICAgICAgICAgIFtrZXlOYW1lXTogaWRzLmN1cnJlbnRbaW5kZXhdIHx8IGdlbmVyYXRlSWQoKSxcbiAgICAgICAgfSkpLCBbZmllbGRzLCBrZXlOYW1lXSksXG4gICAgfTtcbn1cblxuLyoqXG4gKiBDdXN0b20gaG9vayB0byBtYW5hZ2UgdGhlIGVudGlyZSBmb3JtLlxuICpcbiAqIEByZW1hcmtzXG4gKiBbQVBJXShodHRwczovL3JlYWN0LWhvb2stZm9ybS5jb20vZG9jcy91c2Vmb3JtKSDigKIgW0RlbW9dKGh0dHBzOi8vY29kZXNhbmRib3guaW8vcy9yZWFjdC1ob29rLWZvcm0tZ2V0LXN0YXJ0ZWQtdHMtNWtzbW0pIOKAoiBbVmlkZW9dKGh0dHBzOi8vd3d3LnlvdXR1YmUuY29tL3dhdGNoP3Y9UmtYdjRBWFhDXzQpXG4gKlxuICogQHBhcmFtIHByb3BzIC0gZm9ybSBjb25maWd1cmF0aW9uIGFuZCB2YWxpZGF0aW9uIHBhcmFtZXRlcnMuXG4gKlxuICogQHJldHVybnMgbWV0aG9kcyAtIGluZGl2aWR1YWwgZnVuY3Rpb25zIHRvIG1hbmFnZSB0aGUgZm9ybSBzdGF0ZS4ge0BsaW5rIFVzZUZvcm1SZXR1cm59XG4gKlxuICogQGV4YW1wbGVcbiAqIGBgYHRzeFxuICogZnVuY3Rpb24gQXBwKCkge1xuICogICBjb25zdCB7IHJlZ2lzdGVyLCBoYW5kbGVTdWJtaXQsIHdhdGNoLCBmb3JtU3RhdGU6IHsgZXJyb3JzIH0gfSA9IHVzZUZvcm0oKTtcbiAqICAgY29uc3Qgb25TdWJtaXQgPSBkYXRhID0+IGNvbnNvbGUubG9nKGRhdGEpO1xuICpcbiAqICAgY29uc29sZS5sb2cod2F0Y2goXCJleGFtcGxlXCIpKTtcbiAqXG4gKiAgIHJldHVybiAoXG4gKiAgICAgPGZvcm0gb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdChvblN1Ym1pdCl9PlxuICogICAgICAgPGlucHV0IGRlZmF1bHRWYWx1ZT1cInRlc3RcIiB7Li4ucmVnaXN0ZXIoXCJleGFtcGxlXCIpfSAvPlxuICogICAgICAgPGlucHV0IHsuLi5yZWdpc3RlcihcImV4YW1wbGVSZXF1aXJlZFwiLCB7IHJlcXVpcmVkOiB0cnVlIH0pfSAvPlxuICogICAgICAge2Vycm9ycy5leGFtcGxlUmVxdWlyZWQgJiYgPHNwYW4+VGhpcyBmaWVsZCBpcyByZXF1aXJlZDwvc3Bhbj59XG4gKiAgICAgICA8YnV0dG9uPlN1Ym1pdDwvYnV0dG9uPlxuICogICAgIDwvZm9ybT5cbiAqICAgKTtcbiAqIH1cbiAqIGBgYFxuICovXG5mdW5jdGlvbiB1c2VGb3JtKHByb3BzID0ge30pIHtcbiAgICBjb25zdCBfZm9ybUNvbnRyb2wgPSBSZWFjdF9fZGVmYXVsdC51c2VSZWYodW5kZWZpbmVkKTtcbiAgICBjb25zdCBfdmFsdWVzID0gUmVhY3RfX2RlZmF1bHQudXNlUmVmKHVuZGVmaW5lZCk7XG4gICAgY29uc3QgW2Zvcm1TdGF0ZSwgdXBkYXRlRm9ybVN0YXRlXSA9IFJlYWN0X19kZWZhdWx0LnVzZVN0YXRlKHtcbiAgICAgICAgaXNEaXJ0eTogZmFsc2UsXG4gICAgICAgIGlzVmFsaWRhdGluZzogZmFsc2UsXG4gICAgICAgIGlzTG9hZGluZzogaXNGdW5jdGlvbihwcm9wcy5kZWZhdWx0VmFsdWVzKSxcbiAgICAgICAgaXNTdWJtaXR0ZWQ6IGZhbHNlLFxuICAgICAgICBpc1N1Ym1pdHRpbmc6IGZhbHNlLFxuICAgICAgICBpc1N1Ym1pdFN1Y2Nlc3NmdWw6IGZhbHNlLFxuICAgICAgICBpc1ZhbGlkOiBmYWxzZSxcbiAgICAgICAgc3VibWl0Q291bnQ6IDAsXG4gICAgICAgIGRpcnR5RmllbGRzOiB7fSxcbiAgICAgICAgdG91Y2hlZEZpZWxkczoge30sXG4gICAgICAgIHZhbGlkYXRpbmdGaWVsZHM6IHt9LFxuICAgICAgICBlcnJvcnM6IHByb3BzLmVycm9ycyB8fCB7fSxcbiAgICAgICAgZGlzYWJsZWQ6IHByb3BzLmRpc2FibGVkIHx8IGZhbHNlLFxuICAgICAgICBpc1JlYWR5OiBmYWxzZSxcbiAgICAgICAgZGVmYXVsdFZhbHVlczogaXNGdW5jdGlvbihwcm9wcy5kZWZhdWx0VmFsdWVzKVxuICAgICAgICAgICAgPyB1bmRlZmluZWRcbiAgICAgICAgICAgIDogcHJvcHMuZGVmYXVsdFZhbHVlcyxcbiAgICB9KTtcbiAgICBpZiAoIV9mb3JtQ29udHJvbC5jdXJyZW50KSB7XG4gICAgICAgIF9mb3JtQ29udHJvbC5jdXJyZW50ID0ge1xuICAgICAgICAgICAgLi4uKHByb3BzLmZvcm1Db250cm9sID8gcHJvcHMuZm9ybUNvbnRyb2wgOiBjcmVhdGVGb3JtQ29udHJvbChwcm9wcykpLFxuICAgICAgICAgICAgZm9ybVN0YXRlLFxuICAgICAgICB9O1xuICAgICAgICBpZiAocHJvcHMuZm9ybUNvbnRyb2wgJiZcbiAgICAgICAgICAgIHByb3BzLmRlZmF1bHRWYWx1ZXMgJiZcbiAgICAgICAgICAgICFpc0Z1bmN0aW9uKHByb3BzLmRlZmF1bHRWYWx1ZXMpKSB7XG4gICAgICAgICAgICBwcm9wcy5mb3JtQ29udHJvbC5yZXNldChwcm9wcy5kZWZhdWx0VmFsdWVzLCBwcm9wcy5yZXNldE9wdGlvbnMpO1xuICAgICAgICB9XG4gICAgfVxuICAgIGNvbnN0IGNvbnRyb2wgPSBfZm9ybUNvbnRyb2wuY3VycmVudC5jb250cm9sO1xuICAgIGNvbnRyb2wuX29wdGlvbnMgPSBwcm9wcztcbiAgICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICAgICAgY29uc3Qgc3ViID0gY29udHJvbC5fc3Vic2NyaWJlKHtcbiAgICAgICAgICAgIGZvcm1TdGF0ZTogY29udHJvbC5fcHJveHlGb3JtU3RhdGUsXG4gICAgICAgICAgICBjYWxsYmFjazogKCkgPT4gdXBkYXRlRm9ybVN0YXRlKHsgLi4uY29udHJvbC5fZm9ybVN0YXRlIH0pLFxuICAgICAgICAgICAgcmVSZW5kZXJSb290OiB0cnVlLFxuICAgICAgICB9KTtcbiAgICAgICAgdXBkYXRlRm9ybVN0YXRlKChkYXRhKSA9PiAoe1xuICAgICAgICAgICAgLi4uZGF0YSxcbiAgICAgICAgICAgIGlzUmVhZHk6IHRydWUsXG4gICAgICAgIH0pKTtcbiAgICAgICAgY29udHJvbC5fZm9ybVN0YXRlLmlzUmVhZHkgPSB0cnVlO1xuICAgICAgICByZXR1cm4gc3ViO1xuICAgIH0sIFtjb250cm9sXSk7XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IGNvbnRyb2wuX2Rpc2FibGVGb3JtKHByb3BzLmRpc2FibGVkKSwgW2NvbnRyb2wsIHByb3BzLmRpc2FibGVkXSk7XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgaWYgKHByb3BzLm1vZGUpIHtcbiAgICAgICAgICAgIGNvbnRyb2wuX29wdGlvbnMubW9kZSA9IHByb3BzLm1vZGU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHByb3BzLnJlVmFsaWRhdGVNb2RlKSB7XG4gICAgICAgICAgICBjb250cm9sLl9vcHRpb25zLnJlVmFsaWRhdGVNb2RlID0gcHJvcHMucmVWYWxpZGF0ZU1vZGU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHByb3BzLmVycm9ycyAmJiAhaXNFbXB0eU9iamVjdChwcm9wcy5lcnJvcnMpKSB7XG4gICAgICAgICAgICBjb250cm9sLl9zZXRFcnJvcnMocHJvcHMuZXJyb3JzKTtcbiAgICAgICAgfVxuICAgIH0sIFtjb250cm9sLCBwcm9wcy5lcnJvcnMsIHByb3BzLm1vZGUsIHByb3BzLnJlVmFsaWRhdGVNb2RlXSk7XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgcHJvcHMuc2hvdWxkVW5yZWdpc3RlciAmJlxuICAgICAgICAgICAgY29udHJvbC5fc3ViamVjdHMuc3RhdGUubmV4dCh7XG4gICAgICAgICAgICAgICAgdmFsdWVzOiBjb250cm9sLl9nZXRXYXRjaCgpLFxuICAgICAgICAgICAgfSk7XG4gICAgfSwgW2NvbnRyb2wsIHByb3BzLnNob3VsZFVucmVnaXN0ZXJdKTtcbiAgICBSZWFjdF9fZGVmYXVsdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBpZiAoY29udHJvbC5fcHJveHlGb3JtU3RhdGUuaXNEaXJ0eSkge1xuICAgICAgICAgICAgY29uc3QgaXNEaXJ0eSA9IGNvbnRyb2wuX2dldERpcnR5KCk7XG4gICAgICAgICAgICBpZiAoaXNEaXJ0eSAhPT0gZm9ybVN0YXRlLmlzRGlydHkpIHtcbiAgICAgICAgICAgICAgICBjb250cm9sLl9zdWJqZWN0cy5zdGF0ZS5uZXh0KHtcbiAgICAgICAgICAgICAgICAgICAgaXNEaXJ0eSxcbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH0sIFtjb250cm9sLCBmb3JtU3RhdGUuaXNEaXJ0eV0pO1xuICAgIFJlYWN0X19kZWZhdWx0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlmIChwcm9wcy52YWx1ZXMgJiYgIWRlZXBFcXVhbChwcm9wcy52YWx1ZXMsIF92YWx1ZXMuY3VycmVudCkpIHtcbiAgICAgICAgICAgIGNvbnRyb2wuX3Jlc2V0KHByb3BzLnZhbHVlcywgY29udHJvbC5fb3B0aW9ucy5yZXNldE9wdGlvbnMpO1xuICAgICAgICAgICAgX3ZhbHVlcy5jdXJyZW50ID0gcHJvcHMudmFsdWVzO1xuICAgICAgICAgICAgdXBkYXRlRm9ybVN0YXRlKChzdGF0ZSkgPT4gKHsgLi4uc3RhdGUgfSkpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgY29udHJvbC5fcmVzZXREZWZhdWx0VmFsdWVzKCk7XG4gICAgICAgIH1cbiAgICB9LCBbY29udHJvbCwgcHJvcHMudmFsdWVzXSk7XG4gICAgUmVhY3RfX2RlZmF1bHQudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgaWYgKCFjb250cm9sLl9zdGF0ZS5tb3VudCkge1xuICAgICAgICAgICAgY29udHJvbC5fc2V0VmFsaWQoKTtcbiAgICAgICAgICAgIGNvbnRyb2wuX3N0YXRlLm1vdW50ID0gdHJ1ZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoY29udHJvbC5fc3RhdGUud2F0Y2gpIHtcbiAgICAgICAgICAgIGNvbnRyb2wuX3N0YXRlLndhdGNoID0gZmFsc2U7XG4gICAgICAgICAgICBjb250cm9sLl9zdWJqZWN0cy5zdGF0ZS5uZXh0KHsgLi4uY29udHJvbC5fZm9ybVN0YXRlIH0pO1xuICAgICAgICB9XG4gICAgICAgIGNvbnRyb2wuX3JlbW92ZVVubW91bnRlZCgpO1xuICAgIH0pO1xuICAgIF9mb3JtQ29udHJvbC5jdXJyZW50LmZvcm1TdGF0ZSA9IGdldFByb3h5Rm9ybVN0YXRlKGZvcm1TdGF0ZSwgY29udHJvbCk7XG4gICAgcmV0dXJuIF9mb3JtQ29udHJvbC5jdXJyZW50O1xufVxuXG5leHBvcnQgeyBDb250cm9sbGVyLCBGb3JtLCBGb3JtUHJvdmlkZXIsIGFwcGVuZEVycm9ycywgY3JlYXRlRm9ybUNvbnRyb2wsIGdldCwgc2V0LCB1c2VDb250cm9sbGVyLCB1c2VGaWVsZEFycmF5LCB1c2VGb3JtLCB1c2VGb3JtQ29udGV4dCwgdXNlRm9ybVN0YXRlLCB1c2VXYXRjaCB9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguZXNtLm1qcy5tYXBcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIlJlYWN0X19kZWZhdWx0IiwiaXNDaGVja0JveElucHV0IiwiZWxlbWVudCIsInR5cGUiLCJpc0RhdGVPYmplY3QiLCJ2YWx1ZSIsIkRhdGUiLCJpc051bGxPclVuZGVmaW5lZCIsImlzT2JqZWN0VHlwZSIsImlzT2JqZWN0IiwiQXJyYXkiLCJpc0FycmF5IiwiZ2V0RXZlbnRWYWx1ZSIsImV2ZW50IiwidGFyZ2V0IiwiY2hlY2tlZCIsImdldE5vZGVQYXJlbnROYW1lIiwibmFtZSIsInN1YnN0cmluZyIsInNlYXJjaCIsImlzTmFtZUluRmllbGRBcnJheSIsIm5hbWVzIiwiaGFzIiwiaXNQbGFpbk9iamVjdCIsInRlbXBPYmplY3QiLCJwcm90b3R5cGVDb3B5IiwiY29uc3RydWN0b3IiLCJwcm90b3R5cGUiLCJoYXNPd25Qcm9wZXJ0eSIsImlzV2ViIiwid2luZG93IiwiSFRNTEVsZW1lbnQiLCJkb2N1bWVudCIsImNsb25lT2JqZWN0IiwiZGF0YSIsImNvcHkiLCJpc0ZpbGVMaXN0SW5zdGFuY2UiLCJGaWxlTGlzdCIsIlNldCIsIkJsb2IiLCJrZXkiLCJjb21wYWN0IiwiZmlsdGVyIiwiQm9vbGVhbiIsImlzVW5kZWZpbmVkIiwidmFsIiwidW5kZWZpbmVkIiwiZ2V0Iiwib2JqZWN0IiwicGF0aCIsImRlZmF1bHRWYWx1ZSIsInJlc3VsdCIsInNwbGl0IiwicmVkdWNlIiwiaXNCb29sZWFuIiwiaXNLZXkiLCJ0ZXN0Iiwic3RyaW5nVG9QYXRoIiwiaW5wdXQiLCJyZXBsYWNlIiwic2V0IiwiaW5kZXgiLCJ0ZW1wUGF0aCIsImxlbmd0aCIsImxhc3RJbmRleCIsIm5ld1ZhbHVlIiwib2JqVmFsdWUiLCJpc05hTiIsIkVWRU5UUyIsIkJMVVIiLCJGT0NVU19PVVQiLCJDSEFOR0UiLCJWQUxJREFUSU9OX01PREUiLCJvbkJsdXIiLCJvbkNoYW5nZSIsIm9uU3VibWl0Iiwib25Ub3VjaGVkIiwiYWxsIiwiSU5QVVRfVkFMSURBVElPTl9SVUxFUyIsIm1heCIsIm1pbiIsIm1heExlbmd0aCIsIm1pbkxlbmd0aCIsInBhdHRlcm4iLCJyZXF1aXJlZCIsInZhbGlkYXRlIiwiSG9va0Zvcm1Db250ZXh0IiwiY3JlYXRlQ29udGV4dCIsInVzZUZvcm1Db250ZXh0IiwidXNlQ29udGV4dCIsIkZvcm1Qcm92aWRlciIsInByb3BzIiwiY2hpbGRyZW4iLCJjcmVhdGVFbGVtZW50IiwiUHJvdmlkZXIiLCJnZXRQcm94eUZvcm1TdGF0ZSIsImZvcm1TdGF0ZSIsImNvbnRyb2wiLCJsb2NhbFByb3h5Rm9ybVN0YXRlIiwiaXNSb290IiwiZGVmYXVsdFZhbHVlcyIsIl9kZWZhdWx0VmFsdWVzIiwiT2JqZWN0IiwiZGVmaW5lUHJvcGVydHkiLCJfa2V5IiwiX3Byb3h5Rm9ybVN0YXRlIiwidXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCIsInVzZUxheW91dEVmZmVjdCIsInVzZUVmZmVjdCIsInVzZUZvcm1TdGF0ZSIsIm1ldGhvZHMiLCJkaXNhYmxlZCIsImV4YWN0IiwidXBkYXRlRm9ybVN0YXRlIiwidXNlU3RhdGUiLCJfZm9ybVN0YXRlIiwiX2xvY2FsUHJveHlGb3JtU3RhdGUiLCJ1c2VSZWYiLCJpc0RpcnR5IiwiaXNMb2FkaW5nIiwiZGlydHlGaWVsZHMiLCJ0b3VjaGVkRmllbGRzIiwidmFsaWRhdGluZ0ZpZWxkcyIsImlzVmFsaWRhdGluZyIsImlzVmFsaWQiLCJlcnJvcnMiLCJfc3Vic2NyaWJlIiwiY3VycmVudCIsImNhbGxiYWNrIiwiX3NldFZhbGlkIiwidXNlTWVtbyIsImlzU3RyaW5nIiwiZ2VuZXJhdGVXYXRjaE91dHB1dCIsIl9uYW1lcyIsImZvcm1WYWx1ZXMiLCJpc0dsb2JhbCIsIndhdGNoIiwiYWRkIiwibWFwIiwiZmllbGROYW1lIiwid2F0Y2hBbGwiLCJ1c2VXYXRjaCIsIl9kZWZhdWx0VmFsdWUiLCJ1cGRhdGVWYWx1ZSIsIl9nZXRXYXRjaCIsInZhbHVlcyIsIl9mb3JtVmFsdWVzIiwiX3JlbW92ZVVubW91bnRlZCIsInVzZUNvbnRyb2xsZXIiLCJzaG91bGRVbnJlZ2lzdGVyIiwiaXNBcnJheUZpZWxkIiwiYXJyYXkiLCJfcHJvcHMiLCJfcmVnaXN0ZXJQcm9wcyIsInJlZ2lzdGVyIiwicnVsZXMiLCJmaWVsZFN0YXRlIiwiZGVmaW5lUHJvcGVydGllcyIsImludmFsaWQiLCJlbnVtZXJhYmxlIiwiaXNUb3VjaGVkIiwiZXJyb3IiLCJ1c2VDYWxsYmFjayIsInJlZiIsImVsbSIsImZpZWxkIiwiX2ZpZWxkcyIsIl9mIiwiZm9jdXMiLCJzZWxlY3QiLCJzZXRDdXN0b21WYWxpZGl0eSIsIm1lc3NhZ2UiLCJyZXBvcnRWYWxpZGl0eSIsIl9zaG91bGRVbnJlZ2lzdGVyRmllbGQiLCJfb3B0aW9ucyIsInVwZGF0ZU1vdW50ZWQiLCJtb3VudCIsIl9zdGF0ZSIsImFjdGlvbiIsInVucmVnaXN0ZXIiLCJfc2V0RGlzYWJsZWRGaWVsZCIsIkNvbnRyb2xsZXIiLCJyZW5kZXIiLCJmbGF0dGVuIiwib2JqIiwib3V0cHV0Iiwia2V5cyIsIm5lc3RlZCIsIm5lc3RlZEtleSIsIlBPU1RfUkVRVUVTVCIsIkZvcm0iLCJtb3VudGVkIiwic2V0TW91bnRlZCIsIm1ldGhvZCIsImhlYWRlcnMiLCJlbmNUeXBlIiwib25FcnJvciIsIm9uU3VjY2VzcyIsInZhbGlkYXRlU3RhdHVzIiwicmVzdCIsInN1Ym1pdCIsImhhc0Vycm9yIiwiaGFuZGxlU3VibWl0IiwiZm9ybURhdGEiLCJGb3JtRGF0YSIsImZvcm1EYXRhSnNvbiIsIkpTT04iLCJzdHJpbmdpZnkiLCJfYSIsImZsYXR0ZW5Gb3JtVmFsdWVzIiwiYXBwZW5kIiwic2hvdWxkU3RyaW5naWZ5U3VibWlzc2lvbkRhdGEiLCJzb21lIiwiaW5jbHVkZXMiLCJyZXNwb25zZSIsImZldGNoIiwiU3RyaW5nIiwiYm9keSIsInN0YXR1cyIsIl9zdWJqZWN0cyIsInN0YXRlIiwibmV4dCIsImlzU3VibWl0U3VjY2Vzc2Z1bCIsInNldEVycm9yIiwiRnJhZ21lbnQiLCJub1ZhbGlkYXRlIiwiYXBwZW5kRXJyb3JzIiwidmFsaWRhdGVBbGxGaWVsZENyaXRlcmlhIiwidHlwZXMiLCJjb252ZXJ0VG9BcnJheVBheWxvYWQiLCJjcmVhdGVTdWJqZWN0IiwiX29ic2VydmVycyIsIm9ic2VydmVyIiwic3Vic2NyaWJlIiwicHVzaCIsInVuc3Vic2NyaWJlIiwibyIsIm9ic2VydmVycyIsImlzUHJpbWl0aXZlIiwiZGVlcEVxdWFsIiwib2JqZWN0MSIsIm9iamVjdDIiLCJnZXRUaW1lIiwia2V5czEiLCJrZXlzMiIsInZhbDEiLCJ2YWwyIiwiaXNFbXB0eU9iamVjdCIsImlzRmlsZUlucHV0IiwiaXNGdW5jdGlvbiIsImlzSFRNTEVsZW1lbnQiLCJvd25lciIsIm93bmVyRG9jdW1lbnQiLCJkZWZhdWx0VmlldyIsImlzTXVsdGlwbGVTZWxlY3QiLCJpc1JhZGlvSW5wdXQiLCJpc1JhZGlvT3JDaGVja2JveCIsImxpdmUiLCJpc0Nvbm5lY3RlZCIsImJhc2VHZXQiLCJ1cGRhdGVQYXRoIiwic2xpY2UiLCJpc0VtcHR5QXJyYXkiLCJ1bnNldCIsInBhdGhzIiwiY2hpbGRPYmplY3QiLCJvYmplY3RIYXNGdW5jdGlvbiIsIm1hcmtGaWVsZHNEaXJ0eSIsImZpZWxkcyIsImlzUGFyZW50Tm9kZUFycmF5IiwiZ2V0RGlydHlGaWVsZHNGcm9tRGVmYXVsdFZhbHVlcyIsImRpcnR5RmllbGRzRnJvbVZhbHVlcyIsImdldERpcnR5RmllbGRzIiwiZGVmYXVsdFJlc3VsdCIsInZhbGlkUmVzdWx0IiwiZ2V0Q2hlY2tib3hWYWx1ZSIsIm9wdGlvbnMiLCJvcHRpb24iLCJhdHRyaWJ1dGVzIiwiZ2V0RmllbGRWYWx1ZUFzIiwidmFsdWVBc051bWJlciIsInZhbHVlQXNEYXRlIiwic2V0VmFsdWVBcyIsIk5hTiIsImRlZmF1bHRSZXR1cm4iLCJnZXRSYWRpb1ZhbHVlIiwicHJldmlvdXMiLCJnZXRGaWVsZFZhbHVlIiwiZmlsZXMiLCJyZWZzIiwic2VsZWN0ZWRPcHRpb25zIiwiZ2V0UmVzb2x2ZXJPcHRpb25zIiwiZmllbGRzTmFtZXMiLCJjcml0ZXJpYU1vZGUiLCJzaG91bGRVc2VOYXRpdmVWYWxpZGF0aW9uIiwiaXNSZWdleCIsIlJlZ0V4cCIsImdldFJ1bGVWYWx1ZSIsInJ1bGUiLCJzb3VyY2UiLCJnZXRWYWxpZGF0aW9uTW9kZXMiLCJtb2RlIiwiaXNPblN1Ym1pdCIsImlzT25CbHVyIiwiaXNPbkNoYW5nZSIsImlzT25BbGwiLCJpc09uVG91Y2giLCJBU1lOQ19GVU5DVElPTiIsImhhc1Byb21pc2VWYWxpZGF0aW9uIiwiZmllbGRSZWZlcmVuY2UiLCJmaW5kIiwidmFsaWRhdGVGdW5jdGlvbiIsImhhc1ZhbGlkYXRpb24iLCJpc1dhdGNoZWQiLCJpc0JsdXJFdmVudCIsIndhdGNoTmFtZSIsInN0YXJ0c1dpdGgiLCJpdGVyYXRlRmllbGRzQnlBY3Rpb24iLCJhYm9ydEVhcmx5IiwiY3VycmVudEZpZWxkIiwic2NoZW1hRXJyb3JMb29rdXAiLCJqb2luIiwiZm91bmRFcnJvciIsInBvcCIsInNob3VsZFJlbmRlckZvcm1TdGF0ZSIsImZvcm1TdGF0ZURhdGEiLCJzaG91bGRTdWJzY3JpYmVCeU5hbWUiLCJzaWduYWxOYW1lIiwiY3VycmVudE5hbWUiLCJza2lwVmFsaWRhdGlvbiIsImlzU3VibWl0dGVkIiwicmVWYWxpZGF0ZU1vZGUiLCJ1bnNldEVtcHR5QXJyYXkiLCJ1cGRhdGVGaWVsZEFycmF5Um9vdEVycm9yIiwiZmllbGRBcnJheUVycm9ycyIsImlzTWVzc2FnZSIsImdldFZhbGlkYXRlRXJyb3IiLCJldmVyeSIsImdldFZhbHVlQW5kTWVzc2FnZSIsInZhbGlkYXRpb25EYXRhIiwidmFsaWRhdGVGaWVsZCIsImRpc2FibGVkRmllbGROYW1lcyIsImlzRmllbGRBcnJheSIsImlucHV0VmFsdWUiLCJpbnB1dFJlZiIsImlzUmFkaW8iLCJpc0NoZWNrQm94IiwiaXNFbXB0eSIsImFwcGVuZEVycm9yc0N1cnJ5IiwiYmluZCIsImdldE1pbk1heE1lc3NhZ2UiLCJleGNlZWRNYXgiLCJtYXhMZW5ndGhNZXNzYWdlIiwibWluTGVuZ3RoTWVzc2FnZSIsIm1heFR5cGUiLCJtaW5UeXBlIiwiZXhjZWVkTWluIiwibWF4T3V0cHV0IiwibWluT3V0cHV0IiwidmFsdWVOdW1iZXIiLCJ2YWx1ZURhdGUiLCJjb252ZXJ0VGltZVRvRGF0ZSIsInRpbWUiLCJ0b0RhdGVTdHJpbmciLCJpc1RpbWUiLCJpc1dlZWsiLCJtYXhMZW5ndGhPdXRwdXQiLCJtaW5MZW5ndGhPdXRwdXQiLCJwYXR0ZXJuVmFsdWUiLCJtYXRjaCIsInZhbGlkYXRlRXJyb3IiLCJ2YWxpZGF0aW9uUmVzdWx0IiwiZGVmYXVsdE9wdGlvbnMiLCJzaG91bGRGb2N1c0Vycm9yIiwiY3JlYXRlRm9ybUNvbnRyb2wiLCJzdWJtaXRDb3VudCIsImlzUmVhZHkiLCJpc1N1Ym1pdHRpbmciLCJ1bk1vdW50IiwiZGVsYXlFcnJvckNhbGxiYWNrIiwidGltZXIiLCJfcHJveHlTdWJzY3JpYmVGb3JtU3RhdGUiLCJzaG91bGREaXNwbGF5QWxsQXNzb2NpYXRlZEVycm9ycyIsImRlYm91bmNlIiwid2FpdCIsImNsZWFyVGltZW91dCIsInNldFRpbWVvdXQiLCJzaG91bGRVcGRhdGVWYWxpZCIsInJlc29sdmVyIiwiX3J1blNjaGVtYSIsImV4ZWN1dGVCdWlsdEluVmFsaWRhdGlvbiIsIl91cGRhdGVJc1ZhbGlkYXRpbmciLCJmcm9tIiwiZm9yRWFjaCIsIl9zZXRGaWVsZEFycmF5IiwiYXJncyIsInNob3VsZFNldFZhbHVlcyIsInNob3VsZFVwZGF0ZUZpZWxkc0FuZFN0YXRlIiwiZmllbGRWYWx1ZXMiLCJhcmdBIiwiYXJnQiIsIl9nZXREaXJ0eSIsInVwZGF0ZUVycm9ycyIsIl9zZXRFcnJvcnMiLCJ1cGRhdGVWYWxpZEFuZFZhbHVlIiwic2hvdWxkU2tpcFNldFZhbHVlQXMiLCJkZWZhdWx0Q2hlY2tlZCIsInNldEZpZWxkVmFsdWUiLCJ1cGRhdGVUb3VjaEFuZERpcnR5IiwiZmllbGRWYWx1ZSIsInNob3VsZERpcnR5Iiwic2hvdWxkUmVuZGVyIiwic2hvdWxkVXBkYXRlRmllbGQiLCJpc1ByZXZpb3VzRGlydHkiLCJpc0N1cnJlbnRGaWVsZFByaXN0aW5lIiwiaXNQcmV2aW91c0ZpZWxkVG91Y2hlZCIsInNob3VsZFJlbmRlckJ5RXJyb3IiLCJwcmV2aW91c0ZpZWxkRXJyb3IiLCJkZWxheUVycm9yIiwidXBkYXRlZEZvcm1TdGF0ZSIsImNvbnRleHQiLCJleGVjdXRlU2NoZW1hQW5kVXBkYXRlU3RhdGUiLCJzaG91bGRPbmx5Q2hlY2tWYWxpZCIsInZhbGlkIiwiaXNGaWVsZEFycmF5Um9vdCIsImlzUHJvbWlzZUZ1bmN0aW9uIiwiZmllbGRFcnJvciIsImdldFZhbHVlcyIsIl9nZXRGaWVsZEFycmF5Iiwib3B0aW9uUmVmIiwic2VsZWN0ZWQiLCJjaGVja2JveFJlZiIsInJhZGlvUmVmIiwic2hvdWxkVG91Y2giLCJzaG91bGRWYWxpZGF0ZSIsInRyaWdnZXIiLCJzZXRWYWx1ZXMiLCJmaWVsZEtleSIsInNldFZhbHVlIiwiY2xvbmVWYWx1ZSIsImlzRmllbGRWYWx1ZVVwZGF0ZWQiLCJfdXBkYXRlSXNGaWVsZFZhbHVlVXBkYXRlZCIsIk51bWJlciIsInZhbGlkYXRpb25Nb2RlQmVmb3JlU3VibWl0IiwidmFsaWRhdGlvbk1vZGVBZnRlclN1Ym1pdCIsInNob3VsZFNraXBWYWxpZGF0aW9uIiwiZGVwcyIsIndhdGNoZWQiLCJwcmV2aW91c0Vycm9yTG9va3VwUmVzdWx0IiwiZXJyb3JMb29rdXBSZXN1bHQiLCJfZm9jdXNJbnB1dCIsImZpZWxkTmFtZXMiLCJQcm9taXNlIiwic2hvdWxkRm9jdXMiLCJnZXRGaWVsZFN0YXRlIiwiY2xlYXJFcnJvcnMiLCJpbnB1dE5hbWUiLCJjdXJyZW50RXJyb3IiLCJjdXJyZW50UmVmIiwicmVzdE9mRXJyb3JUcmVlIiwicGF5bG9hZCIsIl9zZXRGb3JtU3RhdGUiLCJyZVJlbmRlclJvb3QiLCJkZWxldGUiLCJrZWVwVmFsdWUiLCJrZWVwRXJyb3IiLCJrZWVwRGlydHkiLCJrZWVwVG91Y2hlZCIsImtlZXBJc1ZhbGlkYXRpbmciLCJrZWVwRGVmYXVsdFZhbHVlIiwia2VlcElzVmFsaWQiLCJkaXNhYmxlZElzRGVmaW5lZCIsInByb2dyZXNzaXZlIiwiZmllbGRSZWYiLCJxdWVyeVNlbGVjdG9yQWxsIiwicmFkaW9PckNoZWNrYm94IiwiX2ZvY3VzRXJyb3IiLCJfZGlzYWJsZUZvcm0iLCJvblZhbGlkIiwib25JbnZhbGlkIiwiZSIsIm9uVmFsaWRFcnJvciIsInByZXZlbnREZWZhdWx0IiwicGVyc2lzdCIsInNpemUiLCJyZXNldEZpZWxkIiwiX3Jlc2V0Iiwia2VlcFN0YXRlT3B0aW9ucyIsInVwZGF0ZWRWYWx1ZXMiLCJjbG9uZVVwZGF0ZWRWYWx1ZXMiLCJpc0VtcHR5UmVzZXRWYWx1ZXMiLCJrZWVwRGVmYXVsdFZhbHVlcyIsImtlZXBWYWx1ZXMiLCJrZWVwRGlydHlWYWx1ZXMiLCJmaWVsZHNUb0NoZWNrIiwiZm9ybSIsImNsb3Nlc3QiLCJyZXNldCIsImtlZXBTdWJtaXRDb3VudCIsImtlZXBJc1N1Ym1pdHRlZCIsImtlZXBFcnJvcnMiLCJrZWVwSXNTdWJtaXRTdWNjZXNzZnVsIiwic2V0Rm9jdXMiLCJzaG91bGRTZWxlY3QiLCJfcmVzZXREZWZhdWx0VmFsdWVzIiwidGhlbiIsInJlc2V0T3B0aW9ucyIsImZvcm1Db250cm9sIiwiZ2VuZXJhdGVJZCIsImQiLCJwZXJmb3JtYW5jZSIsIm5vdyIsImMiLCJyIiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwiZ2V0Rm9jdXNGaWVsZE5hbWUiLCJmb2N1c05hbWUiLCJmb2N1c0luZGV4IiwiYXBwZW5kQXQiLCJmaWxsRW1wdHlBcnJheSIsImluc2VydCIsIm1vdmVBcnJheUF0IiwidG8iLCJzcGxpY2UiLCJwcmVwZW5kQXQiLCJyZW1vdmVBdEluZGV4ZXMiLCJpbmRleGVzIiwiaSIsInRlbXAiLCJyZW1vdmVBcnJheUF0Iiwic29ydCIsImEiLCJiIiwic3dhcEFycmF5QXQiLCJpbmRleEEiLCJpbmRleEIiLCJ1cGRhdGVBdCIsInVzZUZpZWxkQXJyYXkiLCJrZXlOYW1lIiwic2V0RmllbGRzIiwiaWRzIiwiX2ZpZWxkSWRzIiwiX25hbWUiLCJfYWN0aW9uZWQiLCJmaWVsZEFycmF5TmFtZSIsInVwZGF0ZVZhbHVlcyIsInVwZGF0ZWRGaWVsZEFycmF5VmFsdWVzIiwiYXBwZW5kVmFsdWUiLCJwcmVwZW5kIiwicHJlcGVuZFZhbHVlIiwicmVtb3ZlIiwiaW5zZXJ0JDEiLCJpbnNlcnRWYWx1ZSIsInN3YXAiLCJtb3ZlIiwidXBkYXRlIiwiaXRlbSIsImV4aXN0aW5nRXJyb3IiLCJ1c2VGb3JtIiwiX2Zvcm1Db250cm9sIiwiX3ZhbHVlcyIsInN1YiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-hook-form@7.56.4_react@18.3.1/node_modules/react-hook-form/dist/index.esm.mjs\n");

/***/ })

};
;