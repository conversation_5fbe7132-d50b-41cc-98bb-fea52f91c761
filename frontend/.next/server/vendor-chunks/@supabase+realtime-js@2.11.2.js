"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+realtime-js@2.11.2";
exports.ids = ["vendor-chunks/@supabase+realtime-js@2.11.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* binding */ REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* binding */ REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* binding */ REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* binding */ REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   \"default\": () => (/* binding */ RealtimeChannel)\n/* harmony export */ });\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_push__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/push */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/push.js\");\n/* harmony import */ var _lib_timer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/timer */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/timer.js\");\n/* harmony import */ var _RealtimePresence__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./RealtimePresence */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js\");\n/* harmony import */ var _lib_transformers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/transformers */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js\");\n\n\n\n\n\n\nvar REALTIME_POSTGRES_CHANGES_LISTEN_EVENT;\n(function(REALTIME_POSTGRES_CHANGES_LISTEN_EVENT) {\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"ALL\"] = \"*\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"INSERT\"] = \"INSERT\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"UPDATE\"] = \"UPDATE\";\n    REALTIME_POSTGRES_CHANGES_LISTEN_EVENT[\"DELETE\"] = \"DELETE\";\n})(REALTIME_POSTGRES_CHANGES_LISTEN_EVENT || (REALTIME_POSTGRES_CHANGES_LISTEN_EVENT = {}));\nvar REALTIME_LISTEN_TYPES;\n(function(REALTIME_LISTEN_TYPES) {\n    REALTIME_LISTEN_TYPES[\"BROADCAST\"] = \"broadcast\";\n    REALTIME_LISTEN_TYPES[\"PRESENCE\"] = \"presence\";\n    REALTIME_LISTEN_TYPES[\"POSTGRES_CHANGES\"] = \"postgres_changes\";\n    REALTIME_LISTEN_TYPES[\"SYSTEM\"] = \"system\";\n})(REALTIME_LISTEN_TYPES || (REALTIME_LISTEN_TYPES = {}));\nvar REALTIME_SUBSCRIBE_STATES;\n(function(REALTIME_SUBSCRIBE_STATES) {\n    REALTIME_SUBSCRIBE_STATES[\"SUBSCRIBED\"] = \"SUBSCRIBED\";\n    REALTIME_SUBSCRIBE_STATES[\"TIMED_OUT\"] = \"TIMED_OUT\";\n    REALTIME_SUBSCRIBE_STATES[\"CLOSED\"] = \"CLOSED\";\n    REALTIME_SUBSCRIBE_STATES[\"CHANNEL_ERROR\"] = \"CHANNEL_ERROR\";\n})(REALTIME_SUBSCRIBE_STATES || (REALTIME_SUBSCRIBE_STATES = {}));\nconst REALTIME_CHANNEL_STATES = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES;\n/** A channel is the basic building block of Realtime\n * and narrows the scope of data flow to subscribed clients.\n * You can think of a channel as a chatroom where participants are able to see who's online\n * and send and receive messages.\n */ class RealtimeChannel {\n    constructor(/** Topic name can be any string. */ topic, params = {\n        config: {}\n    }, socket){\n        this.topic = topic;\n        this.params = params;\n        this.socket = socket;\n        this.bindings = {};\n        this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed;\n        this.joinedOnce = false;\n        this.pushBuffer = [];\n        this.subTopic = topic.replace(/^realtime:/i, \"\");\n        this.params.config = Object.assign({\n            broadcast: {\n                ack: false,\n                self: false\n            },\n            presence: {\n                key: \"\"\n            },\n            private: false\n        }, params.config);\n        this.timeout = this.socket.timeout;\n        this.joinPush = new _lib_push__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.join, this.params, this.timeout);\n        this.rejoinTimer = new _lib_timer__WEBPACK_IMPORTED_MODULE_2__[\"default\"](()=>this._rejoinUntilConnected(), this.socket.reconnectAfterMs);\n        this.joinPush.receive(\"ok\", ()=>{\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joined;\n            this.rejoinTimer.reset();\n            this.pushBuffer.forEach((pushEvent)=>pushEvent.send());\n            this.pushBuffer = [];\n        });\n        this._onClose(()=>{\n            this.rejoinTimer.reset();\n            this.socket.log(\"channel\", `close ${this.topic} ${this._joinRef()}`);\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed;\n            this.socket._remove(this);\n        });\n        this._onError((reason)=>{\n            if (this._isLeaving() || this._isClosed()) {\n                return;\n            }\n            this.socket.log(\"channel\", `error ${this.topic}`, reason);\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this.joinPush.receive(\"timeout\", ()=>{\n            if (!this._isJoining()) {\n                return;\n            }\n            this.socket.log(\"channel\", `timeout ${this.topic}`, this.joinPush.timeout);\n            this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.errored;\n            this.rejoinTimer.scheduleTimeout();\n        });\n        this._on(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.reply, {}, (payload, ref)=>{\n            this._trigger(this._replyEventName(ref), payload);\n        });\n        this.presence = new _RealtimePresence__WEBPACK_IMPORTED_MODULE_3__[\"default\"](this);\n        this.broadcastEndpointURL = (0,_lib_transformers__WEBPACK_IMPORTED_MODULE_4__.httpEndpointURL)(this.socket.endPoint) + \"/api/broadcast\";\n        this.private = this.params.config.private || false;\n    }\n    /** Subscribe registers your client with the server */ subscribe(callback, timeout = this.timeout) {\n        var _a, _b;\n        if (!this.socket.isConnected()) {\n            this.socket.connect();\n        }\n        if (this.joinedOnce) {\n            throw `tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance`;\n        } else {\n            const { config: { broadcast, presence, private: isPrivate } } = this.params;\n            this._onError((e)=>callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, e));\n            this._onClose(()=>callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CLOSED));\n            const accessTokenPayload = {};\n            const config = {\n                broadcast,\n                presence,\n                postgres_changes: (_b = (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.map((r)=>r.filter)) !== null && _b !== void 0 ? _b : [],\n                private: isPrivate\n            };\n            if (this.socket.accessTokenValue) {\n                accessTokenPayload.access_token = this.socket.accessTokenValue;\n            }\n            this.updateJoinPayload(Object.assign({\n                config\n            }, accessTokenPayload));\n            this.joinedOnce = true;\n            this._rejoin(timeout);\n            this.joinPush.receive(\"ok\", async ({ postgres_changes })=>{\n                var _a;\n                this.socket.setAuth();\n                if (postgres_changes === undefined) {\n                    callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n                    return;\n                } else {\n                    const clientPostgresBindings = this.bindings.postgres_changes;\n                    const bindingsLen = (_a = clientPostgresBindings === null || clientPostgresBindings === void 0 ? void 0 : clientPostgresBindings.length) !== null && _a !== void 0 ? _a : 0;\n                    const newPostgresBindings = [];\n                    for(let i = 0; i < bindingsLen; i++){\n                        const clientPostgresBinding = clientPostgresBindings[i];\n                        const { filter: { event, schema, table, filter } } = clientPostgresBinding;\n                        const serverPostgresFilter = postgres_changes && postgres_changes[i];\n                        if (serverPostgresFilter && serverPostgresFilter.event === event && serverPostgresFilter.schema === schema && serverPostgresFilter.table === table && serverPostgresFilter.filter === filter) {\n                            newPostgresBindings.push(Object.assign(Object.assign({}, clientPostgresBinding), {\n                                id: serverPostgresFilter.id\n                            }));\n                        } else {\n                            this.unsubscribe();\n                            callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error(\"mismatch between server and client bindings for postgres changes\"));\n                            return;\n                        }\n                    }\n                    this.bindings.postgres_changes = newPostgresBindings;\n                    callback && callback(REALTIME_SUBSCRIBE_STATES.SUBSCRIBED);\n                    return;\n                }\n            }).receive(\"error\", (error)=>{\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.CHANNEL_ERROR, new Error(JSON.stringify(Object.values(error).join(\", \") || \"error\")));\n                return;\n            }).receive(\"timeout\", ()=>{\n                callback === null || callback === void 0 ? void 0 : callback(REALTIME_SUBSCRIBE_STATES.TIMED_OUT);\n                return;\n            });\n        }\n        return this;\n    }\n    presenceState() {\n        return this.presence.state;\n    }\n    async track(payload, opts = {}) {\n        return await this.send({\n            type: \"presence\",\n            event: \"track\",\n            payload\n        }, opts.timeout || this.timeout);\n    }\n    async untrack(opts = {}) {\n        return await this.send({\n            type: \"presence\",\n            event: \"untrack\"\n        }, opts);\n    }\n    on(type, filter, callback) {\n        return this._on(type, filter, callback);\n    }\n    /**\n     * Sends a message into the channel.\n     *\n     * @param args Arguments to send to channel\n     * @param args.type The type of event to send\n     * @param args.event The name of the event being sent\n     * @param args.payload Payload to be sent\n     * @param opts Options to be used during the send process\n     */ async send(args, opts = {}) {\n        var _a, _b;\n        if (!this._canPush() && args.type === \"broadcast\") {\n            const { event, payload: endpoint_payload } = args;\n            const authorization = this.socket.accessTokenValue ? `Bearer ${this.socket.accessTokenValue}` : \"\";\n            const options = {\n                method: \"POST\",\n                headers: {\n                    Authorization: authorization,\n                    apikey: this.socket.apiKey ? this.socket.apiKey : \"\",\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    messages: [\n                        {\n                            topic: this.subTopic,\n                            event,\n                            payload: endpoint_payload,\n                            private: this.private\n                        }\n                    ]\n                })\n            };\n            try {\n                const response = await this._fetchWithTimeout(this.broadcastEndpointURL, options, (_a = opts.timeout) !== null && _a !== void 0 ? _a : this.timeout);\n                await ((_b = response.body) === null || _b === void 0 ? void 0 : _b.cancel());\n                return response.ok ? \"ok\" : \"error\";\n            } catch (error) {\n                if (error.name === \"AbortError\") {\n                    return \"timed out\";\n                } else {\n                    return \"error\";\n                }\n            }\n        } else {\n            return new Promise((resolve)=>{\n                var _a, _b, _c;\n                const push = this._push(args.type, args, opts.timeout || this.timeout);\n                if (args.type === \"broadcast\" && !((_c = (_b = (_a = this.params) === null || _a === void 0 ? void 0 : _a.config) === null || _b === void 0 ? void 0 : _b.broadcast) === null || _c === void 0 ? void 0 : _c.ack)) {\n                    resolve(\"ok\");\n                }\n                push.receive(\"ok\", ()=>resolve(\"ok\"));\n                push.receive(\"error\", ()=>resolve(\"error\"));\n                push.receive(\"timeout\", ()=>resolve(\"timed out\"));\n            });\n        }\n    }\n    updateJoinPayload(payload) {\n        this.joinPush.updatePayload(payload);\n    }\n    /**\n     * Leaves the channel.\n     *\n     * Unsubscribes from server events, and instructs channel to terminate on server.\n     * Triggers onClose() hooks.\n     *\n     * To receive leave acknowledgements, use the a `receive` hook to bind to the server ack, ie:\n     * channel.unsubscribe().receive(\"ok\", () => alert(\"left!\") )\n     */ unsubscribe(timeout = this.timeout) {\n        this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.leaving;\n        const onClose = ()=>{\n            this.socket.log(\"channel\", `leave ${this.topic}`);\n            this._trigger(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.close, \"leave\", this._joinRef());\n        };\n        this.rejoinTimer.reset();\n        // Destroy joinPush to avoid connection timeouts during unscription phase\n        this.joinPush.destroy();\n        return new Promise((resolve)=>{\n            const leavePush = new _lib_push__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.leave, {}, timeout);\n            leavePush.receive(\"ok\", ()=>{\n                onClose();\n                resolve(\"ok\");\n            }).receive(\"timeout\", ()=>{\n                onClose();\n                resolve(\"timed out\");\n            }).receive(\"error\", ()=>{\n                resolve(\"error\");\n            });\n            leavePush.send();\n            if (!this._canPush()) {\n                leavePush.trigger(\"ok\", {});\n            }\n        });\n    }\n    /** @internal */ async _fetchWithTimeout(url, options, timeout) {\n        const controller = new AbortController();\n        const id = setTimeout(()=>controller.abort(), timeout);\n        const response = await this.socket.fetch(url, Object.assign(Object.assign({}, options), {\n            signal: controller.signal\n        }));\n        clearTimeout(id);\n        return response;\n    }\n    /** @internal */ _push(event, payload, timeout = this.timeout) {\n        if (!this.joinedOnce) {\n            throw `tried to push '${event}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;\n        }\n        let pushEvent = new _lib_push__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this, event, payload, timeout);\n        if (this._canPush()) {\n            pushEvent.send();\n        } else {\n            pushEvent.startTimeout();\n            this.pushBuffer.push(pushEvent);\n        }\n        return pushEvent;\n    }\n    /**\n     * Overridable message hook\n     *\n     * Receives all events for specialized message handling before dispatching to the channel callbacks.\n     * Must return the payload, modified or unmodified.\n     *\n     * @internal\n     */ _onMessage(_event, payload, _ref) {\n        return payload;\n    }\n    /** @internal */ _isMember(topic) {\n        return this.topic === topic;\n    }\n    /** @internal */ _joinRef() {\n        return this.joinPush.ref;\n    }\n    /** @internal */ _trigger(type, payload, ref) {\n        var _a, _b;\n        const typeLower = type.toLocaleLowerCase();\n        const { close, error, leave, join } = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS;\n        const events = [\n            close,\n            error,\n            leave,\n            join\n        ];\n        if (ref && events.indexOf(typeLower) >= 0 && ref !== this._joinRef()) {\n            return;\n        }\n        let handledPayload = this._onMessage(typeLower, payload, ref);\n        if (payload && !handledPayload) {\n            throw \"channel onMessage callbacks must return the payload, modified or unmodified\";\n        }\n        if ([\n            \"insert\",\n            \"update\",\n            \"delete\"\n        ].includes(typeLower)) {\n            (_a = this.bindings.postgres_changes) === null || _a === void 0 ? void 0 : _a.filter((bind)=>{\n                var _a, _b, _c;\n                return ((_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event) === \"*\" || ((_c = (_b = bind.filter) === null || _b === void 0 ? void 0 : _b.event) === null || _c === void 0 ? void 0 : _c.toLocaleLowerCase()) === typeLower;\n            }).map((bind)=>bind.callback(handledPayload, ref));\n        } else {\n            (_b = this.bindings[typeLower]) === null || _b === void 0 ? void 0 : _b.filter((bind)=>{\n                var _a, _b, _c, _d, _e, _f;\n                if ([\n                    \"broadcast\",\n                    \"presence\",\n                    \"postgres_changes\"\n                ].includes(typeLower)) {\n                    if (\"id\" in bind) {\n                        const bindId = bind.id;\n                        const bindEvent = (_a = bind.filter) === null || _a === void 0 ? void 0 : _a.event;\n                        return bindId && ((_b = payload.ids) === null || _b === void 0 ? void 0 : _b.includes(bindId)) && (bindEvent === \"*\" || (bindEvent === null || bindEvent === void 0 ? void 0 : bindEvent.toLocaleLowerCase()) === ((_c = payload.data) === null || _c === void 0 ? void 0 : _c.type.toLocaleLowerCase()));\n                    } else {\n                        const bindEvent = (_e = (_d = bind === null || bind === void 0 ? void 0 : bind.filter) === null || _d === void 0 ? void 0 : _d.event) === null || _e === void 0 ? void 0 : _e.toLocaleLowerCase();\n                        return bindEvent === \"*\" || bindEvent === ((_f = payload === null || payload === void 0 ? void 0 : payload.event) === null || _f === void 0 ? void 0 : _f.toLocaleLowerCase());\n                    }\n                } else {\n                    return bind.type.toLocaleLowerCase() === typeLower;\n                }\n            }).map((bind)=>{\n                if (typeof handledPayload === \"object\" && \"ids\" in handledPayload) {\n                    const postgresChanges = handledPayload.data;\n                    const { schema, table, commit_timestamp, type, errors } = postgresChanges;\n                    const enrichedPayload = {\n                        schema: schema,\n                        table: table,\n                        commit_timestamp: commit_timestamp,\n                        eventType: type,\n                        new: {},\n                        old: {},\n                        errors: errors\n                    };\n                    handledPayload = Object.assign(Object.assign({}, enrichedPayload), this._getPayloadRecords(postgresChanges));\n                }\n                bind.callback(handledPayload, ref);\n            });\n        }\n    }\n    /** @internal */ _isClosed() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.closed;\n    }\n    /** @internal */ _isJoined() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joined;\n    }\n    /** @internal */ _isJoining() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joining;\n    }\n    /** @internal */ _isLeaving() {\n        return this.state === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.leaving;\n    }\n    /** @internal */ _replyEventName(ref) {\n        return `chan_reply_${ref}`;\n    }\n    /** @internal */ _on(type, filter, callback) {\n        const typeLower = type.toLocaleLowerCase();\n        const binding = {\n            type: typeLower,\n            filter: filter,\n            callback: callback\n        };\n        if (this.bindings[typeLower]) {\n            this.bindings[typeLower].push(binding);\n        } else {\n            this.bindings[typeLower] = [\n                binding\n            ];\n        }\n        return this;\n    }\n    /** @internal */ _off(type, filter) {\n        const typeLower = type.toLocaleLowerCase();\n        this.bindings[typeLower] = this.bindings[typeLower].filter((bind)=>{\n            var _a;\n            return !(((_a = bind.type) === null || _a === void 0 ? void 0 : _a.toLocaleLowerCase()) === typeLower && RealtimeChannel.isEqual(bind.filter, filter));\n        });\n        return this;\n    }\n    /** @internal */ static isEqual(obj1, obj2) {\n        if (Object.keys(obj1).length !== Object.keys(obj2).length) {\n            return false;\n        }\n        for(const k in obj1){\n            if (obj1[k] !== obj2[k]) {\n                return false;\n            }\n        }\n        return true;\n    }\n    /** @internal */ _rejoinUntilConnected() {\n        this.rejoinTimer.scheduleTimeout();\n        if (this.socket.isConnected()) {\n            this._rejoin();\n        }\n    }\n    /**\n     * Registers a callback that will be executed when the channel closes.\n     *\n     * @internal\n     */ _onClose(callback) {\n        this._on(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.close, {}, callback);\n    }\n    /**\n     * Registers a callback that will be executed when the channel encounteres an error.\n     *\n     * @internal\n     */ _onError(callback) {\n        this._on(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.error, {}, (reason)=>callback(reason));\n    }\n    /**\n     * Returns `true` if the socket is connected and the channel has been joined.\n     *\n     * @internal\n     */ _canPush() {\n        return this.socket.isConnected() && this._isJoined();\n    }\n    /** @internal */ _rejoin(timeout = this.timeout) {\n        if (this._isLeaving()) {\n            return;\n        }\n        this.socket._leaveOpenTopic(this.topic);\n        this.state = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_STATES.joining;\n        this.joinPush.resend(timeout);\n    }\n    /** @internal */ _getPayloadRecords(payload) {\n        const records = {\n            new: {},\n            old: {}\n        };\n        if (payload.type === \"INSERT\" || payload.type === \"UPDATE\") {\n            records.new = _lib_transformers__WEBPACK_IMPORTED_MODULE_4__.convertChangeData(payload.columns, payload.record);\n        }\n        if (payload.type === \"UPDATE\" || payload.type === \"DELETE\") {\n            records.old = _lib_transformers__WEBPACK_IMPORTED_MODULE_4__.convertChangeData(payload.columns, payload.old_record);\n        }\n        return records;\n    }\n} //# sourceMappingURL=RealtimeChannel.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RealtimeClient)\n/* harmony export */ });\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_serializer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/serializer */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js\");\n/* harmony import */ var _lib_timer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/timer */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/timer.js\");\n/* harmony import */ var _lib_transformers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/transformers */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js\");\n/* harmony import */ var _RealtimeChannel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./RealtimeChannel */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js\");\n\n\n\n\n\nconst noop = ()=>{};\nconst NATIVE_WEBSOCKET_AVAILABLE = typeof WebSocket !== \"undefined\";\nconst WORKER_SCRIPT = `\n  addEventListener(\"message\", (e) => {\n    if (e.data.event === \"start\") {\n      setInterval(() => postMessage({ event: \"keepAlive\" }), e.data.interval);\n    }\n  });`;\nclass RealtimeClient {\n    /**\n     * Initializes the Socket.\n     *\n     * @param endPoint The string WebSocket endpoint, ie, \"ws://example.com/socket\", \"wss://example.com\", \"/socket\" (inherited host & protocol)\n     * @param httpEndpoint The string HTTP endpoint, ie, \"https://example.com\", \"/\" (inherited host & protocol)\n     * @param options.transport The Websocket Transport, for example WebSocket.\n     * @param options.timeout The default timeout in milliseconds to trigger push timeouts.\n     * @param options.params The optional params to pass when connecting.\n     * @param options.headers The optional headers to pass when connecting.\n     * @param options.heartbeatIntervalMs The millisec interval to send a heartbeat message.\n     * @param options.logger The optional function for specialized logging, ie: logger: (kind, msg, data) => { console.log(`${kind}: ${msg}`, data) }\n     * @param options.encode The function to encode outgoing messages. Defaults to JSON: (payload, callback) => callback(JSON.stringify(payload))\n     * @param options.decode The function to decode incoming messages. Defaults to Serializer's decode.\n     * @param options.reconnectAfterMs he optional function that returns the millsec reconnect interval. Defaults to stepped backoff off.\n     * @param options.worker Use Web Worker to set a side flow. Defaults to false.\n     * @param options.workerUrl The URL of the worker script. Defaults to https://realtime.supabase.com/worker.js that includes a heartbeat event call to keep the connection alive.\n     */ constructor(endPoint, options){\n        var _a;\n        this.accessTokenValue = null;\n        this.apiKey = null;\n        this.channels = [];\n        this.endPoint = \"\";\n        this.httpEndpoint = \"\";\n        this.headers = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_HEADERS;\n        this.params = {};\n        this.timeout = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_TIMEOUT;\n        this.heartbeatIntervalMs = 30000;\n        this.heartbeatTimer = undefined;\n        this.pendingHeartbeatRef = null;\n        this.ref = 0;\n        this.logger = noop;\n        this.conn = null;\n        this.sendBuffer = [];\n        this.serializer = new _lib_serializer__WEBPACK_IMPORTED_MODULE_1__[\"default\"]();\n        this.stateChangeCallbacks = {\n            open: [],\n            close: [],\n            error: [],\n            message: []\n        };\n        this.accessToken = null;\n        /**\n         * Use either custom fetch, if provided, or default fetch to make HTTP requests\n         *\n         * @internal\n         */ this._resolveFetch = (customFetch)=>{\n            let _fetch;\n            if (customFetch) {\n                _fetch = customFetch;\n            } else if (typeof fetch === \"undefined\") {\n                _fetch = (...args)=>Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! @supabase/node-fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\", 23)).then(({ default: fetch1 })=>fetch1(...args));\n            } else {\n                _fetch = fetch;\n            }\n            return (...args)=>_fetch(...args);\n        };\n        this.endPoint = `${endPoint}/${_lib_constants__WEBPACK_IMPORTED_MODULE_0__.TRANSPORTS.websocket}`;\n        this.httpEndpoint = (0,_lib_transformers__WEBPACK_IMPORTED_MODULE_3__.httpEndpointURL)(endPoint);\n        if (options === null || options === void 0 ? void 0 : options.transport) {\n            this.transport = options.transport;\n        } else {\n            this.transport = null;\n        }\n        if (options === null || options === void 0 ? void 0 : options.params) this.params = options.params;\n        if (options === null || options === void 0 ? void 0 : options.headers) this.headers = Object.assign(Object.assign({}, this.headers), options.headers);\n        if (options === null || options === void 0 ? void 0 : options.timeout) this.timeout = options.timeout;\n        if (options === null || options === void 0 ? void 0 : options.logger) this.logger = options.logger;\n        if (options === null || options === void 0 ? void 0 : options.heartbeatIntervalMs) this.heartbeatIntervalMs = options.heartbeatIntervalMs;\n        const accessTokenValue = (_a = options === null || options === void 0 ? void 0 : options.params) === null || _a === void 0 ? void 0 : _a.apikey;\n        if (accessTokenValue) {\n            this.accessTokenValue = accessTokenValue;\n            this.apiKey = accessTokenValue;\n        }\n        this.reconnectAfterMs = (options === null || options === void 0 ? void 0 : options.reconnectAfterMs) ? options.reconnectAfterMs : (tries)=>{\n            return [\n                1000,\n                2000,\n                5000,\n                10000\n            ][tries - 1] || 10000;\n        };\n        this.encode = (options === null || options === void 0 ? void 0 : options.encode) ? options.encode : (payload, callback)=>{\n            return callback(JSON.stringify(payload));\n        };\n        this.decode = (options === null || options === void 0 ? void 0 : options.decode) ? options.decode : this.serializer.decode.bind(this.serializer);\n        this.reconnectTimer = new _lib_timer__WEBPACK_IMPORTED_MODULE_2__[\"default\"](async ()=>{\n            this.disconnect();\n            this.connect();\n        }, this.reconnectAfterMs);\n        this.fetch = this._resolveFetch(options === null || options === void 0 ? void 0 : options.fetch);\n        if (options === null || options === void 0 ? void 0 : options.worker) {\n            if (false) {}\n            this.worker = (options === null || options === void 0 ? void 0 : options.worker) || false;\n            this.workerUrl = options === null || options === void 0 ? void 0 : options.workerUrl;\n        }\n        this.accessToken = (options === null || options === void 0 ? void 0 : options.accessToken) || null;\n    }\n    /**\n     * Connects the socket, unless already connected.\n     */ connect() {\n        if (this.conn) {\n            return;\n        }\n        if (this.transport) {\n            this.conn = new this.transport(this.endpointURL(), undefined, {\n                headers: this.headers\n            });\n            return;\n        }\n        if (NATIVE_WEBSOCKET_AVAILABLE) {\n            this.conn = new WebSocket(this.endpointURL());\n            this.setupConnection();\n            return;\n        }\n        this.conn = new WSWebSocketDummy(this.endpointURL(), undefined, {\n            close: ()=>{\n                this.conn = null;\n            }\n        });\n        Promise.all(/*! import() */[__webpack_require__.e(\"vendor-chunks/ws@8.18.2\"), __webpack_require__.e(\"_8bfc-_cf7b\")]).then(__webpack_require__.bind(__webpack_require__, /*! ws */ \"(ssr)/./node_modules/.pnpm/ws@8.18.2/node_modules/ws/wrapper.mjs\")).then(({ default: WS })=>{\n            this.conn = new WS(this.endpointURL(), undefined, {\n                headers: this.headers\n            });\n            this.setupConnection();\n        });\n    }\n    /**\n     * Returns the URL of the websocket.\n     * @returns string The URL of the websocket.\n     */ endpointURL() {\n        return this._appendParams(this.endPoint, Object.assign({}, this.params, {\n            vsn: _lib_constants__WEBPACK_IMPORTED_MODULE_0__.VSN\n        }));\n    }\n    /**\n     * Disconnects the socket.\n     *\n     * @param code A numeric status code to send on disconnect.\n     * @param reason A custom reason for the disconnect.\n     */ disconnect(code, reason) {\n        if (this.conn) {\n            this.conn.onclose = function() {}; // noop\n            if (code) {\n                this.conn.close(code, reason !== null && reason !== void 0 ? reason : \"\");\n            } else {\n                this.conn.close();\n            }\n            this.conn = null;\n            // remove open handles\n            this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n            this.reconnectTimer.reset();\n        }\n    }\n    /**\n     * Returns all created channels\n     */ getChannels() {\n        return this.channels;\n    }\n    /**\n     * Unsubscribes and removes a single channel\n     * @param channel A RealtimeChannel instance\n     */ async removeChannel(channel) {\n        const status = await channel.unsubscribe();\n        if (this.channels.length === 0) {\n            this.disconnect();\n        }\n        return status;\n    }\n    /**\n     * Unsubscribes and removes all channels\n     */ async removeAllChannels() {\n        const values_1 = await Promise.all(this.channels.map((channel)=>channel.unsubscribe()));\n        this.disconnect();\n        return values_1;\n    }\n    /**\n     * Logs the message.\n     *\n     * For customized logging, `this.logger` can be overridden.\n     */ log(kind, msg, data) {\n        this.logger(kind, msg, data);\n    }\n    /**\n     * Returns the current state of the socket.\n     */ connectionState() {\n        switch(this.conn && this.conn.readyState){\n            case _lib_constants__WEBPACK_IMPORTED_MODULE_0__.SOCKET_STATES.connecting:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Connecting;\n            case _lib_constants__WEBPACK_IMPORTED_MODULE_0__.SOCKET_STATES.open:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Open;\n            case _lib_constants__WEBPACK_IMPORTED_MODULE_0__.SOCKET_STATES.closing:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Closing;\n            default:\n                return _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Closed;\n        }\n    }\n    /**\n     * Returns `true` is the connection is open.\n     */ isConnected() {\n        return this.connectionState() === _lib_constants__WEBPACK_IMPORTED_MODULE_0__.CONNECTION_STATE.Open;\n    }\n    channel(topic, params = {\n        config: {}\n    }) {\n        const chan = new _RealtimeChannel__WEBPACK_IMPORTED_MODULE_4__[\"default\"](`realtime:${topic}`, params, this);\n        this.channels.push(chan);\n        return chan;\n    }\n    /**\n     * Push out a message if the socket is connected.\n     *\n     * If the socket is not connected, the message gets enqueued within a local buffer, and sent out when a connection is next established.\n     */ push(data) {\n        const { topic, event, payload, ref } = data;\n        const callback = ()=>{\n            this.encode(data, (result)=>{\n                var _a;\n                (_a = this.conn) === null || _a === void 0 ? void 0 : _a.send(result);\n            });\n        };\n        this.log(\"push\", `${topic} ${event} (${ref})`, payload);\n        if (this.isConnected()) {\n            callback();\n        } else {\n            this.sendBuffer.push(callback);\n        }\n    }\n    /**\n     * Sets the JWT access token used for channel subscription authorization and Realtime RLS.\n     *\n     * If param is null it will use the `accessToken` callback function or the token set on the client.\n     *\n     * On callback used, it will set the value of the token internal to the client.\n     *\n     * @param token A JWT string to override the token set on the client.\n     */ async setAuth(token = null) {\n        let tokenToSend = token || this.accessToken && await this.accessToken() || this.accessTokenValue;\n        if (tokenToSend) {\n            let parsed = null;\n            try {\n                parsed = JSON.parse(atob(tokenToSend.split(\".\")[1]));\n            } catch (_error) {}\n            if (parsed && parsed.exp) {\n                let now = Math.floor(Date.now() / 1000);\n                let valid = now - parsed.exp < 0;\n                if (!valid) {\n                    this.log(\"auth\", `InvalidJWTToken: Invalid value for JWT claim \"exp\" with value ${parsed.exp}`);\n                    return Promise.reject(`InvalidJWTToken: Invalid value for JWT claim \"exp\" with value ${parsed.exp}`);\n                }\n            }\n            this.accessTokenValue = tokenToSend;\n            this.channels.forEach((channel)=>{\n                tokenToSend && channel.updateJoinPayload({\n                    access_token: tokenToSend\n                });\n                if (channel.joinedOnce && channel._isJoined()) {\n                    channel._push(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.access_token, {\n                        access_token: tokenToSend\n                    });\n                }\n            });\n        }\n    }\n    /**\n     * Sends a heartbeat message if the socket is connected.\n     */ async sendHeartbeat() {\n        var _a;\n        if (!this.isConnected()) {\n            return;\n        }\n        if (this.pendingHeartbeatRef) {\n            this.pendingHeartbeatRef = null;\n            this.log(\"transport\", \"heartbeat timeout. Attempting to re-establish connection\");\n            (_a = this.conn) === null || _a === void 0 ? void 0 : _a.close(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.WS_CLOSE_NORMAL, \"hearbeat timeout\");\n            return;\n        }\n        this.pendingHeartbeatRef = this._makeRef();\n        this.push({\n            topic: \"phoenix\",\n            event: \"heartbeat\",\n            payload: {},\n            ref: this.pendingHeartbeatRef\n        });\n        this.setAuth();\n    }\n    /**\n     * Flushes send buffer\n     */ flushSendBuffer() {\n        if (this.isConnected() && this.sendBuffer.length > 0) {\n            this.sendBuffer.forEach((callback)=>callback());\n            this.sendBuffer = [];\n        }\n    }\n    /**\n     * Return the next message ref, accounting for overflows\n     *\n     * @internal\n     */ _makeRef() {\n        let newRef = this.ref + 1;\n        if (newRef === this.ref) {\n            this.ref = 0;\n        } else {\n            this.ref = newRef;\n        }\n        return this.ref.toString();\n    }\n    /**\n     * Unsubscribe from channels with the specified topic.\n     *\n     * @internal\n     */ _leaveOpenTopic(topic) {\n        let dupChannel = this.channels.find((c)=>c.topic === topic && (c._isJoined() || c._isJoining()));\n        if (dupChannel) {\n            this.log(\"transport\", `leaving duplicate topic \"${topic}\"`);\n            dupChannel.unsubscribe();\n        }\n    }\n    /**\n     * Removes a subscription from the socket.\n     *\n     * @param channel An open subscription.\n     *\n     * @internal\n     */ _remove(channel) {\n        this.channels = this.channels.filter((c)=>c._joinRef() !== channel._joinRef());\n    }\n    /**\n     * Sets up connection handlers.\n     *\n     * @internal\n     */ setupConnection() {\n        if (this.conn) {\n            this.conn.binaryType = \"arraybuffer\";\n            this.conn.onopen = ()=>this._onConnOpen();\n            this.conn.onerror = (error)=>this._onConnError(error);\n            this.conn.onmessage = (event)=>this._onConnMessage(event);\n            this.conn.onclose = (event)=>this._onConnClose(event);\n        }\n    }\n    /** @internal */ _onConnMessage(rawMessage) {\n        this.decode(rawMessage.data, (msg)=>{\n            let { topic, event, payload, ref } = msg;\n            if (ref && ref === this.pendingHeartbeatRef) {\n                this.pendingHeartbeatRef = null;\n            }\n            this.log(\"receive\", `${payload.status || \"\"} ${topic} ${event} ${ref && \"(\" + ref + \")\" || \"\"}`, payload);\n            this.channels.filter((channel)=>channel._isMember(topic)).forEach((channel)=>channel._trigger(event, payload, ref));\n            this.stateChangeCallbacks.message.forEach((callback)=>callback(msg));\n        });\n    }\n    /** @internal */ async _onConnOpen() {\n        this.log(\"transport\", `connected to ${this.endpointURL()}`);\n        this.flushSendBuffer();\n        this.reconnectTimer.reset();\n        if (!this.worker) {\n            this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n            this.heartbeatTimer = setInterval(()=>this.sendHeartbeat(), this.heartbeatIntervalMs);\n        } else {\n            if (this.workerUrl) {\n                this.log(\"worker\", `starting worker for from ${this.workerUrl}`);\n            } else {\n                this.log(\"worker\", `starting default worker`);\n            }\n            const objectUrl = this._workerObjectUrl(this.workerUrl);\n            this.workerRef = new Worker(objectUrl);\n            this.workerRef.onerror = (error)=>{\n                this.log(\"worker\", \"worker error\", error.message);\n                this.workerRef.terminate();\n            };\n            this.workerRef.onmessage = (event)=>{\n                if (event.data.event === \"keepAlive\") {\n                    this.sendHeartbeat();\n                }\n            };\n            this.workerRef.postMessage({\n                event: \"start\",\n                interval: this.heartbeatIntervalMs\n            });\n        }\n        this.stateChangeCallbacks.open.forEach((callback)=>callback());\n    }\n    /** @internal */ _onConnClose(event) {\n        this.log(\"transport\", \"close\", event);\n        this._triggerChanError();\n        this.heartbeatTimer && clearInterval(this.heartbeatTimer);\n        this.reconnectTimer.scheduleTimeout();\n        this.stateChangeCallbacks.close.forEach((callback)=>callback(event));\n    }\n    /** @internal */ _onConnError(error) {\n        this.log(\"transport\", error.message);\n        this._triggerChanError();\n        this.stateChangeCallbacks.error.forEach((callback)=>callback(error));\n    }\n    /** @internal */ _triggerChanError() {\n        this.channels.forEach((channel)=>channel._trigger(_lib_constants__WEBPACK_IMPORTED_MODULE_0__.CHANNEL_EVENTS.error));\n    }\n    /** @internal */ _appendParams(url, params) {\n        if (Object.keys(params).length === 0) {\n            return url;\n        }\n        const prefix = url.match(/\\?/) ? \"&\" : \"?\";\n        const query = new URLSearchParams(params);\n        return `${url}${prefix}${query}`;\n    }\n    _workerObjectUrl(url) {\n        let result_url;\n        if (url) {\n            result_url = url;\n        } else {\n            const blob = new Blob([\n                WORKER_SCRIPT\n            ], {\n                type: \"application/javascript\"\n            });\n            result_url = URL.createObjectURL(blob);\n        }\n        return result_url;\n    }\n}\nclass WSWebSocketDummy {\n    constructor(address, _protocols, options){\n        this.binaryType = \"arraybuffer\";\n        this.onclose = ()=>{};\n        this.onerror = ()=>{};\n        this.onmessage = ()=>{};\n        this.onopen = ()=>{};\n        this.readyState = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.SOCKET_STATES.connecting;\n        this.send = ()=>{};\n        this.url = null;\n        this.url = address;\n        this.close = options.close;\n    }\n} //# sourceMappingURL=RealtimeClient.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* binding */ REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   \"default\": () => (/* binding */ RealtimePresence)\n/* harmony export */ });\n/*\n  This file draws heavily from https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/assets/js/phoenix/presence.js\n  License: https://github.com/phoenixframework/phoenix/blob/d344ec0a732ab4ee204215b31de69cf4be72e3bf/LICENSE.md\n*/ var REALTIME_PRESENCE_LISTEN_EVENTS;\n(function(REALTIME_PRESENCE_LISTEN_EVENTS) {\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"SYNC\"] = \"sync\";\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"JOIN\"] = \"join\";\n    REALTIME_PRESENCE_LISTEN_EVENTS[\"LEAVE\"] = \"leave\";\n})(REALTIME_PRESENCE_LISTEN_EVENTS || (REALTIME_PRESENCE_LISTEN_EVENTS = {}));\nclass RealtimePresence {\n    /**\n     * Initializes the Presence.\n     *\n     * @param channel - The RealtimeChannel\n     * @param opts - The options,\n     *        for example `{events: {state: 'state', diff: 'diff'}}`\n     */ constructor(channel, opts){\n        this.channel = channel;\n        this.state = {};\n        this.pendingDiffs = [];\n        this.joinRef = null;\n        this.caller = {\n            onJoin: ()=>{},\n            onLeave: ()=>{},\n            onSync: ()=>{}\n        };\n        const events = (opts === null || opts === void 0 ? void 0 : opts.events) || {\n            state: \"presence_state\",\n            diff: \"presence_diff\"\n        };\n        this.channel._on(events.state, {}, (newState)=>{\n            const { onJoin, onLeave, onSync } = this.caller;\n            this.joinRef = this.channel._joinRef();\n            this.state = RealtimePresence.syncState(this.state, newState, onJoin, onLeave);\n            this.pendingDiffs.forEach((diff)=>{\n                this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n            });\n            this.pendingDiffs = [];\n            onSync();\n        });\n        this.channel._on(events.diff, {}, (diff)=>{\n            const { onJoin, onLeave, onSync } = this.caller;\n            if (this.inPendingSyncState()) {\n                this.pendingDiffs.push(diff);\n            } else {\n                this.state = RealtimePresence.syncDiff(this.state, diff, onJoin, onLeave);\n                onSync();\n            }\n        });\n        this.onJoin((key, currentPresences, newPresences)=>{\n            this.channel._trigger(\"presence\", {\n                event: \"join\",\n                key,\n                currentPresences,\n                newPresences\n            });\n        });\n        this.onLeave((key, currentPresences, leftPresences)=>{\n            this.channel._trigger(\"presence\", {\n                event: \"leave\",\n                key,\n                currentPresences,\n                leftPresences\n            });\n        });\n        this.onSync(()=>{\n            this.channel._trigger(\"presence\", {\n                event: \"sync\"\n            });\n        });\n    }\n    /**\n     * Used to sync the list of presences on the server with the\n     * client's state.\n     *\n     * An optional `onJoin` and `onLeave` callback can be provided to\n     * react to changes in the client's local presences across\n     * disconnects and reconnects with the server.\n     *\n     * @internal\n     */ static syncState(currentState, newState, onJoin, onLeave) {\n        const state = this.cloneDeep(currentState);\n        const transformedState = this.transformState(newState);\n        const joins = {};\n        const leaves = {};\n        this.map(state, (key, presences)=>{\n            if (!transformedState[key]) {\n                leaves[key] = presences;\n            }\n        });\n        this.map(transformedState, (key, newPresences)=>{\n            const currentPresences = state[key];\n            if (currentPresences) {\n                const newPresenceRefs = newPresences.map((m)=>m.presence_ref);\n                const curPresenceRefs = currentPresences.map((m)=>m.presence_ref);\n                const joinedPresences = newPresences.filter((m)=>curPresenceRefs.indexOf(m.presence_ref) < 0);\n                const leftPresences = currentPresences.filter((m)=>newPresenceRefs.indexOf(m.presence_ref) < 0);\n                if (joinedPresences.length > 0) {\n                    joins[key] = joinedPresences;\n                }\n                if (leftPresences.length > 0) {\n                    leaves[key] = leftPresences;\n                }\n            } else {\n                joins[key] = newPresences;\n            }\n        });\n        return this.syncDiff(state, {\n            joins,\n            leaves\n        }, onJoin, onLeave);\n    }\n    /**\n     * Used to sync a diff of presence join and leave events from the\n     * server, as they happen.\n     *\n     * Like `syncState`, `syncDiff` accepts optional `onJoin` and\n     * `onLeave` callbacks to react to a user joining or leaving from a\n     * device.\n     *\n     * @internal\n     */ static syncDiff(state, diff, onJoin, onLeave) {\n        const { joins, leaves } = {\n            joins: this.transformState(diff.joins),\n            leaves: this.transformState(diff.leaves)\n        };\n        if (!onJoin) {\n            onJoin = ()=>{};\n        }\n        if (!onLeave) {\n            onLeave = ()=>{};\n        }\n        this.map(joins, (key, newPresences)=>{\n            var _a;\n            const currentPresences = (_a = state[key]) !== null && _a !== void 0 ? _a : [];\n            state[key] = this.cloneDeep(newPresences);\n            if (currentPresences.length > 0) {\n                const joinedPresenceRefs = state[key].map((m)=>m.presence_ref);\n                const curPresences = currentPresences.filter((m)=>joinedPresenceRefs.indexOf(m.presence_ref) < 0);\n                state[key].unshift(...curPresences);\n            }\n            onJoin(key, currentPresences, newPresences);\n        });\n        this.map(leaves, (key, leftPresences)=>{\n            let currentPresences = state[key];\n            if (!currentPresences) return;\n            const presenceRefsToRemove = leftPresences.map((m)=>m.presence_ref);\n            currentPresences = currentPresences.filter((m)=>presenceRefsToRemove.indexOf(m.presence_ref) < 0);\n            state[key] = currentPresences;\n            onLeave(key, currentPresences, leftPresences);\n            if (currentPresences.length === 0) delete state[key];\n        });\n        return state;\n    }\n    /** @internal */ static map(obj, func) {\n        return Object.getOwnPropertyNames(obj).map((key)=>func(key, obj[key]));\n    }\n    /**\n     * Remove 'metas' key\n     * Change 'phx_ref' to 'presence_ref'\n     * Remove 'phx_ref' and 'phx_ref_prev'\n     *\n     * @example\n     * // returns {\n     *  abc123: [\n     *    { presence_ref: '2', user_id: 1 },\n     *    { presence_ref: '3', user_id: 2 }\n     *  ]\n     * }\n     * RealtimePresence.transformState({\n     *  abc123: {\n     *    metas: [\n     *      { phx_ref: '2', phx_ref_prev: '1' user_id: 1 },\n     *      { phx_ref: '3', user_id: 2 }\n     *    ]\n     *  }\n     * })\n     *\n     * @internal\n     */ static transformState(state) {\n        state = this.cloneDeep(state);\n        return Object.getOwnPropertyNames(state).reduce((newState, key)=>{\n            const presences = state[key];\n            if (\"metas\" in presences) {\n                newState[key] = presences.metas.map((presence)=>{\n                    presence[\"presence_ref\"] = presence[\"phx_ref\"];\n                    delete presence[\"phx_ref\"];\n                    delete presence[\"phx_ref_prev\"];\n                    return presence;\n                });\n            } else {\n                newState[key] = presences;\n            }\n            return newState;\n        }, {});\n    }\n    /** @internal */ static cloneDeep(obj) {\n        return JSON.parse(JSON.stringify(obj));\n    }\n    /** @internal */ onJoin(callback) {\n        this.caller.onJoin = callback;\n    }\n    /** @internal */ onLeave(callback) {\n        this.caller.onLeave = callback;\n    }\n    /** @internal */ onSync(callback) {\n        this.caller.onSync = callback;\n    }\n    /** @internal */ inPendingSyncState() {\n        return !this.joinRef || this.joinRef !== this.channel._joinRef();\n    }\n} //# sourceMappingURL=RealtimePresence.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/index.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* reexport safe */ _RealtimePresence__WEBPACK_IMPORTED_MODULE_2__.REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__.REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   RealtimeChannel: () => (/* reexport safe */ _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   RealtimeClient: () => (/* reexport safe */ _RealtimeClient__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   RealtimePresence: () => (/* reexport safe */ _RealtimePresence__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _RealtimeClient__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./RealtimeClient */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js\");\n/* harmony import */ var _RealtimeChannel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./RealtimeChannel */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js\");\n/* harmony import */ var _RealtimePresence__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./RealtimePresence */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js\");\n\n\n\n //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3JlYWx0aW1lLWpzQDIuMTEuMi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3JlYWx0aW1lLWpzL2Rpc3QvbW9kdWxlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQThDO0FBQzBIO0FBQ2hGO0FBQzBILENBQ2xOLGlDQUFpQyIsInNvdXJjZXMiOlsid2VicGFjazovL2J1Z3dhdGNoZ292LWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0BzdXBhYmFzZStyZWFsdGltZS1qc0AyLjExLjIvbm9kZV9tb2R1bGVzL0BzdXBhYmFzZS9yZWFsdGltZS1qcy9kaXN0L21vZHVsZS9pbmRleC5qcz80MTVjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFsdGltZUNsaWVudCBmcm9tICcuL1JlYWx0aW1lQ2xpZW50JztcbmltcG9ydCBSZWFsdGltZUNoYW5uZWwsIHsgUkVBTFRJTUVfTElTVEVOX1RZUEVTLCBSRUFMVElNRV9QT1NUR1JFU19DSEFOR0VTX0xJU1RFTl9FVkVOVCwgUkVBTFRJTUVfU1VCU0NSSUJFX1NUQVRFUywgUkVBTFRJTUVfQ0hBTk5FTF9TVEFURVMsIH0gZnJvbSAnLi9SZWFsdGltZUNoYW5uZWwnO1xuaW1wb3J0IFJlYWx0aW1lUHJlc2VuY2UsIHsgUkVBTFRJTUVfUFJFU0VOQ0VfTElTVEVOX0VWRU5UUywgfSBmcm9tICcuL1JlYWx0aW1lUHJlc2VuY2UnO1xuZXhwb3J0IHsgUmVhbHRpbWVQcmVzZW5jZSwgUmVhbHRpbWVDaGFubmVsLCBSZWFsdGltZUNsaWVudCwgUkVBTFRJTUVfTElTVEVOX1RZUEVTLCBSRUFMVElNRV9QT1NUR1JFU19DSEFOR0VTX0xJU1RFTl9FVkVOVCwgUkVBTFRJTUVfUFJFU0VOQ0VfTElTVEVOX0VWRU5UUywgUkVBTFRJTUVfU1VCU0NSSUJFX1NUQVRFUywgUkVBTFRJTUVfQ0hBTk5FTF9TVEFURVMsIH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOlsiUmVhbHRpbWVDbGllbnQiLCJSZWFsdGltZUNoYW5uZWwiLCJSRUFMVElNRV9MSVNURU5fVFlQRVMiLCJSRUFMVElNRV9QT1NUR1JFU19DSEFOR0VTX0xJU1RFTl9FVkVOVCIsIlJFQUxUSU1FX1NVQlNDUklCRV9TVEFURVMiLCJSRUFMVElNRV9DSEFOTkVMX1NUQVRFUyIsIlJlYWx0aW1lUHJlc2VuY2UiLCJSRUFMVElNRV9QUkVTRU5DRV9MSVNURU5fRVZFTlRTIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/constants.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/constants.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CHANNEL_EVENTS: () => (/* binding */ CHANNEL_EVENTS),\n/* harmony export */   CHANNEL_STATES: () => (/* binding */ CHANNEL_STATES),\n/* harmony export */   CONNECTION_STATE: () => (/* binding */ CONNECTION_STATE),\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS),\n/* harmony export */   DEFAULT_TIMEOUT: () => (/* binding */ DEFAULT_TIMEOUT),\n/* harmony export */   SOCKET_STATES: () => (/* binding */ SOCKET_STATES),\n/* harmony export */   TRANSPORTS: () => (/* binding */ TRANSPORTS),\n/* harmony export */   VSN: () => (/* binding */ VSN),\n/* harmony export */   WS_CLOSE_NORMAL: () => (/* binding */ WS_CLOSE_NORMAL)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/version.js\");\n\nconst DEFAULT_HEADERS = {\n    \"X-Client-Info\": `realtime-js/${_version__WEBPACK_IMPORTED_MODULE_0__.version}`\n};\nconst VSN = \"1.0.0\";\nconst DEFAULT_TIMEOUT = 10000;\nconst WS_CLOSE_NORMAL = 1000;\nvar SOCKET_STATES;\n(function(SOCKET_STATES) {\n    SOCKET_STATES[SOCKET_STATES[\"connecting\"] = 0] = \"connecting\";\n    SOCKET_STATES[SOCKET_STATES[\"open\"] = 1] = \"open\";\n    SOCKET_STATES[SOCKET_STATES[\"closing\"] = 2] = \"closing\";\n    SOCKET_STATES[SOCKET_STATES[\"closed\"] = 3] = \"closed\";\n})(SOCKET_STATES || (SOCKET_STATES = {}));\nvar CHANNEL_STATES;\n(function(CHANNEL_STATES) {\n    CHANNEL_STATES[\"closed\"] = \"closed\";\n    CHANNEL_STATES[\"errored\"] = \"errored\";\n    CHANNEL_STATES[\"joined\"] = \"joined\";\n    CHANNEL_STATES[\"joining\"] = \"joining\";\n    CHANNEL_STATES[\"leaving\"] = \"leaving\";\n})(CHANNEL_STATES || (CHANNEL_STATES = {}));\nvar CHANNEL_EVENTS;\n(function(CHANNEL_EVENTS) {\n    CHANNEL_EVENTS[\"close\"] = \"phx_close\";\n    CHANNEL_EVENTS[\"error\"] = \"phx_error\";\n    CHANNEL_EVENTS[\"join\"] = \"phx_join\";\n    CHANNEL_EVENTS[\"reply\"] = \"phx_reply\";\n    CHANNEL_EVENTS[\"leave\"] = \"phx_leave\";\n    CHANNEL_EVENTS[\"access_token\"] = \"access_token\";\n})(CHANNEL_EVENTS || (CHANNEL_EVENTS = {}));\nvar TRANSPORTS;\n(function(TRANSPORTS) {\n    TRANSPORTS[\"websocket\"] = \"websocket\";\n})(TRANSPORTS || (TRANSPORTS = {}));\nvar CONNECTION_STATE;\n(function(CONNECTION_STATE) {\n    CONNECTION_STATE[\"Connecting\"] = \"connecting\";\n    CONNECTION_STATE[\"Open\"] = \"open\";\n    CONNECTION_STATE[\"Closing\"] = \"closing\";\n    CONNECTION_STATE[\"Closed\"] = \"closed\";\n})(CONNECTION_STATE || (CONNECTION_STATE = {})); //# sourceMappingURL=constants.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/push.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/push.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Push)\n/* harmony export */ });\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/constants */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/constants.js\");\n\nclass Push {\n    /**\n     * Initializes the Push\n     *\n     * @param channel The Channel\n     * @param event The event, for example `\"phx_join\"`\n     * @param payload The payload, for example `{user_id: 123}`\n     * @param timeout The push timeout in milliseconds\n     */ constructor(channel, event, payload = {}, timeout = _lib_constants__WEBPACK_IMPORTED_MODULE_0__.DEFAULT_TIMEOUT){\n        this.channel = channel;\n        this.event = event;\n        this.payload = payload;\n        this.timeout = timeout;\n        this.sent = false;\n        this.timeoutTimer = undefined;\n        this.ref = \"\";\n        this.receivedResp = null;\n        this.recHooks = [];\n        this.refEvent = null;\n    }\n    resend(timeout) {\n        this.timeout = timeout;\n        this._cancelRefEvent();\n        this.ref = \"\";\n        this.refEvent = null;\n        this.receivedResp = null;\n        this.sent = false;\n        this.send();\n    }\n    send() {\n        if (this._hasReceived(\"timeout\")) {\n            return;\n        }\n        this.startTimeout();\n        this.sent = true;\n        this.channel.socket.push({\n            topic: this.channel.topic,\n            event: this.event,\n            payload: this.payload,\n            ref: this.ref,\n            join_ref: this.channel._joinRef()\n        });\n    }\n    updatePayload(payload) {\n        this.payload = Object.assign(Object.assign({}, this.payload), payload);\n    }\n    receive(status, callback) {\n        var _a;\n        if (this._hasReceived(status)) {\n            callback((_a = this.receivedResp) === null || _a === void 0 ? void 0 : _a.response);\n        }\n        this.recHooks.push({\n            status,\n            callback\n        });\n        return this;\n    }\n    startTimeout() {\n        if (this.timeoutTimer) {\n            return;\n        }\n        this.ref = this.channel.socket._makeRef();\n        this.refEvent = this.channel._replyEventName(this.ref);\n        const callback = (payload)=>{\n            this._cancelRefEvent();\n            this._cancelTimeout();\n            this.receivedResp = payload;\n            this._matchReceive(payload);\n        };\n        this.channel._on(this.refEvent, {}, callback);\n        this.timeoutTimer = setTimeout(()=>{\n            this.trigger(\"timeout\", {});\n        }, this.timeout);\n    }\n    trigger(status, response) {\n        if (this.refEvent) this.channel._trigger(this.refEvent, {\n            status,\n            response\n        });\n    }\n    destroy() {\n        this._cancelRefEvent();\n        this._cancelTimeout();\n    }\n    _cancelRefEvent() {\n        if (!this.refEvent) {\n            return;\n        }\n        this.channel._off(this.refEvent, {});\n    }\n    _cancelTimeout() {\n        clearTimeout(this.timeoutTimer);\n        this.timeoutTimer = undefined;\n    }\n    _matchReceive({ status, response }) {\n        this.recHooks.filter((h)=>h.status === status).forEach((h)=>h.callback(response));\n    }\n    _hasReceived(status) {\n        return this.receivedResp && this.receivedResp.status === status;\n    }\n} //# sourceMappingURL=push.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3JlYWx0aW1lLWpzQDIuMTEuMi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3JlYWx0aW1lLWpzL2Rpc3QvbW9kdWxlL2xpYi9wdXNoLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW1EO0FBQ3BDLE1BQU1DO0lBQ2pCOzs7Ozs7O0tBT0MsR0FDREMsWUFBWUMsT0FBTyxFQUFFQyxLQUFLLEVBQUVDLFVBQVUsQ0FBQyxDQUFDLEVBQUVDLFVBQVVOLDJEQUFlLENBQUU7UUFDakUsSUFBSSxDQUFDRyxPQUFPLEdBQUdBO1FBQ2YsSUFBSSxDQUFDQyxLQUFLLEdBQUdBO1FBQ2IsSUFBSSxDQUFDQyxPQUFPLEdBQUdBO1FBQ2YsSUFBSSxDQUFDQyxPQUFPLEdBQUdBO1FBQ2YsSUFBSSxDQUFDQyxJQUFJLEdBQUc7UUFDWixJQUFJLENBQUNDLFlBQVksR0FBR0M7UUFDcEIsSUFBSSxDQUFDQyxHQUFHLEdBQUc7UUFDWCxJQUFJLENBQUNDLFlBQVksR0FBRztRQUNwQixJQUFJLENBQUNDLFFBQVEsR0FBRyxFQUFFO1FBQ2xCLElBQUksQ0FBQ0MsUUFBUSxHQUFHO0lBQ3BCO0lBQ0FDLE9BQU9SLE9BQU8sRUFBRTtRQUNaLElBQUksQ0FBQ0EsT0FBTyxHQUFHQTtRQUNmLElBQUksQ0FBQ1MsZUFBZTtRQUNwQixJQUFJLENBQUNMLEdBQUcsR0FBRztRQUNYLElBQUksQ0FBQ0csUUFBUSxHQUFHO1FBQ2hCLElBQUksQ0FBQ0YsWUFBWSxHQUFHO1FBQ3BCLElBQUksQ0FBQ0osSUFBSSxHQUFHO1FBQ1osSUFBSSxDQUFDUyxJQUFJO0lBQ2I7SUFDQUEsT0FBTztRQUNILElBQUksSUFBSSxDQUFDQyxZQUFZLENBQUMsWUFBWTtZQUM5QjtRQUNKO1FBQ0EsSUFBSSxDQUFDQyxZQUFZO1FBQ2pCLElBQUksQ0FBQ1gsSUFBSSxHQUFHO1FBQ1osSUFBSSxDQUFDSixPQUFPLENBQUNnQixNQUFNLENBQUNDLElBQUksQ0FBQztZQUNyQkMsT0FBTyxJQUFJLENBQUNsQixPQUFPLENBQUNrQixLQUFLO1lBQ3pCakIsT0FBTyxJQUFJLENBQUNBLEtBQUs7WUFDakJDLFNBQVMsSUFBSSxDQUFDQSxPQUFPO1lBQ3JCSyxLQUFLLElBQUksQ0FBQ0EsR0FBRztZQUNiWSxVQUFVLElBQUksQ0FBQ25CLE9BQU8sQ0FBQ29CLFFBQVE7UUFDbkM7SUFDSjtJQUNBQyxjQUFjbkIsT0FBTyxFQUFFO1FBQ25CLElBQUksQ0FBQ0EsT0FBTyxHQUFHb0IsT0FBT0MsTUFBTSxDQUFDRCxPQUFPQyxNQUFNLENBQUMsQ0FBQyxHQUFHLElBQUksQ0FBQ3JCLE9BQU8sR0FBR0E7SUFDbEU7SUFDQXNCLFFBQVFDLE1BQU0sRUFBRUMsUUFBUSxFQUFFO1FBQ3RCLElBQUlDO1FBQ0osSUFBSSxJQUFJLENBQUNiLFlBQVksQ0FBQ1csU0FBUztZQUMzQkMsU0FBUyxDQUFDQyxLQUFLLElBQUksQ0FBQ25CLFlBQVksTUFBTSxRQUFRbUIsT0FBTyxLQUFLLElBQUksS0FBSyxJQUFJQSxHQUFHQyxRQUFRO1FBQ3RGO1FBQ0EsSUFBSSxDQUFDbkIsUUFBUSxDQUFDUSxJQUFJLENBQUM7WUFBRVE7WUFBUUM7UUFBUztRQUN0QyxPQUFPLElBQUk7SUFDZjtJQUNBWCxlQUFlO1FBQ1gsSUFBSSxJQUFJLENBQUNWLFlBQVksRUFBRTtZQUNuQjtRQUNKO1FBQ0EsSUFBSSxDQUFDRSxHQUFHLEdBQUcsSUFBSSxDQUFDUCxPQUFPLENBQUNnQixNQUFNLENBQUNhLFFBQVE7UUFDdkMsSUFBSSxDQUFDbkIsUUFBUSxHQUFHLElBQUksQ0FBQ1YsT0FBTyxDQUFDOEIsZUFBZSxDQUFDLElBQUksQ0FBQ3ZCLEdBQUc7UUFDckQsTUFBTW1CLFdBQVcsQ0FBQ3hCO1lBQ2QsSUFBSSxDQUFDVSxlQUFlO1lBQ3BCLElBQUksQ0FBQ21CLGNBQWM7WUFDbkIsSUFBSSxDQUFDdkIsWUFBWSxHQUFHTjtZQUNwQixJQUFJLENBQUM4QixhQUFhLENBQUM5QjtRQUN2QjtRQUNBLElBQUksQ0FBQ0YsT0FBTyxDQUFDaUMsR0FBRyxDQUFDLElBQUksQ0FBQ3ZCLFFBQVEsRUFBRSxDQUFDLEdBQUdnQjtRQUNwQyxJQUFJLENBQUNyQixZQUFZLEdBQUc2QixXQUFXO1lBQzNCLElBQUksQ0FBQ0MsT0FBTyxDQUFDLFdBQVcsQ0FBQztRQUM3QixHQUFHLElBQUksQ0FBQ2hDLE9BQU87SUFDbkI7SUFDQWdDLFFBQVFWLE1BQU0sRUFBRUcsUUFBUSxFQUFFO1FBQ3RCLElBQUksSUFBSSxDQUFDbEIsUUFBUSxFQUNiLElBQUksQ0FBQ1YsT0FBTyxDQUFDb0MsUUFBUSxDQUFDLElBQUksQ0FBQzFCLFFBQVEsRUFBRTtZQUFFZTtZQUFRRztRQUFTO0lBQ2hFO0lBQ0FTLFVBQVU7UUFDTixJQUFJLENBQUN6QixlQUFlO1FBQ3BCLElBQUksQ0FBQ21CLGNBQWM7SUFDdkI7SUFDQW5CLGtCQUFrQjtRQUNkLElBQUksQ0FBQyxJQUFJLENBQUNGLFFBQVEsRUFBRTtZQUNoQjtRQUNKO1FBQ0EsSUFBSSxDQUFDVixPQUFPLENBQUNzQyxJQUFJLENBQUMsSUFBSSxDQUFDNUIsUUFBUSxFQUFFLENBQUM7SUFDdEM7SUFDQXFCLGlCQUFpQjtRQUNiUSxhQUFhLElBQUksQ0FBQ2xDLFlBQVk7UUFDOUIsSUFBSSxDQUFDQSxZQUFZLEdBQUdDO0lBQ3hCO0lBQ0EwQixjQUFjLEVBQUVQLE1BQU0sRUFBRUcsUUFBUSxFQUFHLEVBQUU7UUFDakMsSUFBSSxDQUFDbkIsUUFBUSxDQUNSK0IsTUFBTSxDQUFDLENBQUNDLElBQU1BLEVBQUVoQixNQUFNLEtBQUtBLFFBQzNCaUIsT0FBTyxDQUFDLENBQUNELElBQU1BLEVBQUVmLFFBQVEsQ0FBQ0U7SUFDbkM7SUFDQWQsYUFBYVcsTUFBTSxFQUFFO1FBQ2pCLE9BQU8sSUFBSSxDQUFDakIsWUFBWSxJQUFJLElBQUksQ0FBQ0EsWUFBWSxDQUFDaUIsTUFBTSxLQUFLQTtJQUM3RDtBQUNKLEVBQ0EsZ0NBQWdDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVnd2F0Y2hnb3YtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3JlYWx0aW1lLWpzQDIuMTEuMi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3JlYWx0aW1lLWpzL2Rpc3QvbW9kdWxlL2xpYi9wdXNoLmpzP2ExZTgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgREVGQVVMVF9USU1FT1VUIH0gZnJvbSAnLi4vbGliL2NvbnN0YW50cyc7XG5leHBvcnQgZGVmYXVsdCBjbGFzcyBQdXNoIHtcbiAgICAvKipcbiAgICAgKiBJbml0aWFsaXplcyB0aGUgUHVzaFxuICAgICAqXG4gICAgICogQHBhcmFtIGNoYW5uZWwgVGhlIENoYW5uZWxcbiAgICAgKiBAcGFyYW0gZXZlbnQgVGhlIGV2ZW50LCBmb3IgZXhhbXBsZSBgXCJwaHhfam9pblwiYFxuICAgICAqIEBwYXJhbSBwYXlsb2FkIFRoZSBwYXlsb2FkLCBmb3IgZXhhbXBsZSBge3VzZXJfaWQ6IDEyM31gXG4gICAgICogQHBhcmFtIHRpbWVvdXQgVGhlIHB1c2ggdGltZW91dCBpbiBtaWxsaXNlY29uZHNcbiAgICAgKi9cbiAgICBjb25zdHJ1Y3RvcihjaGFubmVsLCBldmVudCwgcGF5bG9hZCA9IHt9LCB0aW1lb3V0ID0gREVGQVVMVF9USU1FT1VUKSB7XG4gICAgICAgIHRoaXMuY2hhbm5lbCA9IGNoYW5uZWw7XG4gICAgICAgIHRoaXMuZXZlbnQgPSBldmVudDtcbiAgICAgICAgdGhpcy5wYXlsb2FkID0gcGF5bG9hZDtcbiAgICAgICAgdGhpcy50aW1lb3V0ID0gdGltZW91dDtcbiAgICAgICAgdGhpcy5zZW50ID0gZmFsc2U7XG4gICAgICAgIHRoaXMudGltZW91dFRpbWVyID0gdW5kZWZpbmVkO1xuICAgICAgICB0aGlzLnJlZiA9ICcnO1xuICAgICAgICB0aGlzLnJlY2VpdmVkUmVzcCA9IG51bGw7XG4gICAgICAgIHRoaXMucmVjSG9va3MgPSBbXTtcbiAgICAgICAgdGhpcy5yZWZFdmVudCA9IG51bGw7XG4gICAgfVxuICAgIHJlc2VuZCh0aW1lb3V0KSB7XG4gICAgICAgIHRoaXMudGltZW91dCA9IHRpbWVvdXQ7XG4gICAgICAgIHRoaXMuX2NhbmNlbFJlZkV2ZW50KCk7XG4gICAgICAgIHRoaXMucmVmID0gJyc7XG4gICAgICAgIHRoaXMucmVmRXZlbnQgPSBudWxsO1xuICAgICAgICB0aGlzLnJlY2VpdmVkUmVzcCA9IG51bGw7XG4gICAgICAgIHRoaXMuc2VudCA9IGZhbHNlO1xuICAgICAgICB0aGlzLnNlbmQoKTtcbiAgICB9XG4gICAgc2VuZCgpIHtcbiAgICAgICAgaWYgKHRoaXMuX2hhc1JlY2VpdmVkKCd0aW1lb3V0JykpIHtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLnN0YXJ0VGltZW91dCgpO1xuICAgICAgICB0aGlzLnNlbnQgPSB0cnVlO1xuICAgICAgICB0aGlzLmNoYW5uZWwuc29ja2V0LnB1c2goe1xuICAgICAgICAgICAgdG9waWM6IHRoaXMuY2hhbm5lbC50b3BpYyxcbiAgICAgICAgICAgIGV2ZW50OiB0aGlzLmV2ZW50LFxuICAgICAgICAgICAgcGF5bG9hZDogdGhpcy5wYXlsb2FkLFxuICAgICAgICAgICAgcmVmOiB0aGlzLnJlZixcbiAgICAgICAgICAgIGpvaW5fcmVmOiB0aGlzLmNoYW5uZWwuX2pvaW5SZWYoKSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIHVwZGF0ZVBheWxvYWQocGF5bG9hZCkge1xuICAgICAgICB0aGlzLnBheWxvYWQgPSBPYmplY3QuYXNzaWduKE9iamVjdC5hc3NpZ24oe30sIHRoaXMucGF5bG9hZCksIHBheWxvYWQpO1xuICAgIH1cbiAgICByZWNlaXZlKHN0YXR1cywgY2FsbGJhY2spIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICBpZiAodGhpcy5faGFzUmVjZWl2ZWQoc3RhdHVzKSkge1xuICAgICAgICAgICAgY2FsbGJhY2soKF9hID0gdGhpcy5yZWNlaXZlZFJlc3ApID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5yZXNwb25zZSk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5yZWNIb29rcy5wdXNoKHsgc3RhdHVzLCBjYWxsYmFjayB9KTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfVxuICAgIHN0YXJ0VGltZW91dCgpIHtcbiAgICAgICAgaWYgKHRoaXMudGltZW91dFRpbWVyKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5yZWYgPSB0aGlzLmNoYW5uZWwuc29ja2V0Ll9tYWtlUmVmKCk7XG4gICAgICAgIHRoaXMucmVmRXZlbnQgPSB0aGlzLmNoYW5uZWwuX3JlcGx5RXZlbnROYW1lKHRoaXMucmVmKTtcbiAgICAgICAgY29uc3QgY2FsbGJhY2sgPSAocGF5bG9hZCkgPT4ge1xuICAgICAgICAgICAgdGhpcy5fY2FuY2VsUmVmRXZlbnQoKTtcbiAgICAgICAgICAgIHRoaXMuX2NhbmNlbFRpbWVvdXQoKTtcbiAgICAgICAgICAgIHRoaXMucmVjZWl2ZWRSZXNwID0gcGF5bG9hZDtcbiAgICAgICAgICAgIHRoaXMuX21hdGNoUmVjZWl2ZShwYXlsb2FkKTtcbiAgICAgICAgfTtcbiAgICAgICAgdGhpcy5jaGFubmVsLl9vbih0aGlzLnJlZkV2ZW50LCB7fSwgY2FsbGJhY2spO1xuICAgICAgICB0aGlzLnRpbWVvdXRUaW1lciA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgdGhpcy50cmlnZ2VyKCd0aW1lb3V0Jywge30pO1xuICAgICAgICB9LCB0aGlzLnRpbWVvdXQpO1xuICAgIH1cbiAgICB0cmlnZ2VyKHN0YXR1cywgcmVzcG9uc2UpIHtcbiAgICAgICAgaWYgKHRoaXMucmVmRXZlbnQpXG4gICAgICAgICAgICB0aGlzLmNoYW5uZWwuX3RyaWdnZXIodGhpcy5yZWZFdmVudCwgeyBzdGF0dXMsIHJlc3BvbnNlIH0pO1xuICAgIH1cbiAgICBkZXN0cm95KCkge1xuICAgICAgICB0aGlzLl9jYW5jZWxSZWZFdmVudCgpO1xuICAgICAgICB0aGlzLl9jYW5jZWxUaW1lb3V0KCk7XG4gICAgfVxuICAgIF9jYW5jZWxSZWZFdmVudCgpIHtcbiAgICAgICAgaWYgKCF0aGlzLnJlZkV2ZW50KSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5jaGFubmVsLl9vZmYodGhpcy5yZWZFdmVudCwge30pO1xuICAgIH1cbiAgICBfY2FuY2VsVGltZW91dCgpIHtcbiAgICAgICAgY2xlYXJUaW1lb3V0KHRoaXMudGltZW91dFRpbWVyKTtcbiAgICAgICAgdGhpcy50aW1lb3V0VGltZXIgPSB1bmRlZmluZWQ7XG4gICAgfVxuICAgIF9tYXRjaFJlY2VpdmUoeyBzdGF0dXMsIHJlc3BvbnNlLCB9KSB7XG4gICAgICAgIHRoaXMucmVjSG9va3NcbiAgICAgICAgICAgIC5maWx0ZXIoKGgpID0+IGguc3RhdHVzID09PSBzdGF0dXMpXG4gICAgICAgICAgICAuZm9yRWFjaCgoaCkgPT4gaC5jYWxsYmFjayhyZXNwb25zZSkpO1xuICAgIH1cbiAgICBfaGFzUmVjZWl2ZWQoc3RhdHVzKSB7XG4gICAgICAgIHJldHVybiB0aGlzLnJlY2VpdmVkUmVzcCAmJiB0aGlzLnJlY2VpdmVkUmVzcC5zdGF0dXMgPT09IHN0YXR1cztcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1wdXNoLmpzLm1hcCJdLCJuYW1lcyI6WyJERUZBVUxUX1RJTUVPVVQiLCJQdXNoIiwiY29uc3RydWN0b3IiLCJjaGFubmVsIiwiZXZlbnQiLCJwYXlsb2FkIiwidGltZW91dCIsInNlbnQiLCJ0aW1lb3V0VGltZXIiLCJ1bmRlZmluZWQiLCJyZWYiLCJyZWNlaXZlZFJlc3AiLCJyZWNIb29rcyIsInJlZkV2ZW50IiwicmVzZW5kIiwiX2NhbmNlbFJlZkV2ZW50Iiwic2VuZCIsIl9oYXNSZWNlaXZlZCIsInN0YXJ0VGltZW91dCIsInNvY2tldCIsInB1c2giLCJ0b3BpYyIsImpvaW5fcmVmIiwiX2pvaW5SZWYiLCJ1cGRhdGVQYXlsb2FkIiwiT2JqZWN0IiwiYXNzaWduIiwicmVjZWl2ZSIsInN0YXR1cyIsImNhbGxiYWNrIiwiX2EiLCJyZXNwb25zZSIsIl9tYWtlUmVmIiwiX3JlcGx5RXZlbnROYW1lIiwiX2NhbmNlbFRpbWVvdXQiLCJfbWF0Y2hSZWNlaXZlIiwiX29uIiwic2V0VGltZW91dCIsInRyaWdnZXIiLCJfdHJpZ2dlciIsImRlc3Ryb3kiLCJfb2ZmIiwiY2xlYXJUaW1lb3V0IiwiZmlsdGVyIiwiaCIsImZvckVhY2giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/push.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Serializer)\n/* harmony export */ });\n// This file draws heavily from https://github.com/phoenixframework/phoenix/commit/cf098e9cf7a44ee6479d31d911a97d3c7430c6fe\n// License: https://github.com/phoenixframework/phoenix/blob/master/LICENSE.md\nclass Serializer {\n    constructor(){\n        this.HEADER_LENGTH = 1;\n    }\n    decode(rawPayload, callback) {\n        if (rawPayload.constructor === ArrayBuffer) {\n            return callback(this._binaryDecode(rawPayload));\n        }\n        if (typeof rawPayload === \"string\") {\n            return callback(JSON.parse(rawPayload));\n        }\n        return callback({});\n    }\n    _binaryDecode(buffer) {\n        const view = new DataView(buffer);\n        const decoder = new TextDecoder();\n        return this._decodeBroadcast(buffer, view, decoder);\n    }\n    _decodeBroadcast(buffer, view, decoder) {\n        const topicSize = view.getUint8(1);\n        const eventSize = view.getUint8(2);\n        let offset = this.HEADER_LENGTH + 2;\n        const topic = decoder.decode(buffer.slice(offset, offset + topicSize));\n        offset = offset + topicSize;\n        const event = decoder.decode(buffer.slice(offset, offset + eventSize));\n        offset = offset + eventSize;\n        const data = JSON.parse(decoder.decode(buffer.slice(offset, buffer.byteLength)));\n        return {\n            ref: null,\n            topic: topic,\n            event: event,\n            payload: data\n        };\n    }\n} //# sourceMappingURL=serializer.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/serializer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/timer.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/timer.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Timer)\n/* harmony export */ });\n/**\n * Creates a timer that accepts a `timerCalc` function to perform calculated timeout retries, such as exponential backoff.\n *\n * @example\n *    let reconnectTimer = new Timer(() => this.connect(), function(tries){\n *      return [1000, 5000, 10000][tries - 1] || 10000\n *    })\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n *    reconnectTimer.scheduleTimeout() // fires after 5000\n *    reconnectTimer.reset()\n *    reconnectTimer.scheduleTimeout() // fires after 1000\n */ class Timer {\n    constructor(callback, timerCalc){\n        this.callback = callback;\n        this.timerCalc = timerCalc;\n        this.timer = undefined;\n        this.tries = 0;\n        this.callback = callback;\n        this.timerCalc = timerCalc;\n    }\n    reset() {\n        this.tries = 0;\n        clearTimeout(this.timer);\n    }\n    // Cancels any previous scheduleTimeout and schedules callback\n    scheduleTimeout() {\n        clearTimeout(this.timer);\n        this.timer = setTimeout(()=>{\n            this.tries = this.tries + 1;\n            this.callback();\n        }, this.timerCalc(this.tries + 1));\n    }\n} //# sourceMappingURL=timer.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/timer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PostgresTypes: () => (/* binding */ PostgresTypes),\n/* harmony export */   convertCell: () => (/* binding */ convertCell),\n/* harmony export */   convertChangeData: () => (/* binding */ convertChangeData),\n/* harmony export */   convertColumn: () => (/* binding */ convertColumn),\n/* harmony export */   httpEndpointURL: () => (/* binding */ httpEndpointURL),\n/* harmony export */   toArray: () => (/* binding */ toArray),\n/* harmony export */   toBoolean: () => (/* binding */ toBoolean),\n/* harmony export */   toJson: () => (/* binding */ toJson),\n/* harmony export */   toNumber: () => (/* binding */ toNumber),\n/* harmony export */   toTimestampString: () => (/* binding */ toTimestampString)\n/* harmony export */ });\n/**\n * Helpers to convert the change Payload into native JS types.\n */ // Adapted from epgsql (src/epgsql_binary.erl), this module licensed under\n// 3-clause BSD found here: https://raw.githubusercontent.com/epgsql/epgsql/devel/LICENSE\nvar PostgresTypes;\n(function(PostgresTypes) {\n    PostgresTypes[\"abstime\"] = \"abstime\";\n    PostgresTypes[\"bool\"] = \"bool\";\n    PostgresTypes[\"date\"] = \"date\";\n    PostgresTypes[\"daterange\"] = \"daterange\";\n    PostgresTypes[\"float4\"] = \"float4\";\n    PostgresTypes[\"float8\"] = \"float8\";\n    PostgresTypes[\"int2\"] = \"int2\";\n    PostgresTypes[\"int4\"] = \"int4\";\n    PostgresTypes[\"int4range\"] = \"int4range\";\n    PostgresTypes[\"int8\"] = \"int8\";\n    PostgresTypes[\"int8range\"] = \"int8range\";\n    PostgresTypes[\"json\"] = \"json\";\n    PostgresTypes[\"jsonb\"] = \"jsonb\";\n    PostgresTypes[\"money\"] = \"money\";\n    PostgresTypes[\"numeric\"] = \"numeric\";\n    PostgresTypes[\"oid\"] = \"oid\";\n    PostgresTypes[\"reltime\"] = \"reltime\";\n    PostgresTypes[\"text\"] = \"text\";\n    PostgresTypes[\"time\"] = \"time\";\n    PostgresTypes[\"timestamp\"] = \"timestamp\";\n    PostgresTypes[\"timestamptz\"] = \"timestamptz\";\n    PostgresTypes[\"timetz\"] = \"timetz\";\n    PostgresTypes[\"tsrange\"] = \"tsrange\";\n    PostgresTypes[\"tstzrange\"] = \"tstzrange\";\n})(PostgresTypes || (PostgresTypes = {}));\n/**\n * Takes an array of columns and an object of string values then converts each string value\n * to its mapped type.\n *\n * @param {{name: String, type: String}[]} columns\n * @param {Object} record\n * @param {Object} options The map of various options that can be applied to the mapper\n * @param {Array} options.skipTypes The array of types that should not be converted\n *\n * @example convertChangeData([{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age:'33'}, {})\n * //=>{ first_name: 'Paul', age: 33 }\n */ const convertChangeData = (columns, record, options = {})=>{\n    var _a;\n    const skipTypes = (_a = options.skipTypes) !== null && _a !== void 0 ? _a : [];\n    return Object.keys(record).reduce((acc, rec_key)=>{\n        acc[rec_key] = convertColumn(rec_key, columns, record, skipTypes);\n        return acc;\n    }, {});\n};\n/**\n * Converts the value of an individual column.\n *\n * @param {String} columnName The column that you want to convert\n * @param {{name: String, type: String}[]} columns All of the columns\n * @param {Object} record The map of string values\n * @param {Array} skipTypes An array of types that should not be converted\n * @return {object} Useless information\n *\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, [])\n * //=> 33\n * @example convertColumn('age', [{name: 'first_name', type: 'text'}, {name: 'age', type: 'int4'}], {first_name: 'Paul', age: '33'}, ['int4'])\n * //=> \"33\"\n */ const convertColumn = (columnName, columns, record, skipTypes)=>{\n    const column = columns.find((x)=>x.name === columnName);\n    const colType = column === null || column === void 0 ? void 0 : column.type;\n    const value = record[columnName];\n    if (colType && !skipTypes.includes(colType)) {\n        return convertCell(colType, value);\n    }\n    return noop(value);\n};\n/**\n * If the value of the cell is `null`, returns null.\n * Otherwise converts the string value to the correct type.\n * @param {String} type A postgres column type\n * @param {String} value The cell value\n *\n * @example convertCell('bool', 't')\n * //=> true\n * @example convertCell('int8', '10')\n * //=> 10\n * @example convertCell('_int4', '{1,2,3,4}')\n * //=> [1,2,3,4]\n */ const convertCell = (type, value)=>{\n    // if data type is an array\n    if (type.charAt(0) === \"_\") {\n        const dataType = type.slice(1, type.length);\n        return toArray(value, dataType);\n    }\n    // If not null, convert to correct type.\n    switch(type){\n        case PostgresTypes.bool:\n            return toBoolean(value);\n        case PostgresTypes.float4:\n        case PostgresTypes.float8:\n        case PostgresTypes.int2:\n        case PostgresTypes.int4:\n        case PostgresTypes.int8:\n        case PostgresTypes.numeric:\n        case PostgresTypes.oid:\n            return toNumber(value);\n        case PostgresTypes.json:\n        case PostgresTypes.jsonb:\n            return toJson(value);\n        case PostgresTypes.timestamp:\n            return toTimestampString(value); // Format to be consistent with PostgREST\n        case PostgresTypes.abstime:\n        case PostgresTypes.date:\n        case PostgresTypes.daterange:\n        case PostgresTypes.int4range:\n        case PostgresTypes.int8range:\n        case PostgresTypes.money:\n        case PostgresTypes.reltime:\n        case PostgresTypes.text:\n        case PostgresTypes.time:\n        case PostgresTypes.timestamptz:\n        case PostgresTypes.timetz:\n        case PostgresTypes.tsrange:\n        case PostgresTypes.tstzrange:\n            return noop(value);\n        default:\n            // Return the value for remaining types\n            return noop(value);\n    }\n};\nconst noop = (value)=>{\n    return value;\n};\nconst toBoolean = (value)=>{\n    switch(value){\n        case \"t\":\n            return true;\n        case \"f\":\n            return false;\n        default:\n            return value;\n    }\n};\nconst toNumber = (value)=>{\n    if (typeof value === \"string\") {\n        const parsedValue = parseFloat(value);\n        if (!Number.isNaN(parsedValue)) {\n            return parsedValue;\n        }\n    }\n    return value;\n};\nconst toJson = (value)=>{\n    if (typeof value === \"string\") {\n        try {\n            return JSON.parse(value);\n        } catch (error) {\n            console.log(`JSON parse error: ${error}`);\n            return value;\n        }\n    }\n    return value;\n};\n/**\n * Converts a Postgres Array into a native JS array\n *\n * @example toArray('{}', 'int4')\n * //=> []\n * @example toArray('{\"[2021-01-01,2021-12-31)\",\"(2021-01-01,2021-12-32]\"}', 'daterange')\n * //=> ['[2021-01-01,2021-12-31)', '(2021-01-01,2021-12-32]']\n * @example toArray([1,2,3,4], 'int4')\n * //=> [1,2,3,4]\n */ const toArray = (value, type)=>{\n    if (typeof value !== \"string\") {\n        return value;\n    }\n    const lastIdx = value.length - 1;\n    const closeBrace = value[lastIdx];\n    const openBrace = value[0];\n    // Confirm value is a Postgres array by checking curly brackets\n    if (openBrace === \"{\" && closeBrace === \"}\") {\n        let arr;\n        const valTrim = value.slice(1, lastIdx);\n        // TODO: find a better solution to separate Postgres array data\n        try {\n            arr = JSON.parse(\"[\" + valTrim + \"]\");\n        } catch (_) {\n            // WARNING: splitting on comma does not cover all edge cases\n            arr = valTrim ? valTrim.split(\",\") : [];\n        }\n        return arr.map((val)=>convertCell(type, val));\n    }\n    return value;\n};\n/**\n * Fixes timestamp to be ISO-8601. Swaps the space between the date and time for a 'T'\n * See https://github.com/supabase/supabase/issues/18\n *\n * @example toTimestampString('2019-09-10 00:00:00')\n * //=> '2019-09-10T00:00:00'\n */ const toTimestampString = (value)=>{\n    if (typeof value === \"string\") {\n        return value.replace(\" \", \"T\");\n    }\n    return value;\n};\nconst httpEndpointURL = (socketUrl)=>{\n    let url = socketUrl;\n    url = url.replace(/^ws/i, \"http\");\n    url = url.replace(/(\\/socket\\/websocket|\\/socket|\\/websocket)\\/?$/i, \"\");\n    return url.replace(/\\/+$/, \"\");\n}; //# sourceMappingURL=transformers.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/transformers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/version.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/version.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = \"2.11.2\"; //# sourceMappingURL=version.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3JlYWx0aW1lLWpzQDIuMTEuMi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3JlYWx0aW1lLWpzL2Rpc3QvbW9kdWxlL2xpYi92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxNQUFNQSxVQUFVLFNBQVMsQ0FDaEMsbUNBQW1DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVnd2F0Y2hnb3YtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3JlYWx0aW1lLWpzQDIuMTEuMi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3JlYWx0aW1lLWpzL2Rpc3QvbW9kdWxlL2xpYi92ZXJzaW9uLmpzPzdhNDgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHZlcnNpb24gPSAnMi4xMS4yJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbInZlcnNpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.2/node_modules/@supabase/realtime-js/dist/module/lib/version.js\n");

/***/ })

};
;