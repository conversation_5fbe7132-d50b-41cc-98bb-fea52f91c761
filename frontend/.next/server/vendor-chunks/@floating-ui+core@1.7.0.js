"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@floating-ui+core@1.7.0";
exports.ids = ["vendor-chunks/@floating-ui+core@1.7.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@floating-ui+core@1.7.0/node_modules/@floating-ui/core/dist/floating-ui.core.mjs":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@floating-ui+core@1.7.0/node_modules/@floating-ui/core/dist/floating-ui.core.mjs ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrow: () => (/* binding */ arrow),\n/* harmony export */   autoPlacement: () => (/* binding */ autoPlacement),\n/* harmony export */   computePosition: () => (/* binding */ computePosition),\n/* harmony export */   detectOverflow: () => (/* binding */ detectOverflow),\n/* harmony export */   flip: () => (/* binding */ flip),\n/* harmony export */   hide: () => (/* binding */ hide),\n/* harmony export */   inline: () => (/* binding */ inline),\n/* harmony export */   limitShift: () => (/* binding */ limitShift),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   rectToClientRect: () => (/* reexport safe */ _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect),\n/* harmony export */   shift: () => (/* binding */ shift),\n/* harmony export */   size: () => (/* binding */ size)\n/* harmony export */ });\n/* harmony import */ var _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @floating-ui/utils */ \"(ssr)/./node_modules/.pnpm/@floating-ui+utils@0.2.9/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\");\n\n\nfunction computeCoordsFromPlacement(_ref, placement, rtl) {\n    let { reference, floating } = _ref;\n    const sideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement);\n    const alignmentAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentAxis)(placement);\n    const alignLength = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAxisLength)(alignmentAxis);\n    const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n    const isVertical = sideAxis === \"y\";\n    const commonX = reference.x + reference.width / 2 - floating.width / 2;\n    const commonY = reference.y + reference.height / 2 - floating.height / 2;\n    const commonAlign = reference[alignLength] / 2 - floating[alignLength] / 2;\n    let coords;\n    switch(side){\n        case \"top\":\n            coords = {\n                x: commonX,\n                y: reference.y - floating.height\n            };\n            break;\n        case \"bottom\":\n            coords = {\n                x: commonX,\n                y: reference.y + reference.height\n            };\n            break;\n        case \"right\":\n            coords = {\n                x: reference.x + reference.width,\n                y: commonY\n            };\n            break;\n        case \"left\":\n            coords = {\n                x: reference.x - floating.width,\n                y: commonY\n            };\n            break;\n        default:\n            coords = {\n                x: reference.x,\n                y: reference.y\n            };\n    }\n    switch((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement)){\n        case \"start\":\n            coords[alignmentAxis] -= commonAlign * (rtl && isVertical ? -1 : 1);\n            break;\n        case \"end\":\n            coords[alignmentAxis] += commonAlign * (rtl && isVertical ? -1 : 1);\n            break;\n    }\n    return coords;\n}\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n *\n * This export does not have any `platform` interface logic. You will need to\n * write one for the platform you are using Floating UI with.\n */ const computePosition = async (reference, floating, config)=>{\n    const { placement = \"bottom\", strategy = \"absolute\", middleware = [], platform } = config;\n    const validMiddleware = middleware.filter(Boolean);\n    const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(floating));\n    let rects = await platform.getElementRects({\n        reference,\n        floating,\n        strategy\n    });\n    let { x, y } = computeCoordsFromPlacement(rects, placement, rtl);\n    let statefulPlacement = placement;\n    let middlewareData = {};\n    let resetCount = 0;\n    for(let i = 0; i < validMiddleware.length; i++){\n        const { name, fn } = validMiddleware[i];\n        const { x: nextX, y: nextY, data, reset } = await fn({\n            x,\n            y,\n            initialPlacement: placement,\n            placement: statefulPlacement,\n            strategy,\n            middlewareData,\n            rects,\n            platform,\n            elements: {\n                reference,\n                floating\n            }\n        });\n        x = nextX != null ? nextX : x;\n        y = nextY != null ? nextY : y;\n        middlewareData = {\n            ...middlewareData,\n            [name]: {\n                ...middlewareData[name],\n                ...data\n            }\n        };\n        if (reset && resetCount <= 50) {\n            resetCount++;\n            if (typeof reset === \"object\") {\n                if (reset.placement) {\n                    statefulPlacement = reset.placement;\n                }\n                if (reset.rects) {\n                    rects = reset.rects === true ? await platform.getElementRects({\n                        reference,\n                        floating,\n                        strategy\n                    }) : reset.rects;\n                }\n                ({ x, y } = computeCoordsFromPlacement(rects, statefulPlacement, rtl));\n            }\n            i = -1;\n        }\n    }\n    return {\n        x,\n        y,\n        placement: statefulPlacement,\n        strategy,\n        middlewareData\n    };\n};\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */ async function detectOverflow(state, options) {\n    var _await$platform$isEle;\n    if (options === void 0) {\n        options = {};\n    }\n    const { x, y, platform, rects, elements, strategy } = state;\n    const { boundary = \"clippingAncestors\", rootBoundary = \"viewport\", elementContext = \"floating\", altBoundary = false, padding = 0 } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n    const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n    const altContext = elementContext === \"floating\" ? \"reference\" : \"floating\";\n    const element = elements[altBoundary ? altContext : elementContext];\n    const clippingClientRect = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(await platform.getClippingRect({\n        element: ((_await$platform$isEle = await (platform.isElement == null ? void 0 : platform.isElement(element))) != null ? _await$platform$isEle : true) ? element : element.contextElement || await (platform.getDocumentElement == null ? void 0 : platform.getDocumentElement(elements.floating)),\n        boundary,\n        rootBoundary,\n        strategy\n    }));\n    const rect = elementContext === \"floating\" ? {\n        x,\n        y,\n        width: rects.floating.width,\n        height: rects.floating.height\n    } : rects.reference;\n    const offsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(elements.floating));\n    const offsetScale = await (platform.isElement == null ? void 0 : platform.isElement(offsetParent)) ? await (platform.getScale == null ? void 0 : platform.getScale(offsetParent)) || {\n        x: 1,\n        y: 1\n    } : {\n        x: 1,\n        y: 1\n    };\n    const elementClientRect = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(platform.convertOffsetParentRelativeRectToViewportRelativeRect ? await platform.convertOffsetParentRelativeRectToViewportRelativeRect({\n        elements,\n        rect,\n        offsetParent,\n        strategy\n    }) : rect);\n    return {\n        top: (clippingClientRect.top - elementClientRect.top + paddingObject.top) / offsetScale.y,\n        bottom: (elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom) / offsetScale.y,\n        left: (clippingClientRect.left - elementClientRect.left + paddingObject.left) / offsetScale.x,\n        right: (elementClientRect.right - clippingClientRect.right + paddingObject.right) / offsetScale.x\n    };\n}\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */ const arrow = (options)=>({\n        name: \"arrow\",\n        options,\n        async fn (state) {\n            const { x, y, placement, rects, platform, elements, middlewareData } = state;\n            // Since `element` is required, we don't Partial<> the type.\n            const { element, padding = 0 } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state) || {};\n            if (element == null) {\n                return {};\n            }\n            const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n            const coords = {\n                x,\n                y\n            };\n            const axis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentAxis)(placement);\n            const length = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAxisLength)(axis);\n            const arrowDimensions = await platform.getDimensions(element);\n            const isYAxis = axis === \"y\";\n            const minProp = isYAxis ? \"top\" : \"left\";\n            const maxProp = isYAxis ? \"bottom\" : \"right\";\n            const clientProp = isYAxis ? \"clientHeight\" : \"clientWidth\";\n            const endDiff = rects.reference[length] + rects.reference[axis] - coords[axis] - rects.floating[length];\n            const startDiff = coords[axis] - rects.reference[axis];\n            const arrowOffsetParent = await (platform.getOffsetParent == null ? void 0 : platform.getOffsetParent(element));\n            let clientSize = arrowOffsetParent ? arrowOffsetParent[clientProp] : 0;\n            // DOM platform can return `window` as the `offsetParent`.\n            if (!clientSize || !await (platform.isElement == null ? void 0 : platform.isElement(arrowOffsetParent))) {\n                clientSize = elements.floating[clientProp] || rects.floating[length];\n            }\n            const centerToReference = endDiff / 2 - startDiff / 2;\n            // If the padding is large enough that it causes the arrow to no longer be\n            // centered, modify the padding so that it is centered.\n            const largestPossiblePadding = clientSize / 2 - arrowDimensions[length] / 2 - 1;\n            const minPadding = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(paddingObject[minProp], largestPossiblePadding);\n            const maxPadding = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(paddingObject[maxProp], largestPossiblePadding);\n            // Make sure the arrow doesn't overflow the floating element if the center\n            // point is outside the floating element's bounds.\n            const min$1 = minPadding;\n            const max = clientSize - arrowDimensions[length] - maxPadding;\n            const center = clientSize / 2 - arrowDimensions[length] / 2 + centerToReference;\n            const offset = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min$1, center, max);\n            // If the reference is small enough that the arrow's padding causes it to\n            // to point to nothing for an aligned placement, adjust the offset of the\n            // floating element itself. To ensure `shift()` continues to take action,\n            // a single reset is performed when this is true.\n            const shouldAddOffset = !middlewareData.arrow && (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) != null && center !== offset && rects.reference[length] / 2 - (center < min$1 ? minPadding : maxPadding) - arrowDimensions[length] / 2 < 0;\n            const alignmentOffset = shouldAddOffset ? center < min$1 ? center - min$1 : center - max : 0;\n            return {\n                [axis]: coords[axis] + alignmentOffset,\n                data: {\n                    [axis]: offset,\n                    centerOffset: center - offset - alignmentOffset,\n                    ...shouldAddOffset && {\n                        alignmentOffset\n                    }\n                },\n                reset: shouldAddOffset\n            };\n        }\n    });\nfunction getPlacementList(alignment, autoAlignment, allowedPlacements) {\n    const allowedPlacementsSortedByAlignment = alignment ? [\n        ...allowedPlacements.filter((placement)=>(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) === alignment),\n        ...allowedPlacements.filter((placement)=>(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) !== alignment)\n    ] : allowedPlacements.filter((placement)=>(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === placement);\n    return allowedPlacementsSortedByAlignment.filter((placement)=>{\n        if (alignment) {\n            return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement) === alignment || (autoAlignment ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAlignmentPlacement)(placement) !== placement : false);\n        }\n        return true;\n    });\n}\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */ const autoPlacement = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"autoPlacement\",\n        options,\n        async fn (state) {\n            var _middlewareData$autoP, _middlewareData$autoP2, _placementsThatFitOnE;\n            const { rects, middlewareData, placement, platform, elements } = state;\n            const { crossAxis = false, alignment, allowedPlacements = _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.placements, autoAlignment = true, ...detectOverflowOptions } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            const placements$1 = alignment !== undefined || allowedPlacements === _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.placements ? getPlacementList(alignment || null, autoAlignment, allowedPlacements) : allowedPlacements;\n            const overflow = await detectOverflow(state, detectOverflowOptions);\n            const currentIndex = ((_middlewareData$autoP = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP.index) || 0;\n            const currentPlacement = placements$1[currentIndex];\n            if (currentPlacement == null) {\n                return {};\n            }\n            const alignmentSides = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentSides)(currentPlacement, rects, await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)));\n            // Make `computeCoords` start from the right place.\n            if (placement !== currentPlacement) {\n                return {\n                    reset: {\n                        placement: placements$1[0]\n                    }\n                };\n            }\n            const currentOverflows = [\n                overflow[(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(currentPlacement)],\n                overflow[alignmentSides[0]],\n                overflow[alignmentSides[1]]\n            ];\n            const allOverflows = [\n                ...((_middlewareData$autoP2 = middlewareData.autoPlacement) == null ? void 0 : _middlewareData$autoP2.overflows) || [],\n                {\n                    placement: currentPlacement,\n                    overflows: currentOverflows\n                }\n            ];\n            const nextPlacement = placements$1[currentIndex + 1];\n            // There are more placements to check.\n            if (nextPlacement) {\n                return {\n                    data: {\n                        index: currentIndex + 1,\n                        overflows: allOverflows\n                    },\n                    reset: {\n                        placement: nextPlacement\n                    }\n                };\n            }\n            const placementsSortedByMostSpace = allOverflows.map((d)=>{\n                const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(d.placement);\n                return [\n                    d.placement,\n                    alignment && crossAxis ? // Check along the mainAxis and main crossAxis side.\n                    d.overflows.slice(0, 2).reduce((acc, v)=>acc + v, 0) : // Check only the mainAxis.\n                    d.overflows[0],\n                    d.overflows\n                ];\n            }).sort((a, b)=>a[1] - b[1]);\n            const placementsThatFitOnEachSide = placementsSortedByMostSpace.filter((d)=>d[2].slice(0, // Aligned placements should not check their opposite crossAxis\n                // side.\n                (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(d[0]) ? 2 : 3).every((v)=>v <= 0));\n            const resetPlacement = ((_placementsThatFitOnE = placementsThatFitOnEachSide[0]) == null ? void 0 : _placementsThatFitOnE[0]) || placementsSortedByMostSpace[0][0];\n            if (resetPlacement !== placement) {\n                return {\n                    data: {\n                        index: currentIndex + 1,\n                        overflows: allOverflows\n                    },\n                    reset: {\n                        placement: resetPlacement\n                    }\n                };\n            }\n            return {};\n        }\n    };\n};\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */ const flip = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"flip\",\n        options,\n        async fn (state) {\n            var _middlewareData$arrow, _middlewareData$flip;\n            const { placement, middlewareData, rects, initialPlacement, platform, elements } = state;\n            const { mainAxis: checkMainAxis = true, crossAxis: checkCrossAxis = true, fallbackPlacements: specifiedFallbackPlacements, fallbackStrategy = \"bestFit\", fallbackAxisSideDirection = \"none\", flipAlignment = true, ...detectOverflowOptions } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            // If a reset by the arrow was caused due to an alignment offset being\n            // added, we should skip any logic now since `flip()` has already done its\n            // work.\n            // https://github.com/floating-ui/floating-ui/issues/2549#issuecomment-1719601643\n            if ((_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n                return {};\n            }\n            const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n            const initialSideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(initialPlacement);\n            const isBasePlacement = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(initialPlacement) === initialPlacement;\n            const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n            const fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipAlignment ? [\n                (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositePlacement)(initialPlacement)\n            ] : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getExpandedPlacements)(initialPlacement));\n            const hasFallbackAxisSideDirection = fallbackAxisSideDirection !== \"none\";\n            if (!specifiedFallbackPlacements && hasFallbackAxisSideDirection) {\n                fallbackPlacements.push(...(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxisPlacements)(initialPlacement, flipAlignment, fallbackAxisSideDirection, rtl));\n            }\n            const placements = [\n                initialPlacement,\n                ...fallbackPlacements\n            ];\n            const overflow = await detectOverflow(state, detectOverflowOptions);\n            const overflows = [];\n            let overflowsData = ((_middlewareData$flip = middlewareData.flip) == null ? void 0 : _middlewareData$flip.overflows) || [];\n            if (checkMainAxis) {\n                overflows.push(overflow[side]);\n            }\n            if (checkCrossAxis) {\n                const sides = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignmentSides)(placement, rects, rtl);\n                overflows.push(overflow[sides[0]], overflow[sides[1]]);\n            }\n            overflowsData = [\n                ...overflowsData,\n                {\n                    placement,\n                    overflows\n                }\n            ];\n            // One or more sides is overflowing.\n            if (!overflows.every((side)=>side <= 0)) {\n                var _middlewareData$flip2, _overflowsData$filter;\n                const nextIndex = (((_middlewareData$flip2 = middlewareData.flip) == null ? void 0 : _middlewareData$flip2.index) || 0) + 1;\n                const nextPlacement = placements[nextIndex];\n                if (nextPlacement) {\n                    var _overflowsData$;\n                    const ignoreCrossAxisOverflow = checkCrossAxis === \"alignment\" ? initialSideAxis !== (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(nextPlacement) : false;\n                    const hasInitialMainAxisOverflow = ((_overflowsData$ = overflowsData[0]) == null ? void 0 : _overflowsData$.overflows[0]) > 0;\n                    if (!ignoreCrossAxisOverflow || hasInitialMainAxisOverflow) {\n                        // Try next placement and re-run the lifecycle.\n                        return {\n                            data: {\n                                index: nextIndex,\n                                overflows: overflowsData\n                            },\n                            reset: {\n                                placement: nextPlacement\n                            }\n                        };\n                    }\n                }\n                // First, find the candidates that fit on the mainAxis side of overflow,\n                // then find the placement that fits the best on the main crossAxis side.\n                let resetPlacement = (_overflowsData$filter = overflowsData.filter((d)=>d.overflows[0] <= 0).sort((a, b)=>a.overflows[1] - b.overflows[1])[0]) == null ? void 0 : _overflowsData$filter.placement;\n                // Otherwise fallback.\n                if (!resetPlacement) {\n                    switch(fallbackStrategy){\n                        case \"bestFit\":\n                            {\n                                var _overflowsData$filter2;\n                                const placement = (_overflowsData$filter2 = overflowsData.filter((d)=>{\n                                    if (hasFallbackAxisSideDirection) {\n                                        const currentSideAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(d.placement);\n                                        return currentSideAxis === initialSideAxis || // Create a bias to the `y` side axis due to horizontal\n                                        // reading directions favoring greater width.\n                                        currentSideAxis === \"y\";\n                                    }\n                                    return true;\n                                }).map((d)=>[\n                                        d.placement,\n                                        d.overflows.filter((overflow)=>overflow > 0).reduce((acc, overflow)=>acc + overflow, 0)\n                                    ]).sort((a, b)=>a[1] - b[1])[0]) == null ? void 0 : _overflowsData$filter2[0];\n                                if (placement) {\n                                    resetPlacement = placement;\n                                }\n                                break;\n                            }\n                        case \"initialPlacement\":\n                            resetPlacement = initialPlacement;\n                            break;\n                    }\n                }\n                if (placement !== resetPlacement) {\n                    return {\n                        reset: {\n                            placement: resetPlacement\n                        }\n                    };\n                }\n            }\n            return {};\n        }\n    };\n};\nfunction getSideOffsets(overflow, rect) {\n    return {\n        top: overflow.top - rect.height,\n        right: overflow.right - rect.width,\n        bottom: overflow.bottom - rect.height,\n        left: overflow.left - rect.width\n    };\n}\nfunction isAnySideFullyClipped(overflow) {\n    return _floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.sides.some((side)=>overflow[side] >= 0);\n}\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */ const hide = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"hide\",\n        options,\n        async fn (state) {\n            const { rects } = state;\n            const { strategy = \"referenceHidden\", ...detectOverflowOptions } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            switch(strategy){\n                case \"referenceHidden\":\n                    {\n                        const overflow = await detectOverflow(state, {\n                            ...detectOverflowOptions,\n                            elementContext: \"reference\"\n                        });\n                        const offsets = getSideOffsets(overflow, rects.reference);\n                        return {\n                            data: {\n                                referenceHiddenOffsets: offsets,\n                                referenceHidden: isAnySideFullyClipped(offsets)\n                            }\n                        };\n                    }\n                case \"escaped\":\n                    {\n                        const overflow = await detectOverflow(state, {\n                            ...detectOverflowOptions,\n                            altBoundary: true\n                        });\n                        const offsets = getSideOffsets(overflow, rects.floating);\n                        return {\n                            data: {\n                                escapedOffsets: offsets,\n                                escaped: isAnySideFullyClipped(offsets)\n                            }\n                        };\n                    }\n                default:\n                    {\n                        return {};\n                    }\n            }\n        }\n    };\n};\nfunction getBoundingRect(rects) {\n    const minX = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...rects.map((rect)=>rect.left));\n    const minY = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...rects.map((rect)=>rect.top));\n    const maxX = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...rects.map((rect)=>rect.right));\n    const maxY = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...rects.map((rect)=>rect.bottom));\n    return {\n        x: minX,\n        y: minY,\n        width: maxX - minX,\n        height: maxY - minY\n    };\n}\nfunction getRectsByLine(rects) {\n    const sortedRects = rects.slice().sort((a, b)=>a.y - b.y);\n    const groups = [];\n    let prevRect = null;\n    for(let i = 0; i < sortedRects.length; i++){\n        const rect = sortedRects[i];\n        if (!prevRect || rect.y - prevRect.y > prevRect.height / 2) {\n            groups.push([\n                rect\n            ]);\n        } else {\n            groups[groups.length - 1].push(rect);\n        }\n        prevRect = rect;\n    }\n    return groups.map((rect)=>(0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(getBoundingRect(rect)));\n}\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */ const inline = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"inline\",\n        options,\n        async fn (state) {\n            const { placement, elements, rects, platform, strategy } = state;\n            // A MouseEvent's client{X,Y} coords can be up to 2 pixels off a\n            // ClientRect's bounds, despite the event listener being triggered. A\n            // padding of 2 seems to handle this issue.\n            const { padding = 2, x, y } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            const nativeClientRects = Array.from(await (platform.getClientRects == null ? void 0 : platform.getClientRects(elements.reference)) || []);\n            const clientRects = getRectsByLine(nativeClientRects);\n            const fallback = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.rectToClientRect)(getBoundingRect(nativeClientRects));\n            const paddingObject = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getPaddingObject)(padding);\n            function getBoundingClientRect() {\n                // There are two rects and they are disjoined.\n                if (clientRects.length === 2 && clientRects[0].left > clientRects[1].right && x != null && y != null) {\n                    // Find the first rect in which the point is fully inside.\n                    return clientRects.find((rect)=>x > rect.left - paddingObject.left && x < rect.right + paddingObject.right && y > rect.top - paddingObject.top && y < rect.bottom + paddingObject.bottom) || fallback;\n                }\n                // There are 2 or more connected rects.\n                if (clientRects.length >= 2) {\n                    if ((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === \"y\") {\n                        const firstRect = clientRects[0];\n                        const lastRect = clientRects[clientRects.length - 1];\n                        const isTop = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === \"top\";\n                        const top = firstRect.top;\n                        const bottom = lastRect.bottom;\n                        const left = isTop ? firstRect.left : lastRect.left;\n                        const right = isTop ? firstRect.right : lastRect.right;\n                        const width = right - left;\n                        const height = bottom - top;\n                        return {\n                            top,\n                            bottom,\n                            left,\n                            right,\n                            width,\n                            height,\n                            x: left,\n                            y: top\n                        };\n                    }\n                    const isLeftSide = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement) === \"left\";\n                    const maxRight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(...clientRects.map((rect)=>rect.right));\n                    const minLeft = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(...clientRects.map((rect)=>rect.left));\n                    const measureRects = clientRects.filter((rect)=>isLeftSide ? rect.left === minLeft : rect.right === maxRight);\n                    const top = measureRects[0].top;\n                    const bottom = measureRects[measureRects.length - 1].bottom;\n                    const left = minLeft;\n                    const right = maxRight;\n                    const width = right - left;\n                    const height = bottom - top;\n                    return {\n                        top,\n                        bottom,\n                        left,\n                        right,\n                        width,\n                        height,\n                        x: left,\n                        y: top\n                    };\n                }\n                return fallback;\n            }\n            const resetRects = await platform.getElementRects({\n                reference: {\n                    getBoundingClientRect\n                },\n                floating: elements.floating,\n                strategy\n            });\n            if (rects.reference.x !== resetRects.reference.x || rects.reference.y !== resetRects.reference.y || rects.reference.width !== resetRects.reference.width || rects.reference.height !== resetRects.reference.height) {\n                return {\n                    reset: {\n                        rects: resetRects\n                    }\n                };\n            }\n            return {};\n        }\n    };\n};\n// For type backwards-compatibility, the `OffsetOptions` type was also\n// Derivable.\nasync function convertValueToCoords(state, options) {\n    const { placement, platform, elements } = state;\n    const rtl = await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating));\n    const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n    const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement);\n    const isVertical = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === \"y\";\n    const mainAxisMulti = [\n        \"left\",\n        \"top\"\n    ].includes(side) ? -1 : 1;\n    const crossAxisMulti = rtl && isVertical ? -1 : 1;\n    const rawValue = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n    // eslint-disable-next-line prefer-const\n    let { mainAxis, crossAxis, alignmentAxis } = typeof rawValue === \"number\" ? {\n        mainAxis: rawValue,\n        crossAxis: 0,\n        alignmentAxis: null\n    } : {\n        mainAxis: rawValue.mainAxis || 0,\n        crossAxis: rawValue.crossAxis || 0,\n        alignmentAxis: rawValue.alignmentAxis\n    };\n    if (alignment && typeof alignmentAxis === \"number\") {\n        crossAxis = alignment === \"end\" ? alignmentAxis * -1 : alignmentAxis;\n    }\n    return isVertical ? {\n        x: crossAxis * crossAxisMulti,\n        y: mainAxis * mainAxisMulti\n    } : {\n        x: mainAxis * mainAxisMulti,\n        y: crossAxis * crossAxisMulti\n    };\n}\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */ const offset = function(options) {\n    if (options === void 0) {\n        options = 0;\n    }\n    return {\n        name: \"offset\",\n        options,\n        async fn (state) {\n            var _middlewareData$offse, _middlewareData$arrow;\n            const { x, y, placement, middlewareData } = state;\n            const diffCoords = await convertValueToCoords(state, options);\n            // If the placement is the same and the arrow caused an alignment offset\n            // then we don't need to change the positioning coordinates.\n            if (placement === ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse.placement) && (_middlewareData$arrow = middlewareData.arrow) != null && _middlewareData$arrow.alignmentOffset) {\n                return {};\n            }\n            return {\n                x: x + diffCoords.x,\n                y: y + diffCoords.y,\n                data: {\n                    ...diffCoords,\n                    placement\n                }\n            };\n        }\n    };\n};\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */ const shift = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"shift\",\n        options,\n        async fn (state) {\n            const { x, y, placement } = state;\n            const { mainAxis: checkMainAxis = true, crossAxis: checkCrossAxis = false, limiter = {\n                fn: (_ref)=>{\n                    let { x, y } = _ref;\n                    return {\n                        x,\n                        y\n                    };\n                }\n            }, ...detectOverflowOptions } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            const coords = {\n                x,\n                y\n            };\n            const overflow = await detectOverflow(state, detectOverflowOptions);\n            const crossAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement));\n            const mainAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxis)(crossAxis);\n            let mainAxisCoord = coords[mainAxis];\n            let crossAxisCoord = coords[crossAxis];\n            if (checkMainAxis) {\n                const minSide = mainAxis === \"y\" ? \"top\" : \"left\";\n                const maxSide = mainAxis === \"y\" ? \"bottom\" : \"right\";\n                const min = mainAxisCoord + overflow[minSide];\n                const max = mainAxisCoord - overflow[maxSide];\n                mainAxisCoord = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min, mainAxisCoord, max);\n            }\n            if (checkCrossAxis) {\n                const minSide = crossAxis === \"y\" ? \"top\" : \"left\";\n                const maxSide = crossAxis === \"y\" ? \"bottom\" : \"right\";\n                const min = crossAxisCoord + overflow[minSide];\n                const max = crossAxisCoord - overflow[maxSide];\n                crossAxisCoord = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.clamp)(min, crossAxisCoord, max);\n            }\n            const limitedCoords = limiter.fn({\n                ...state,\n                [mainAxis]: mainAxisCoord,\n                [crossAxis]: crossAxisCoord\n            });\n            return {\n                ...limitedCoords,\n                data: {\n                    x: limitedCoords.x - x,\n                    y: limitedCoords.y - y,\n                    enabled: {\n                        [mainAxis]: checkMainAxis,\n                        [crossAxis]: checkCrossAxis\n                    }\n                }\n            };\n        }\n    };\n};\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */ const limitShift = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        options,\n        fn (state) {\n            const { x, y, placement, rects, middlewareData } = state;\n            const { offset = 0, mainAxis: checkMainAxis = true, crossAxis: checkCrossAxis = true } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            const coords = {\n                x,\n                y\n            };\n            const crossAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement);\n            const mainAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getOppositeAxis)(crossAxis);\n            let mainAxisCoord = coords[mainAxis];\n            let crossAxisCoord = coords[crossAxis];\n            const rawOffset = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(offset, state);\n            const computedOffset = typeof rawOffset === \"number\" ? {\n                mainAxis: rawOffset,\n                crossAxis: 0\n            } : {\n                mainAxis: 0,\n                crossAxis: 0,\n                ...rawOffset\n            };\n            if (checkMainAxis) {\n                const len = mainAxis === \"y\" ? \"height\" : \"width\";\n                const limitMin = rects.reference[mainAxis] - rects.floating[len] + computedOffset.mainAxis;\n                const limitMax = rects.reference[mainAxis] + rects.reference[len] - computedOffset.mainAxis;\n                if (mainAxisCoord < limitMin) {\n                    mainAxisCoord = limitMin;\n                } else if (mainAxisCoord > limitMax) {\n                    mainAxisCoord = limitMax;\n                }\n            }\n            if (checkCrossAxis) {\n                var _middlewareData$offse, _middlewareData$offse2;\n                const len = mainAxis === \"y\" ? \"width\" : \"height\";\n                const isOriginSide = [\n                    \"top\",\n                    \"left\"\n                ].includes((0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement));\n                const limitMin = rects.reference[crossAxis] - rects.floating[len] + (isOriginSide ? ((_middlewareData$offse = middlewareData.offset) == null ? void 0 : _middlewareData$offse[crossAxis]) || 0 : 0) + (isOriginSide ? 0 : computedOffset.crossAxis);\n                const limitMax = rects.reference[crossAxis] + rects.reference[len] + (isOriginSide ? 0 : ((_middlewareData$offse2 = middlewareData.offset) == null ? void 0 : _middlewareData$offse2[crossAxis]) || 0) - (isOriginSide ? computedOffset.crossAxis : 0);\n                if (crossAxisCoord < limitMin) {\n                    crossAxisCoord = limitMin;\n                } else if (crossAxisCoord > limitMax) {\n                    crossAxisCoord = limitMax;\n                }\n            }\n            return {\n                [mainAxis]: mainAxisCoord,\n                [crossAxis]: crossAxisCoord\n            };\n        }\n    };\n};\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */ const size = function(options) {\n    if (options === void 0) {\n        options = {};\n    }\n    return {\n        name: \"size\",\n        options,\n        async fn (state) {\n            var _state$middlewareData, _state$middlewareData2;\n            const { placement, rects, platform, elements } = state;\n            const { apply = ()=>{}, ...detectOverflowOptions } = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.evaluate)(options, state);\n            const overflow = await detectOverflow(state, detectOverflowOptions);\n            const side = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSide)(placement);\n            const alignment = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getAlignment)(placement);\n            const isYAxis = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.getSideAxis)(placement) === \"y\";\n            const { width, height } = rects.floating;\n            let heightSide;\n            let widthSide;\n            if (side === \"top\" || side === \"bottom\") {\n                heightSide = side;\n                widthSide = alignment === (await (platform.isRTL == null ? void 0 : platform.isRTL(elements.floating)) ? \"start\" : \"end\") ? \"left\" : \"right\";\n            } else {\n                widthSide = side;\n                heightSide = alignment === \"end\" ? \"top\" : \"bottom\";\n            }\n            const maximumClippingHeight = height - overflow.top - overflow.bottom;\n            const maximumClippingWidth = width - overflow.left - overflow.right;\n            const overflowAvailableHeight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(height - overflow[heightSide], maximumClippingHeight);\n            const overflowAvailableWidth = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.min)(width - overflow[widthSide], maximumClippingWidth);\n            const noShift = !state.middlewareData.shift;\n            let availableHeight = overflowAvailableHeight;\n            let availableWidth = overflowAvailableWidth;\n            if ((_state$middlewareData = state.middlewareData.shift) != null && _state$middlewareData.enabled.x) {\n                availableWidth = maximumClippingWidth;\n            }\n            if ((_state$middlewareData2 = state.middlewareData.shift) != null && _state$middlewareData2.enabled.y) {\n                availableHeight = maximumClippingHeight;\n            }\n            if (noShift && !alignment) {\n                const xMin = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.left, 0);\n                const xMax = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.right, 0);\n                const yMin = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.top, 0);\n                const yMax = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.bottom, 0);\n                if (isYAxis) {\n                    availableWidth = width - 2 * (xMin !== 0 || xMax !== 0 ? xMin + xMax : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.left, overflow.right));\n                } else {\n                    availableHeight = height - 2 * (yMin !== 0 || yMax !== 0 ? yMin + yMax : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_0__.max)(overflow.top, overflow.bottom));\n                }\n            }\n            await apply({\n                ...state,\n                availableWidth,\n                availableHeight\n            });\n            const nextDimensions = await platform.getDimensions(elements.floating);\n            if (width !== nextDimensions.width || height !== nextDimensions.height) {\n                return {\n                    reset: {\n                        rects: true\n                    }\n                };\n            }\n            return {};\n        }\n    };\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@floating-ui+core@1.7.0/node_modules/@floating-ui/core/dist/floating-ui.core.mjs\n");

/***/ })

};
;