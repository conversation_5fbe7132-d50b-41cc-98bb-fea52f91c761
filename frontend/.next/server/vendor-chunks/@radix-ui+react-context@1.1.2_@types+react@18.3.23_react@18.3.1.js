"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1";
exports.ids = ["vendor-chunks/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/create-context.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n    const Context = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const Provider = (props)=>{\n        const { children, ...context } = props;\n        const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>context, Object.values(context));\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n            value,\n            children\n        });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName) {\n        const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n        if (context) return context;\n        if (defaultContext !== void 0) return defaultContext;\n        throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [\n        Provider,\n        useContext2\n    ];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n    let defaultContexts = [];\n    function createContext3(rootComponentName, defaultContext) {\n        const BaseContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n        const index = defaultContexts.length;\n        defaultContexts = [\n            ...defaultContexts,\n            defaultContext\n        ];\n        const Provider = (props)=>{\n            const { scope, children, ...context } = props;\n            const Context = scope?.[scopeName]?.[index] || BaseContext;\n            const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>context, Object.values(context));\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, {\n                value,\n                children\n            });\n        };\n        Provider.displayName = rootComponentName + \"Provider\";\n        function useContext2(consumerName, scope) {\n            const Context = scope?.[scopeName]?.[index] || BaseContext;\n            const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n            if (context) return context;\n            if (defaultContext !== void 0) return defaultContext;\n            throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n        }\n        return [\n            Provider,\n            useContext2\n        ];\n    }\n    const createScope = ()=>{\n        const scopeContexts = defaultContexts.map((defaultContext)=>{\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n        });\n        return function useScope(scope) {\n            const contexts = scope?.[scopeName] || scopeContexts;\n            return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n                    [`__scope${scopeName}`]: {\n                        ...scope,\n                        [scopeName]: contexts\n                    }\n                }), [\n                scope,\n                contexts\n            ]);\n        };\n    };\n    createScope.scopeName = scopeName;\n    return [\n        createContext3,\n        composeContextScopes(createScope, ...createContextScopeDeps)\n    ];\n}\nfunction composeContextScopes(...scopes) {\n    const baseScope = scopes[0];\n    if (scopes.length === 1) return baseScope;\n    const createScope = ()=>{\n        const scopeHooks = scopes.map((createScope2)=>({\n                useScope: createScope2(),\n                scopeName: createScope2.scopeName\n            }));\n        return function useComposedScopes(overrideScopes) {\n            const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName })=>{\n                const scopeProps = useScope(overrideScopes);\n                const currentScope = scopeProps[`__scope${scopeName}`];\n                return {\n                    ...nextScopes2,\n                    ...currentScope\n                };\n            }, {});\n            return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n                    [`__scope${baseScope.scopeName}`]: nextScopes\n                }), [\n                nextScopes\n            ]);\n        };\n    };\n    createScope.scopeName = baseScope.scopeName;\n    return createScope;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ })

};
;