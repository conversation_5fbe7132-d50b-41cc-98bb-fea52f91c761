{"name": "bugwatch-gov", "version": "1.0.0", "description": "BugWatch Government - A comprehensive bug tracking and reporting system", "private": true, "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && pnpm run dev", "dev:backend": "cd backend && pnpm run dev", "build": "npm run build:backend && npm run build:frontend", "build:frontend": "cd frontend && pnpm run build", "build:backend": "cd backend && pnpm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:frontend": "cd frontend && PORT=3001 pnpm run start", "start:backend": "cd backend && pnpm run start", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && pnpm run test", "test:backend": "cd backend && pnpm run test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && pnpm run lint", "lint:backend": "cd backend && pnpm run lint", "install:all": "pnpm install && cd frontend && pnpm install && cd ../backend && pnpm install"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0"}