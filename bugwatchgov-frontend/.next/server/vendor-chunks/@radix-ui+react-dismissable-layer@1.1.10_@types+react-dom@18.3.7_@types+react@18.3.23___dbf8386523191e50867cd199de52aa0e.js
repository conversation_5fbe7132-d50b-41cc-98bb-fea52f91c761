"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23___dbf8386523191e50867cd199de52aa0e";
exports.ids = ["vendor-chunks/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23___dbf8386523191e50867cd199de52aa0e"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23___dbf8386523191e50867cd199de52aa0e/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs":
/*!*******************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23___dbf8386523191e50867cd199de52aa0e/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs ***!
  \*******************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Branch: () => (/* binding */ Branch),\n/* harmony export */   DismissableLayer: () => (/* binding */ DismissableLayer),\n/* harmony export */   DismissableLayerBranch: () => (/* binding */ DismissableLayerBranch),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23__@types+re_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-escape-keydown */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-escape-keydown@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-escape-keydown/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Branch,DismissableLayer,DismissableLayerBranch,Root auto */ // src/dismissable-layer.tsx\n\n\n\n\n\n\n\nvar DISMISSABLE_LAYER_NAME = \"DismissableLayer\";\nvar CONTEXT_UPDATE = \"dismissableLayer.update\";\nvar POINTER_DOWN_OUTSIDE = \"dismissableLayer.pointerDownOutside\";\nvar FOCUS_OUTSIDE = \"dismissableLayer.focusOutside\";\nvar originalBodyPointerEvents;\nvar DismissableLayerContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext({\n    layers: /* @__PURE__ */ new Set(),\n    layersWithOutsidePointerEventsDisabled: /* @__PURE__ */ new Set(),\n    branches: /* @__PURE__ */ new Set()\n});\nvar DismissableLayer = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disableOutsidePointerEvents = false, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, ...layerProps } = props;\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const ownerDocument = node?.ownerDocument ?? globalThis?.document;\n    const [, force] = react__WEBPACK_IMPORTED_MODULE_0__.useState({});\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const layers = Array.from(context.layers);\n    const [highestLayerWithOutsidePointerEventsDisabled] = [\n        ...context.layersWithOutsidePointerEventsDisabled\n    ].slice(-1);\n    const highestLayerWithOutsidePointerEventsDisabledIndex = layers.indexOf(highestLayerWithOutsidePointerEventsDisabled);\n    const index = node ? layers.indexOf(node) : -1;\n    const isBodyPointerEventsDisabled = context.layersWithOutsidePointerEventsDisabled.size > 0;\n    const isPointerEventsEnabled = index >= highestLayerWithOutsidePointerEventsDisabledIndex;\n    const pointerDownOutside = usePointerDownOutside((event)=>{\n        const target = event.target;\n        const isPointerDownOnBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (!isPointerEventsEnabled || isPointerDownOnBranch) return;\n        onPointerDownOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    const focusOutside = useFocusOutside((event)=>{\n        const target = event.target;\n        const isFocusInBranch = [\n            ...context.branches\n        ].some((branch)=>branch.contains(target));\n        if (isFocusInBranch) return;\n        onFocusOutside?.(event);\n        onInteractOutside?.(event);\n        if (!event.defaultPrevented) onDismiss?.();\n    }, ownerDocument);\n    (0,_radix_ui_react_use_escape_keydown__WEBPACK_IMPORTED_MODULE_3__.useEscapeKeydown)((event)=>{\n        const isHighestLayer = index === context.layers.size - 1;\n        if (!isHighestLayer) return;\n        onEscapeKeyDown?.(event);\n        if (!event.defaultPrevented && onDismiss) {\n            event.preventDefault();\n            onDismiss();\n        }\n    }, ownerDocument);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (!node) return;\n        if (disableOutsidePointerEvents) {\n            if (context.layersWithOutsidePointerEventsDisabled.size === 0) {\n                originalBodyPointerEvents = ownerDocument.body.style.pointerEvents;\n                ownerDocument.body.style.pointerEvents = \"none\";\n            }\n            context.layersWithOutsidePointerEventsDisabled.add(node);\n        }\n        context.layers.add(node);\n        dispatchUpdate();\n        return ()=>{\n            if (disableOutsidePointerEvents && context.layersWithOutsidePointerEventsDisabled.size === 1) {\n                ownerDocument.body.style.pointerEvents = originalBodyPointerEvents;\n            }\n        };\n    }, [\n        node,\n        ownerDocument,\n        disableOutsidePointerEvents,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (!node) return;\n            context.layers.delete(node);\n            context.layersWithOutsidePointerEventsDisabled.delete(node);\n            dispatchUpdate();\n        };\n    }, [\n        node,\n        context\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleUpdate = ()=>force({});\n        document.addEventListener(CONTEXT_UPDATE, handleUpdate);\n        return ()=>document.removeEventListener(CONTEXT_UPDATE, handleUpdate);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...layerProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: isBodyPointerEventsDisabled ? isPointerEventsEnabled ? \"auto\" : \"none\" : void 0,\n            ...props.style\n        },\n        onFocusCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onFocusCapture, focusOutside.onFocusCapture),\n        onBlurCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onBlurCapture, focusOutside.onBlurCapture),\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_5__.composeEventHandlers)(props.onPointerDownCapture, pointerDownOutside.onPointerDownCapture)\n    });\n});\nDismissableLayer.displayName = DISMISSABLE_LAYER_NAME;\nvar BRANCH_NAME = \"DismissableLayerBranch\";\nvar DismissableLayerBranch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DismissableLayerContext);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const node = ref.current;\n        if (node) {\n            context.branches.add(node);\n            return ()=>{\n                context.branches.delete(node);\n            };\n        }\n    }, [\n        context.branches\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...props,\n        ref: composedRefs\n    });\n});\nDismissableLayerBranch.displayName = BRANCH_NAME;\nfunction usePointerDownOutside(onPointerDownOutside, ownerDocument = globalThis?.document) {\n    const handlePointerDownOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onPointerDownOutside);\n    const isPointerInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleClickRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(()=>{});\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handlePointerDown = (event)=>{\n            if (event.target && !isPointerInsideReactTreeRef.current) {\n                let handleAndDispatchPointerDownOutsideEvent2 = function() {\n                    handleAndDispatchCustomEvent(POINTER_DOWN_OUTSIDE, handlePointerDownOutside, eventDetail, {\n                        discrete: true\n                    });\n                };\n                var handleAndDispatchPointerDownOutsideEvent = handleAndDispatchPointerDownOutsideEvent2;\n                const eventDetail = {\n                    originalEvent: event\n                };\n                if (event.pointerType === \"touch\") {\n                    ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n                    handleClickRef.current = handleAndDispatchPointerDownOutsideEvent2;\n                    ownerDocument.addEventListener(\"click\", handleClickRef.current, {\n                        once: true\n                    });\n                } else {\n                    handleAndDispatchPointerDownOutsideEvent2();\n                }\n            } else {\n                ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n            }\n            isPointerInsideReactTreeRef.current = false;\n        };\n        const timerId = window.setTimeout(()=>{\n            ownerDocument.addEventListener(\"pointerdown\", handlePointerDown);\n        }, 0);\n        return ()=>{\n            window.clearTimeout(timerId);\n            ownerDocument.removeEventListener(\"pointerdown\", handlePointerDown);\n            ownerDocument.removeEventListener(\"click\", handleClickRef.current);\n        };\n    }, [\n        ownerDocument,\n        handlePointerDownOutside\n    ]);\n    return {\n        // ensures we check React component tree (not just DOM tree)\n        onPointerDownCapture: ()=>isPointerInsideReactTreeRef.current = true\n    };\n}\nfunction useFocusOutside(onFocusOutside, ownerDocument = globalThis?.document) {\n    const handleFocusOutside = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onFocusOutside);\n    const isFocusInsideReactTreeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleFocus = (event)=>{\n            if (event.target && !isFocusInsideReactTreeRef.current) {\n                const eventDetail = {\n                    originalEvent: event\n                };\n                handleAndDispatchCustomEvent(FOCUS_OUTSIDE, handleFocusOutside, eventDetail, {\n                    discrete: false\n                });\n            }\n        };\n        ownerDocument.addEventListener(\"focusin\", handleFocus);\n        return ()=>ownerDocument.removeEventListener(\"focusin\", handleFocus);\n    }, [\n        ownerDocument,\n        handleFocusOutside\n    ]);\n    return {\n        onFocusCapture: ()=>isFocusInsideReactTreeRef.current = true,\n        onBlurCapture: ()=>isFocusInsideReactTreeRef.current = false\n    };\n}\nfunction dispatchUpdate() {\n    const event = new CustomEvent(CONTEXT_UPDATE);\n    document.dispatchEvent(event);\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const target = detail.originalEvent.target;\n    const event = new CustomEvent(name, {\n        bubbles: false,\n        cancelable: true,\n        detail\n    });\n    if (handler) target.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.dispatchDiscreteCustomEvent)(target, event);\n    } else {\n        target.dispatchEvent(event);\n    }\n}\nvar Root = DismissableLayer;\nvar Branch = DismissableLayerBranch;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23___dbf8386523191e50867cd199de52aa0e/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\n");

/***/ })

};
;