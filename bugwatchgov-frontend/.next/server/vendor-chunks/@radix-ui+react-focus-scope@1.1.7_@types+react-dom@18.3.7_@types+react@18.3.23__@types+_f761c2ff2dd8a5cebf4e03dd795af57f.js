"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-focus-scope@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+_f761c2ff2dd8a5cebf4e03dd795af57f";
exports.ids = ["vendor-chunks/@radix-ui+react-focus-scope@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+_f761c2ff2dd8a5cebf4e03dd795af57f"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+_f761c2ff2dd8a5cebf4e03dd795af57f/node_modules/@radix-ui/react-focus-scope/dist/index.mjs":
/*!*************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+_f761c2ff2dd8a5cebf4e03dd795af57f/node_modules/@radix-ui/react-focus-scope/dist/index.mjs ***!
  \*************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusScope: () => (/* binding */ FocusScope),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23__@types+re_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ FocusScope,Root auto */ // src/focus-scope.tsx\n\n\n\n\n\nvar AUTOFOCUS_ON_MOUNT = \"focusScope.autoFocusOnMount\";\nvar AUTOFOCUS_ON_UNMOUNT = \"focusScope.autoFocusOnUnmount\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar FOCUS_SCOPE_NAME = \"FocusScope\";\nvar FocusScope = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { loop = false, trapped = false, onMountAutoFocus: onMountAutoFocusProp, onUnmountAutoFocus: onUnmountAutoFocusProp, ...scopeProps } = props;\n    const [container, setContainer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const onMountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onMountAutoFocusProp);\n    const onUnmountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onUnmountAutoFocusProp);\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setContainer(node));\n    const focusScope = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        paused: false,\n        pause () {\n            this.paused = true;\n        },\n        resume () {\n            this.paused = false;\n        }\n    }).current;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (trapped) {\n            let handleFocusIn2 = function(event) {\n                if (focusScope.paused || !container) return;\n                const target = event.target;\n                if (container.contains(target)) {\n                    lastFocusedElementRef.current = target;\n                } else {\n                    focus(lastFocusedElementRef.current, {\n                        select: true\n                    });\n                }\n            }, handleFocusOut2 = function(event) {\n                if (focusScope.paused || !container) return;\n                const relatedTarget = event.relatedTarget;\n                if (relatedTarget === null) return;\n                if (!container.contains(relatedTarget)) {\n                    focus(lastFocusedElementRef.current, {\n                        select: true\n                    });\n                }\n            }, handleMutations2 = function(mutations) {\n                const focusedElement = document.activeElement;\n                if (focusedElement !== document.body) return;\n                for (const mutation of mutations){\n                    if (mutation.removedNodes.length > 0) focus(container);\n                }\n            };\n            var handleFocusIn = handleFocusIn2, handleFocusOut = handleFocusOut2, handleMutations = handleMutations2;\n            document.addEventListener(\"focusin\", handleFocusIn2);\n            document.addEventListener(\"focusout\", handleFocusOut2);\n            const mutationObserver = new MutationObserver(handleMutations2);\n            if (container) mutationObserver.observe(container, {\n                childList: true,\n                subtree: true\n            });\n            return ()=>{\n                document.removeEventListener(\"focusin\", handleFocusIn2);\n                document.removeEventListener(\"focusout\", handleFocusOut2);\n                mutationObserver.disconnect();\n            };\n        }\n    }, [\n        trapped,\n        container,\n        focusScope.paused\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (container) {\n            focusScopesStack.add(focusScope);\n            const previouslyFocusedElement = document.activeElement;\n            const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n            if (!hasFocusedCandidate) {\n                const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n                container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                container.dispatchEvent(mountEvent);\n                if (!mountEvent.defaultPrevented) {\n                    focusFirst(removeLinks(getTabbableCandidates(container)), {\n                        select: true\n                    });\n                    if (document.activeElement === previouslyFocusedElement) {\n                        focus(container);\n                    }\n                }\n            }\n            return ()=>{\n                container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                setTimeout(()=>{\n                    const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n                    container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                    container.dispatchEvent(unmountEvent);\n                    if (!unmountEvent.defaultPrevented) {\n                        focus(previouslyFocusedElement ?? document.body, {\n                            select: true\n                        });\n                    }\n                    container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                    focusScopesStack.remove(focusScope);\n                }, 0);\n            };\n        }\n    }, [\n        container,\n        onMountAutoFocus,\n        onUnmountAutoFocus,\n        focusScope\n    ]);\n    const handleKeyDown = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        if (!loop && !trapped) return;\n        if (focusScope.paused) return;\n        const isTabKey = event.key === \"Tab\" && !event.altKey && !event.ctrlKey && !event.metaKey;\n        const focusedElement = document.activeElement;\n        if (isTabKey && focusedElement) {\n            const container2 = event.currentTarget;\n            const [first, last] = getTabbableEdges(container2);\n            const hasTabbableElementsInside = first && last;\n            if (!hasTabbableElementsInside) {\n                if (focusedElement === container2) event.preventDefault();\n            } else {\n                if (!event.shiftKey && focusedElement === last) {\n                    event.preventDefault();\n                    if (loop) focus(first, {\n                        select: true\n                    });\n                } else if (event.shiftKey && focusedElement === first) {\n                    event.preventDefault();\n                    if (loop) focus(last, {\n                        select: true\n                    });\n                }\n            }\n        }\n    }, [\n        loop,\n        trapped,\n        focusScope.paused\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        tabIndex: -1,\n        ...scopeProps,\n        ref: composedRefs,\n        onKeyDown: handleKeyDown\n    });\n});\nFocusScope.displayName = FOCUS_SCOPE_NAME;\nfunction focusFirst(candidates, { select = false } = {}) {\n    const previouslyFocusedElement = document.activeElement;\n    for (const candidate of candidates){\n        focus(candidate, {\n            select\n        });\n        if (document.activeElement !== previouslyFocusedElement) return;\n    }\n}\nfunction getTabbableEdges(container) {\n    const candidates = getTabbableCandidates(container);\n    const first = findVisible(candidates, container);\n    const last = findVisible(candidates.reverse(), container);\n    return [\n        first,\n        last\n    ];\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction findVisible(elements, container) {\n    for (const element of elements){\n        if (!isHidden(element, {\n            upTo: container\n        })) return element;\n    }\n}\nfunction isHidden(node, { upTo }) {\n    if (getComputedStyle(node).visibility === \"hidden\") return true;\n    while(node){\n        if (upTo !== void 0 && node === upTo) return false;\n        if (getComputedStyle(node).display === \"none\") return true;\n        node = node.parentElement;\n    }\n    return false;\n}\nfunction isSelectableInput(element) {\n    return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction focus(element, { select = false } = {}) {\n    if (element && element.focus) {\n        const previouslyFocusedElement = document.activeElement;\n        element.focus({\n            preventScroll: true\n        });\n        if (element !== previouslyFocusedElement && isSelectableInput(element) && select) element.select();\n    }\n}\nvar focusScopesStack = createFocusScopesStack();\nfunction createFocusScopesStack() {\n    let stack = [];\n    return {\n        add (focusScope) {\n            const activeFocusScope = stack[0];\n            if (focusScope !== activeFocusScope) {\n                activeFocusScope?.pause();\n            }\n            stack = arrayRemove(stack, focusScope);\n            stack.unshift(focusScope);\n        },\n        remove (focusScope) {\n            stack = arrayRemove(stack, focusScope);\n            stack[0]?.resume();\n        }\n    };\n}\nfunction arrayRemove(array, item) {\n    const updatedArray = [\n        ...array\n    ];\n    const index = updatedArray.indexOf(item);\n    if (index !== -1) {\n        updatedArray.splice(index, 1);\n    }\n    return updatedArray;\n}\nfunction removeLinks(items) {\n    return items.filter((item)=>item.tagName !== \"A\");\n}\nvar Root = FocusScope;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+_f761c2ff2dd8a5cebf4e03dd795af57f/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\n");

/***/ })

};
;