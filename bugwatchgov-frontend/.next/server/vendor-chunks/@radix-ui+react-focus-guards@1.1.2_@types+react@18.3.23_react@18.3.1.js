"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-focus-guards@1.1.2_@types+react@18.3.23_react@18.3.1";
exports.ids = ["vendor-chunks/@radix-ui+react-focus-guards@1.1.2_@types+react@18.3.23_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guards@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-focus-guards/dist/index.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-focus-guards@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-focus-guards/dist/index.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusGuards: () => (/* binding */ FocusGuards),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   useFocusGuards: () => (/* binding */ useFocusGuards)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ FocusGuards,Root,useFocusGuards auto */ // packages/react/focus-guards/src/focus-guards.tsx\n\nvar count = 0;\nfunction FocusGuards(props) {\n    useFocusGuards();\n    return props.children;\n}\nfunction useFocusGuards() {\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const edgeGuards = document.querySelectorAll(\"[data-radix-focus-guard]\");\n        document.body.insertAdjacentElement(\"afterbegin\", edgeGuards[0] ?? createFocusGuard());\n        document.body.insertAdjacentElement(\"beforeend\", edgeGuards[1] ?? createFocusGuard());\n        count++;\n        return ()=>{\n            if (count === 1) {\n                document.querySelectorAll(\"[data-radix-focus-guard]\").forEach((node)=>node.remove());\n            }\n            count--;\n        };\n    }, []);\n}\nfunction createFocusGuard() {\n    const element = document.createElement(\"span\");\n    element.setAttribute(\"data-radix-focus-guard\", \"\");\n    element.tabIndex = 0;\n    element.style.outline = \"none\";\n    element.style.opacity = \"0\";\n    element.style.position = \"fixed\";\n    element.style.pointerEvents = \"none\";\n    return element;\n}\nvar Root = FocusGuards;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guards@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\n");

/***/ })

};
;