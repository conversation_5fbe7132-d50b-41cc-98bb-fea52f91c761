"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@floating-ui+dom@1.7.0";
exports.ids = ["vendor-chunks/@floating-ui+dom@1.7.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@floating-ui+dom@1.7.0/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@floating-ui+dom@1.7.0/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrow: () => (/* binding */ arrow),\n/* harmony export */   autoPlacement: () => (/* binding */ autoPlacement),\n/* harmony export */   autoUpdate: () => (/* binding */ autoUpdate),\n/* harmony export */   computePosition: () => (/* binding */ computePosition),\n/* harmony export */   detectOverflow: () => (/* binding */ detectOverflow),\n/* harmony export */   flip: () => (/* binding */ flip),\n/* harmony export */   getOverflowAncestors: () => (/* reexport safe */ _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors),\n/* harmony export */   hide: () => (/* binding */ hide),\n/* harmony export */   inline: () => (/* binding */ inline),\n/* harmony export */   limitShift: () => (/* binding */ limitShift),\n/* harmony export */   offset: () => (/* binding */ offset),\n/* harmony export */   platform: () => (/* binding */ platform),\n/* harmony export */   shift: () => (/* binding */ shift),\n/* harmony export */   size: () => (/* binding */ size)\n/* harmony export */ });\n/* harmony import */ var _floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @floating-ui/utils */ \"(ssr)/./node_modules/.pnpm/@floating-ui+utils@0.2.9/node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs\");\n/* harmony import */ var _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @floating-ui/core */ \"(ssr)/./node_modules/.pnpm/@floating-ui+core@1.7.0/node_modules/@floating-ui/core/dist/floating-ui.core.mjs\");\n/* harmony import */ var _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @floating-ui/utils/dom */ \"(ssr)/./node_modules/.pnpm/@floating-ui+utils@0.2.9/node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs\");\n\n\n\n\nfunction getCssDimensions(element) {\n    const css = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element);\n    // In testing environments, the `width` and `height` properties are empty\n    // strings for SVG elements, returning NaN. Fallback to `0` in this case.\n    let width = parseFloat(css.width) || 0;\n    let height = parseFloat(css.height) || 0;\n    const hasOffset = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element);\n    const offsetWidth = hasOffset ? element.offsetWidth : width;\n    const offsetHeight = hasOffset ? element.offsetHeight : height;\n    const shouldFallback = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(width) !== offsetWidth || (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(height) !== offsetHeight;\n    if (shouldFallback) {\n        width = offsetWidth;\n        height = offsetHeight;\n    }\n    return {\n        width,\n        height,\n        $: shouldFallback\n    };\n}\nfunction unwrapElement(element) {\n    return !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(element) ? element.contextElement : element;\n}\nfunction getScale(element) {\n    const domElement = unwrapElement(element);\n    if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(domElement)) {\n        return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n    }\n    const rect = domElement.getBoundingClientRect();\n    const { width, height, $ } = getCssDimensions(domElement);\n    let x = ($ ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(rect.width) : rect.width) / width;\n    let y = ($ ? (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.round)(rect.height) : rect.height) / height;\n    // 0, NaN, or Infinity should always fallback to 1.\n    if (!x || !Number.isFinite(x)) {\n        x = 1;\n    }\n    if (!y || !Number.isFinite(y)) {\n        y = 1;\n    }\n    return {\n        x,\n        y\n    };\n}\nconst noOffsets = /*#__PURE__*/ (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\nfunction getVisualOffsets(element) {\n    const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n    if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isWebKit)() || !win.visualViewport) {\n        return noOffsets;\n    }\n    return {\n        x: win.visualViewport.offsetLeft,\n        y: win.visualViewport.offsetTop\n    };\n}\nfunction shouldAddVisualOffsets(element, isFixed, floatingOffsetParent) {\n    if (isFixed === void 0) {\n        isFixed = false;\n    }\n    if (!floatingOffsetParent || isFixed && floatingOffsetParent !== (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element)) {\n        return false;\n    }\n    return isFixed;\n}\nfunction getBoundingClientRect(element, includeScale, isFixedStrategy, offsetParent) {\n    if (includeScale === void 0) {\n        includeScale = false;\n    }\n    if (isFixedStrategy === void 0) {\n        isFixedStrategy = false;\n    }\n    const clientRect = element.getBoundingClientRect();\n    const domElement = unwrapElement(element);\n    let scale = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n    if (includeScale) {\n        if (offsetParent) {\n            if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(offsetParent)) {\n                scale = getScale(offsetParent);\n            }\n        } else {\n            scale = getScale(element);\n        }\n    }\n    const visualOffsets = shouldAddVisualOffsets(domElement, isFixedStrategy, offsetParent) ? getVisualOffsets(domElement) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n    let x = (clientRect.left + visualOffsets.x) / scale.x;\n    let y = (clientRect.top + visualOffsets.y) / scale.y;\n    let width = clientRect.width / scale.x;\n    let height = clientRect.height / scale.y;\n    if (domElement) {\n        const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(domElement);\n        const offsetWin = offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(offsetParent) ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(offsetParent) : offsetParent;\n        let currentWin = win;\n        let currentIFrame = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getFrameElement)(currentWin);\n        while(currentIFrame && offsetParent && offsetWin !== currentWin){\n            const iframeScale = getScale(currentIFrame);\n            const iframeRect = currentIFrame.getBoundingClientRect();\n            const css = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(currentIFrame);\n            const left = iframeRect.left + (currentIFrame.clientLeft + parseFloat(css.paddingLeft)) * iframeScale.x;\n            const top = iframeRect.top + (currentIFrame.clientTop + parseFloat(css.paddingTop)) * iframeScale.y;\n            x *= iframeScale.x;\n            y *= iframeScale.y;\n            width *= iframeScale.x;\n            height *= iframeScale.y;\n            x += left;\n            y += top;\n            currentWin = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(currentIFrame);\n            currentIFrame = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getFrameElement)(currentWin);\n        }\n    }\n    return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.rectToClientRect)({\n        width,\n        height,\n        x,\n        y\n    });\n}\n// If <html> has a CSS width greater than the viewport, then this will be\n// incorrect for RTL.\nfunction getWindowScrollBarX(element, rect) {\n    const leftScroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(element).scrollLeft;\n    if (!rect) {\n        return getBoundingClientRect((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element)).left + leftScroll;\n    }\n    return rect.left + leftScroll;\n}\nfunction getHTMLOffset(documentElement, scroll, ignoreScrollbarX) {\n    if (ignoreScrollbarX === void 0) {\n        ignoreScrollbarX = false;\n    }\n    const htmlRect = documentElement.getBoundingClientRect();\n    const x = htmlRect.left + scroll.scrollLeft - (ignoreScrollbarX ? 0 : // RTL <body> scrollbar.\n    getWindowScrollBarX(documentElement, htmlRect));\n    const y = htmlRect.top + scroll.scrollTop;\n    return {\n        x,\n        y\n    };\n}\nfunction convertOffsetParentRelativeRectToViewportRelativeRect(_ref) {\n    let { elements, rect, offsetParent, strategy } = _ref;\n    const isFixed = strategy === \"fixed\";\n    const documentElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(offsetParent);\n    const topLayer = elements ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(elements.floating) : false;\n    if (offsetParent === documentElement || topLayer && isFixed) {\n        return rect;\n    }\n    let scroll = {\n        scrollLeft: 0,\n        scrollTop: 0\n    };\n    let scale = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n    const offsets = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n    const isOffsetParentAnElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent);\n    if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n        if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(offsetParent) !== \"body\" || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(documentElement)) {\n            scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(offsetParent);\n        }\n        if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent)) {\n            const offsetRect = getBoundingClientRect(offsetParent);\n            scale = getScale(offsetParent);\n            offsets.x = offsetRect.x + offsetParent.clientLeft;\n            offsets.y = offsetRect.y + offsetParent.clientTop;\n        }\n    }\n    const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll, true) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n    return {\n        width: rect.width * scale.x,\n        height: rect.height * scale.y,\n        x: rect.x * scale.x - scroll.scrollLeft * scale.x + offsets.x + htmlOffset.x,\n        y: rect.y * scale.y - scroll.scrollTop * scale.y + offsets.y + htmlOffset.y\n    };\n}\nfunction getClientRects(element) {\n    return Array.from(element.getClientRects());\n}\n// Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable.\nfunction getDocumentRect(element) {\n    const html = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n    const scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(element);\n    const body = element.ownerDocument.body;\n    const width = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.scrollWidth, html.clientWidth, body.scrollWidth, body.clientWidth);\n    const height = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.scrollHeight, html.clientHeight, body.scrollHeight, body.clientHeight);\n    let x = -scroll.scrollLeft + getWindowScrollBarX(element);\n    const y = -scroll.scrollTop;\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(body).direction === \"rtl\") {\n        x += (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(html.clientWidth, body.clientWidth) - width;\n    }\n    return {\n        width,\n        height,\n        x,\n        y\n    };\n}\nfunction getViewportRect(element, strategy) {\n    const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n    const html = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n    const visualViewport = win.visualViewport;\n    let width = html.clientWidth;\n    let height = html.clientHeight;\n    let x = 0;\n    let y = 0;\n    if (visualViewport) {\n        width = visualViewport.width;\n        height = visualViewport.height;\n        const visualViewportBased = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isWebKit)();\n        if (!visualViewportBased || visualViewportBased && strategy === \"fixed\") {\n            x = visualViewport.offsetLeft;\n            y = visualViewport.offsetTop;\n        }\n    }\n    return {\n        width,\n        height,\n        x,\n        y\n    };\n}\n// Returns the inner client rect, subtracting scrollbars if present.\nfunction getInnerBoundingClientRect(element, strategy) {\n    const clientRect = getBoundingClientRect(element, true, strategy === \"fixed\");\n    const top = clientRect.top + element.clientTop;\n    const left = clientRect.left + element.clientLeft;\n    const scale = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) ? getScale(element) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(1);\n    const width = element.clientWidth * scale.x;\n    const height = element.clientHeight * scale.y;\n    const x = left * scale.x;\n    const y = top * scale.y;\n    return {\n        width,\n        height,\n        x,\n        y\n    };\n}\nfunction getClientRectFromClippingAncestor(element, clippingAncestor, strategy) {\n    let rect;\n    if (clippingAncestor === \"viewport\") {\n        rect = getViewportRect(element, strategy);\n    } else if (clippingAncestor === \"document\") {\n        rect = getDocumentRect((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element));\n    } else if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(clippingAncestor)) {\n        rect = getInnerBoundingClientRect(clippingAncestor, strategy);\n    } else {\n        const visualOffsets = getVisualOffsets(element);\n        rect = {\n            x: clippingAncestor.x - visualOffsets.x,\n            y: clippingAncestor.y - visualOffsets.y,\n            width: clippingAncestor.width,\n            height: clippingAncestor.height\n        };\n    }\n    return (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.rectToClientRect)(rect);\n}\nfunction hasFixedPositionAncestor(element, stopNode) {\n    const parentNode = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element);\n    if (parentNode === stopNode || !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(parentNode) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(parentNode)) {\n        return false;\n    }\n    return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(parentNode).position === \"fixed\" || hasFixedPositionAncestor(parentNode, stopNode);\n}\n// A \"clipping ancestor\" is an `overflow` element with the characteristic of\n// clipping (or hiding) child elements. This returns all clipping ancestors\n// of the given element up the tree.\nfunction getClippingElementAncestors(element, cache) {\n    const cachedResult = cache.get(element);\n    if (cachedResult) {\n        return cachedResult;\n    }\n    let result = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(element, [], false).filter((el)=>(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(el) && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(el) !== \"body\");\n    let currentContainingBlockComputedStyle = null;\n    const elementIsFixed = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === \"fixed\";\n    let currentNode = elementIsFixed ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element) : element;\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n    while((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(currentNode) && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(currentNode)){\n        const computedStyle = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(currentNode);\n        const currentNodeIsContaining = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isContainingBlock)(currentNode);\n        if (!currentNodeIsContaining && computedStyle.position === \"fixed\") {\n            currentContainingBlockComputedStyle = null;\n        }\n        const shouldDropCurrentNode = elementIsFixed ? !currentNodeIsContaining && !currentContainingBlockComputedStyle : !currentNodeIsContaining && computedStyle.position === \"static\" && !!currentContainingBlockComputedStyle && [\n            \"absolute\",\n            \"fixed\"\n        ].includes(currentContainingBlockComputedStyle.position) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(currentNode) && !currentNodeIsContaining && hasFixedPositionAncestor(element, currentNode);\n        if (shouldDropCurrentNode) {\n            // Drop non-containing blocks.\n            result = result.filter((ancestor)=>ancestor !== currentNode);\n        } else {\n            // Record last containing block for next iteration.\n            currentContainingBlockComputedStyle = computedStyle;\n        }\n        currentNode = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(currentNode);\n    }\n    cache.set(element, result);\n    return result;\n}\n// Gets the maximum area that the element is visible in due to any number of\n// clipping ancestors.\nfunction getClippingRect(_ref) {\n    let { element, boundary, rootBoundary, strategy } = _ref;\n    const elementClippingAncestors = boundary === \"clippingAncestors\" ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(element) ? [] : getClippingElementAncestors(element, this._c) : [].concat(boundary);\n    const clippingAncestors = [\n        ...elementClippingAncestors,\n        rootBoundary\n    ];\n    const firstClippingAncestor = clippingAncestors[0];\n    const clippingRect = clippingAncestors.reduce((accRect, clippingAncestor)=>{\n        const rect = getClientRectFromClippingAncestor(element, clippingAncestor, strategy);\n        accRect.top = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(rect.top, accRect.top);\n        accRect.right = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(rect.right, accRect.right);\n        accRect.bottom = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(rect.bottom, accRect.bottom);\n        accRect.left = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(rect.left, accRect.left);\n        return accRect;\n    }, getClientRectFromClippingAncestor(element, firstClippingAncestor, strategy));\n    return {\n        width: clippingRect.right - clippingRect.left,\n        height: clippingRect.bottom - clippingRect.top,\n        x: clippingRect.left,\n        y: clippingRect.top\n    };\n}\nfunction getDimensions(element) {\n    const { width, height } = getCssDimensions(element);\n    return {\n        width,\n        height\n    };\n}\nfunction getRectRelativeToOffsetParent(element, offsetParent, strategy) {\n    const isOffsetParentAnElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(offsetParent);\n    const documentElement = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(offsetParent);\n    const isFixed = strategy === \"fixed\";\n    const rect = getBoundingClientRect(element, true, isFixed, offsetParent);\n    let scroll = {\n        scrollLeft: 0,\n        scrollTop: 0\n    };\n    const offsets = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n    // If the <body> scrollbar appears on the left (e.g. RTL systems). Use\n    // Firefox with layout.scrollbar.side = 3 in about:config to test this.\n    function setLeftRTLScrollbarOffset() {\n        offsets.x = getWindowScrollBarX(documentElement);\n    }\n    if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n        if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeName)(offsetParent) !== \"body\" || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isOverflowElement)(documentElement)) {\n            scroll = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getNodeScroll)(offsetParent);\n        }\n        if (isOffsetParentAnElement) {\n            const offsetRect = getBoundingClientRect(offsetParent, true, isFixed, offsetParent);\n            offsets.x = offsetRect.x + offsetParent.clientLeft;\n            offsets.y = offsetRect.y + offsetParent.clientTop;\n        } else if (documentElement) {\n            setLeftRTLScrollbarOffset();\n        }\n    }\n    if (isFixed && !isOffsetParentAnElement && documentElement) {\n        setLeftRTLScrollbarOffset();\n    }\n    const htmlOffset = documentElement && !isOffsetParentAnElement && !isFixed ? getHTMLOffset(documentElement, scroll) : (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.createCoords)(0);\n    const x = rect.left + scroll.scrollLeft - offsets.x - htmlOffset.x;\n    const y = rect.top + scroll.scrollTop - offsets.y - htmlOffset.y;\n    return {\n        x,\n        y,\n        width: rect.width,\n        height: rect.height\n    };\n}\nfunction isStaticPositioned(element) {\n    return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === \"static\";\n}\nfunction getTrueOffsetParent(element, polyfill) {\n    if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element) || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).position === \"fixed\") {\n        return null;\n    }\n    if (polyfill) {\n        return polyfill(element);\n    }\n    let rawOffsetParent = element.offsetParent;\n    // Firefox returns the <html> element as the offsetParent if it's non-static,\n    // while Chrome and Safari return the <body> element. The <body> element must\n    // be used to perform the correct calculations even if the <html> element is\n    // non-static.\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element) === rawOffsetParent) {\n        rawOffsetParent = rawOffsetParent.ownerDocument.body;\n    }\n    return rawOffsetParent;\n}\n// Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\nfunction getOffsetParent(element, polyfill) {\n    const win = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getWindow)(element);\n    if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTopLayer)(element)) {\n        return win;\n    }\n    if (!(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n        let svgOffsetParent = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(element);\n        while(svgOffsetParent && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(svgOffsetParent)){\n            if ((0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement)(svgOffsetParent) && !isStaticPositioned(svgOffsetParent)) {\n                return svgOffsetParent;\n            }\n            svgOffsetParent = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getParentNode)(svgOffsetParent);\n        }\n        return win;\n    }\n    let offsetParent = getTrueOffsetParent(element, polyfill);\n    while(offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isTableElement)(offsetParent) && isStaticPositioned(offsetParent)){\n        offsetParent = getTrueOffsetParent(offsetParent, polyfill);\n    }\n    if (offsetParent && (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isLastTraversableNode)(offsetParent) && isStaticPositioned(offsetParent) && !(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isContainingBlock)(offsetParent)) {\n        return win;\n    }\n    return offsetParent || (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getContainingBlock)(element) || win;\n}\nconst getElementRects = async function(data) {\n    const getOffsetParentFn = this.getOffsetParent || getOffsetParent;\n    const getDimensionsFn = this.getDimensions;\n    const floatingDimensions = await getDimensionsFn(data.floating);\n    return {\n        reference: getRectRelativeToOffsetParent(data.reference, await getOffsetParentFn(data.floating), data.strategy),\n        floating: {\n            x: 0,\n            y: 0,\n            width: floatingDimensions.width,\n            height: floatingDimensions.height\n        }\n    };\n};\nfunction isRTL(element) {\n    return (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getComputedStyle)(element).direction === \"rtl\";\n}\nconst platform = {\n    convertOffsetParentRelativeRectToViewportRelativeRect,\n    getDocumentElement: _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement,\n    getClippingRect,\n    getOffsetParent,\n    getElementRects,\n    getClientRects,\n    getDimensions,\n    getScale,\n    isElement: _floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.isElement,\n    isRTL\n};\nfunction rectsAreEqual(a, b) {\n    return a.x === b.x && a.y === b.y && a.width === b.width && a.height === b.height;\n}\n// https://samthor.au/2021/observing-dom/\nfunction observeMove(element, onMove) {\n    let io = null;\n    let timeoutId;\n    const root = (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getDocumentElement)(element);\n    function cleanup() {\n        var _io;\n        clearTimeout(timeoutId);\n        (_io = io) == null || _io.disconnect();\n        io = null;\n    }\n    function refresh(skip, threshold) {\n        if (skip === void 0) {\n            skip = false;\n        }\n        if (threshold === void 0) {\n            threshold = 1;\n        }\n        cleanup();\n        const elementRectForRootMargin = element.getBoundingClientRect();\n        const { left, top, width, height } = elementRectForRootMargin;\n        if (!skip) {\n            onMove();\n        }\n        if (!width || !height) {\n            return;\n        }\n        const insetTop = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(top);\n        const insetRight = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(root.clientWidth - (left + width));\n        const insetBottom = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(root.clientHeight - (top + height));\n        const insetLeft = (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.floor)(left);\n        const rootMargin = -insetTop + \"px \" + -insetRight + \"px \" + -insetBottom + \"px \" + -insetLeft + \"px\";\n        const options = {\n            rootMargin,\n            threshold: (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.max)(0, (0,_floating_ui_utils__WEBPACK_IMPORTED_MODULE_1__.min)(1, threshold)) || 1\n        };\n        let isFirstUpdate = true;\n        function handleObserve(entries) {\n            const ratio = entries[0].intersectionRatio;\n            if (ratio !== threshold) {\n                if (!isFirstUpdate) {\n                    return refresh();\n                }\n                if (!ratio) {\n                    // If the reference is clipped, the ratio is 0. Throttle the refresh\n                    // to prevent an infinite loop of updates.\n                    timeoutId = setTimeout(()=>{\n                        refresh(false, 1e-7);\n                    }, 1000);\n                } else {\n                    refresh(false, ratio);\n                }\n            }\n            if (ratio === 1 && !rectsAreEqual(elementRectForRootMargin, element.getBoundingClientRect())) {\n                // It's possible that even though the ratio is reported as 1, the\n                // element is not actually fully within the IntersectionObserver's root\n                // area anymore. This can happen under performance constraints. This may\n                // be a bug in the browser's IntersectionObserver implementation. To\n                // work around this, we compare the element's bounding rect now with\n                // what it was at the time we created the IntersectionObserver. If they\n                // are not equal then the element moved, so we refresh.\n                refresh();\n            }\n            isFirstUpdate = false;\n        }\n        // Older browsers don't support a `document` as the root and will throw an\n        // error.\n        try {\n            io = new IntersectionObserver(handleObserve, {\n                ...options,\n                // Handle <iframe>s\n                root: root.ownerDocument\n            });\n        } catch (_e) {\n            io = new IntersectionObserver(handleObserve, options);\n        }\n        io.observe(element);\n    }\n    refresh(true);\n    return cleanup;\n}\n/**\n * Automatically updates the position of the floating element when necessary.\n * Should only be called when the floating element is mounted on the DOM or\n * visible on the screen.\n * @returns cleanup function that should be invoked when the floating element is\n * removed from the DOM or hidden from the screen.\n * @see https://floating-ui.com/docs/autoUpdate\n */ function autoUpdate(reference, floating, update, options) {\n    if (options === void 0) {\n        options = {};\n    }\n    const { ancestorScroll = true, ancestorResize = true, elementResize = typeof ResizeObserver === \"function\", layoutShift = typeof IntersectionObserver === \"function\", animationFrame = false } = options;\n    const referenceEl = unwrapElement(reference);\n    const ancestors = ancestorScroll || ancestorResize ? [\n        ...referenceEl ? (0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(referenceEl) : [],\n        ...(0,_floating_ui_utils_dom__WEBPACK_IMPORTED_MODULE_0__.getOverflowAncestors)(floating)\n    ] : [];\n    ancestors.forEach((ancestor)=>{\n        ancestorScroll && ancestor.addEventListener(\"scroll\", update, {\n            passive: true\n        });\n        ancestorResize && ancestor.addEventListener(\"resize\", update);\n    });\n    const cleanupIo = referenceEl && layoutShift ? observeMove(referenceEl, update) : null;\n    let reobserveFrame = -1;\n    let resizeObserver = null;\n    if (elementResize) {\n        resizeObserver = new ResizeObserver((_ref)=>{\n            let [firstEntry] = _ref;\n            if (firstEntry && firstEntry.target === referenceEl && resizeObserver) {\n                // Prevent update loops when using the `size` middleware.\n                // https://github.com/floating-ui/floating-ui/issues/1740\n                resizeObserver.unobserve(floating);\n                cancelAnimationFrame(reobserveFrame);\n                reobserveFrame = requestAnimationFrame(()=>{\n                    var _resizeObserver;\n                    (_resizeObserver = resizeObserver) == null || _resizeObserver.observe(floating);\n                });\n            }\n            update();\n        });\n        if (referenceEl && !animationFrame) {\n            resizeObserver.observe(referenceEl);\n        }\n        resizeObserver.observe(floating);\n    }\n    let frameId;\n    let prevRefRect = animationFrame ? getBoundingClientRect(reference) : null;\n    if (animationFrame) {\n        frameLoop();\n    }\n    function frameLoop() {\n        const nextRefRect = getBoundingClientRect(reference);\n        if (prevRefRect && !rectsAreEqual(prevRefRect, nextRefRect)) {\n            update();\n        }\n        prevRefRect = nextRefRect;\n        frameId = requestAnimationFrame(frameLoop);\n    }\n    update();\n    return ()=>{\n        var _resizeObserver2;\n        ancestors.forEach((ancestor)=>{\n            ancestorScroll && ancestor.removeEventListener(\"scroll\", update);\n            ancestorResize && ancestor.removeEventListener(\"resize\", update);\n        });\n        cleanupIo == null || cleanupIo();\n        (_resizeObserver2 = resizeObserver) == null || _resizeObserver2.disconnect();\n        resizeObserver = null;\n        if (animationFrame) {\n            cancelAnimationFrame(frameId);\n        }\n    };\n}\n/**\n * Resolves with an object of overflow side offsets that determine how much the\n * element is overflowing a given clipping boundary on each side.\n * - positive = overflowing the boundary by that number of pixels\n * - negative = how many pixels left before it will overflow\n * - 0 = lies flush with the boundary\n * @see https://floating-ui.com/docs/detectOverflow\n */ const detectOverflow = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.detectOverflow;\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */ const offset = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.offset;\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */ const autoPlacement = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.autoPlacement;\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */ const shift = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.shift;\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */ const flip = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.flip;\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */ const size = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.size;\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */ const hide = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.hide;\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * @see https://floating-ui.com/docs/arrow\n */ const arrow = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.arrow;\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */ const inline = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.inline;\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */ const limitShift = _floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.limitShift;\n/**\n * Computes the `x` and `y` coordinates that will place the floating element\n * next to a given reference element.\n */ const computePosition = (reference, floating, options)=>{\n    // This caches the expensive `getClippingElementAncestors` function so that\n    // multiple lifecycle resets re-use the same result. It only lives for a\n    // single call. If other functions become expensive, we can add them as well.\n    const cache = new Map();\n    const mergedOptions = {\n        platform,\n        ...options\n    };\n    const platformWithCache = {\n        ...mergedOptions.platform,\n        _c: cache\n    };\n    return (0,_floating_ui_core__WEBPACK_IMPORTED_MODULE_2__.computePosition)(reference, floating, {\n        ...mergedOptions,\n        platform: platformWithCache\n    });\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@floating-ui+dom@1.7.0/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\n");

/***/ })

};
;