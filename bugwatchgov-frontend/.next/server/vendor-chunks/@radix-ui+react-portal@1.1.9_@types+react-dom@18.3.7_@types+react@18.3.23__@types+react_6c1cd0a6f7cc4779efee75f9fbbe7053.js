"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-portal@1.1.9_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_6c1cd0a6f7cc4779efee75f9fbbe7053";
exports.ids = ["vendor-chunks/@radix-ui+react-portal@1.1.9_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_6c1cd0a6f7cc4779efee75f9fbbe7053"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1.9_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_6c1cd0a6f7cc4779efee75f9fbbe7053/node_modules/@radix-ui/react-portal/dist/index.mjs":
/*!********************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-portal@1.1.9_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_6c1cd0a6f7cc4779efee75f9fbbe7053/node_modules/@radix-ui/react-portal/dist/index.mjs ***!
  \********************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23__@types+re_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-effect@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Portal,Root auto */ // src/portal.tsx\n\n\n\n\n\nvar PORTAL_NAME = \"Portal\";\nvar Portal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { container: containerProp, ...portalProps } = props;\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect)(()=>setMounted(true), []);\n    const container = containerProp || mounted && globalThis?.document?.body;\n    return container ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...portalProps,\n        ref: forwardedRef\n    }), container) : null;\n});\nPortal.displayName = PORTAL_NAME;\nvar Root = Portal;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1.9_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_6c1cd0a6f7cc4779efee75f9fbbe7053/node_modules/@radix-ui/react-portal/dist/index.mjs\n");

/***/ })

};
;