"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-toast@1.2.14_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_f2d63fe9a772cc94531d3b740a819e3d";
exports.ids = ["vendor-chunks/@radix-ui+react-toast@1.2.14_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_f2d63fe9a772cc94531d3b740a819e3d"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-toast@1.2.14_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_f2d63fe9a772cc94531d3b740a819e3d/node_modules/@radix-ui/react-toast/dist/index.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-toast@1.2.14_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_f2d63fe9a772cc94531d3b740a819e3d/node_modules/@radix-ui/react-toast/dist/index.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Action: () => (/* binding */ Action),\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Description: () => (/* binding */ Description),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Title: () => (/* binding */ Title),\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createToastScope: () => (/* binding */ createToastScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+r_8ed47621286c24058c66cffcdce48db5/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23___dbf8386523191e50867cd199de52aa0e/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1.9_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_6c1cd0a6f7cc4779efee75f9fbbe7053/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1.1.4_@types+react-dom@18.3.7_@types+react@18.3.23__@types+rea_587c7e8c3eecba09139e2afe2a783727/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23__@types+re_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callback-ref@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-controllable-state@1.2.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-effect@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hidden@1.2.3_@types+react-dom@18.3.7_@types+react@18.3.23__@ty_bd769e2c7ddceeff6e63be21c84dfac7/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Action,Close,Description,Provider,Root,Title,Toast,ToastAction,ToastClose,ToastDescription,ToastProvider,ToastTitle,ToastViewport,Viewport,createToastScope auto */ // src/toast.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar PROVIDER_NAME = \"ToastProvider\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(\"Toast\");\nvar [createToastContext, createToastScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(\"Toast\", [\n    createCollectionScope\n]);\nvar [ToastProviderProvider, useToastProviderContext] = createToastContext(PROVIDER_NAME);\nvar ToastProvider = (props)=>{\n    const { __scopeToast, label = \"Notification\", duration = 5e3, swipeDirection = \"right\", swipeThreshold = 50, children } = props;\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [toastCount, setToastCount] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const isFocusedToastEscapeKeyDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isClosePausedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    if (!label.trim()) {\n        console.error(`Invalid prop \\`label\\` supplied to \\`${PROVIDER_NAME}\\`. Expected non-empty \\`string\\`.`);\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n        scope: __scopeToast,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastProviderProvider, {\n            scope: __scopeToast,\n            label,\n            duration,\n            swipeDirection,\n            swipeThreshold,\n            toastCount,\n            viewport,\n            onViewportChange: setViewport,\n            onToastAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setToastCount((prevCount)=>prevCount + 1), []),\n            onToastRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setToastCount((prevCount)=>prevCount - 1), []),\n            isFocusedToastEscapeKeyDownRef,\n            isClosePausedRef,\n            children\n        })\n    });\n};\nToastProvider.displayName = PROVIDER_NAME;\nvar VIEWPORT_NAME = \"ToastViewport\";\nvar VIEWPORT_DEFAULT_HOTKEY = [\n    \"F8\"\n];\nvar VIEWPORT_PAUSE = \"toast.viewportPause\";\nvar VIEWPORT_RESUME = \"toast.viewportResume\";\nvar ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, hotkey = VIEWPORT_DEFAULT_HOTKEY, label = \"Notifications ({hotkey})\", ...viewportProps } = props;\n    const context = useToastProviderContext(VIEWPORT_NAME, __scopeToast);\n    const getItems = useCollection(__scopeToast);\n    const wrapperRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const headFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const tailFocusProxyRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    const hotkeyLabel = hotkey.join(\"+\").replace(/Key/g, \"\").replace(/Digit/g, \"\");\n    const hasToasts = context.toastCount > 0;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = (event)=>{\n            const isHotkeyPressed = hotkey.length !== 0 && hotkey.every((key)=>event[key] || event.code === key);\n            if (isHotkeyPressed) ref.current?.focus();\n        };\n        document.addEventListener(\"keydown\", handleKeyDown);\n        return ()=>document.removeEventListener(\"keydown\", handleKeyDown);\n    }, [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const wrapper = wrapperRef.current;\n        const viewport = ref.current;\n        if (hasToasts && wrapper && viewport) {\n            const handlePause = ()=>{\n                if (!context.isClosePausedRef.current) {\n                    const pauseEvent = new CustomEvent(VIEWPORT_PAUSE);\n                    viewport.dispatchEvent(pauseEvent);\n                    context.isClosePausedRef.current = true;\n                }\n            };\n            const handleResume = ()=>{\n                if (context.isClosePausedRef.current) {\n                    const resumeEvent = new CustomEvent(VIEWPORT_RESUME);\n                    viewport.dispatchEvent(resumeEvent);\n                    context.isClosePausedRef.current = false;\n                }\n            };\n            const handleFocusOutResume = (event)=>{\n                const isFocusMovingOutside = !wrapper.contains(event.relatedTarget);\n                if (isFocusMovingOutside) handleResume();\n            };\n            const handlePointerLeaveResume = ()=>{\n                const isFocusInside = wrapper.contains(document.activeElement);\n                if (!isFocusInside) handleResume();\n            };\n            wrapper.addEventListener(\"focusin\", handlePause);\n            wrapper.addEventListener(\"focusout\", handleFocusOutResume);\n            wrapper.addEventListener(\"pointermove\", handlePause);\n            wrapper.addEventListener(\"pointerleave\", handlePointerLeaveResume);\n            window.addEventListener(\"blur\", handlePause);\n            window.addEventListener(\"focus\", handleResume);\n            return ()=>{\n                wrapper.removeEventListener(\"focusin\", handlePause);\n                wrapper.removeEventListener(\"focusout\", handleFocusOutResume);\n                wrapper.removeEventListener(\"pointermove\", handlePause);\n                wrapper.removeEventListener(\"pointerleave\", handlePointerLeaveResume);\n                window.removeEventListener(\"blur\", handlePause);\n                window.removeEventListener(\"focus\", handleResume);\n            };\n        }\n    }, [\n        hasToasts,\n        context.isClosePausedRef\n    ]);\n    const getSortedTabbableCandidates = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(({ tabbingDirection })=>{\n        const toastItems = getItems();\n        const tabbableCandidates = toastItems.map((toastItem)=>{\n            const toastNode = toastItem.ref.current;\n            const toastTabbableCandidates = [\n                toastNode,\n                ...getTabbableCandidates(toastNode)\n            ];\n            return tabbingDirection === \"forwards\" ? toastTabbableCandidates : toastTabbableCandidates.reverse();\n        });\n        return (tabbingDirection === \"forwards\" ? tabbableCandidates.reverse() : tabbableCandidates).flat();\n    }, [\n        getItems\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = ref.current;\n        if (viewport) {\n            const handleKeyDown = (event)=>{\n                const isMetaKey = event.altKey || event.ctrlKey || event.metaKey;\n                const isTabKey = event.key === \"Tab\" && !isMetaKey;\n                if (isTabKey) {\n                    const focusedElement = document.activeElement;\n                    const isTabbingBackwards = event.shiftKey;\n                    const targetIsViewport = event.target === viewport;\n                    if (targetIsViewport && isTabbingBackwards) {\n                        headFocusProxyRef.current?.focus();\n                        return;\n                    }\n                    const tabbingDirection = isTabbingBackwards ? \"backwards\" : \"forwards\";\n                    const sortedCandidates = getSortedTabbableCandidates({\n                        tabbingDirection\n                    });\n                    const index = sortedCandidates.findIndex((candidate)=>candidate === focusedElement);\n                    if (focusFirst(sortedCandidates.slice(index + 1))) {\n                        event.preventDefault();\n                    } else {\n                        isTabbingBackwards ? headFocusProxyRef.current?.focus() : tailFocusProxyRef.current?.focus();\n                    }\n                }\n            };\n            viewport.addEventListener(\"keydown\", handleKeyDown);\n            return ()=>viewport.removeEventListener(\"keydown\", handleKeyDown);\n        }\n    }, [\n        getItems,\n        getSortedTabbableCandidates\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Branch, {\n        ref: wrapperRef,\n        role: \"region\",\n        \"aria-label\": label.replace(\"{hotkey}\", hotkeyLabel),\n        tabIndex: -1,\n        style: {\n            pointerEvents: hasToasts ? void 0 : \"none\"\n        },\n        children: [\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: headFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"forwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeToast,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.ol, {\n                    tabIndex: -1,\n                    ...viewportProps,\n                    ref: composedRefs\n                })\n            }),\n            hasToasts && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(FocusProxy, {\n                ref: tailFocusProxyRef,\n                onFocusFromOutsideViewport: ()=>{\n                    const tabbableCandidates = getSortedTabbableCandidates({\n                        tabbingDirection: \"backwards\"\n                    });\n                    focusFirst(tabbableCandidates);\n                }\n            })\n        ]\n    });\n});\nToastViewport.displayName = VIEWPORT_NAME;\nvar FOCUS_PROXY_NAME = \"ToastFocusProxy\";\nvar FocusProxy = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, onFocusFromOutsideViewport, ...proxyProps } = props;\n    const context = useToastProviderContext(FOCUS_PROXY_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n        \"aria-hidden\": true,\n        tabIndex: 0,\n        ...proxyProps,\n        ref: forwardedRef,\n        style: {\n            position: \"fixed\"\n        },\n        onFocus: (event)=>{\n            const prevFocusedElement = event.relatedTarget;\n            const isFocusFromOutsideViewport = !context.viewport?.contains(prevFocusedElement);\n            if (isFocusFromOutsideViewport) onFocusFromOutsideViewport();\n        }\n    });\n});\nFocusProxy.displayName = FOCUS_PROXY_NAME;\nvar TOAST_NAME = \"Toast\";\nvar TOAST_SWIPE_START = \"toast.swipeStart\";\nvar TOAST_SWIPE_MOVE = \"toast.swipeMove\";\nvar TOAST_SWIPE_CANCEL = \"toast.swipeCancel\";\nvar TOAST_SWIPE_END = \"toast.swipeEnd\";\nvar Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, open: openProp, defaultOpen, onOpenChange, ...toastProps } = props;\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_9__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? true,\n        onChange: onOpenChange,\n        caller: TOAST_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_10__.Presence, {\n        present: forceMount || open,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastImpl, {\n            open,\n            ...toastProps,\n            ref: forwardedRef,\n            onClose: ()=>setOpen(false),\n            onPause: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onPause),\n            onResume: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(props.onResume),\n            onSwipeStart: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeStart, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"start\");\n            }),\n            onSwipeMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeMove, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"move\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-move-y\", `${y}px`);\n            }),\n            onSwipeCancel: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeCancel, (event)=>{\n                event.currentTarget.setAttribute(\"data-swipe\", \"cancel\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-end-y\");\n            }),\n            onSwipeEnd: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onSwipeEnd, (event)=>{\n                const { x, y } = event.detail.delta;\n                event.currentTarget.setAttribute(\"data-swipe\", \"end\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-x\");\n                event.currentTarget.style.removeProperty(\"--radix-toast-swipe-move-y\");\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-x\", `${x}px`);\n                event.currentTarget.style.setProperty(\"--radix-toast-swipe-end-y\", `${y}px`);\n                setOpen(false);\n            })\n        })\n    });\n});\nToast.displayName = TOAST_NAME;\nvar [ToastInteractiveProvider, useToastInteractiveContext] = createToastContext(TOAST_NAME, {\n    onClose () {}\n});\nvar ToastImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, type = \"foreground\", duration: durationProp, open, onClose, onEscapeKeyDown, onPause, onResume, onSwipeStart, onSwipeMove, onSwipeCancel, onSwipeEnd, ...toastProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_5__.useComposedRefs)(forwardedRef, (node2)=>setNode(node2));\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const swipeDeltaRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const duration = durationProp || context.duration;\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const closeTimerRemainingTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(duration);\n    const closeTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const { onToastAdd, onToastRemove } = context;\n    const handleClose = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(()=>{\n        const isFocusInToast = node?.contains(document.activeElement);\n        if (isFocusInToast) context.viewport?.focus();\n        onClose();\n    });\n    const startTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((duration2)=>{\n        if (!duration2 || duration2 === Infinity) return;\n        window.clearTimeout(closeTimerRef.current);\n        closeTimerStartTimeRef.current = /* @__PURE__ */ new Date().getTime();\n        closeTimerRef.current = window.setTimeout(handleClose, duration2);\n    }, [\n        handleClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = context.viewport;\n        if (viewport) {\n            const handleResume = ()=>{\n                startTimer(closeTimerRemainingTimeRef.current);\n                onResume?.();\n            };\n            const handlePause = ()=>{\n                const elapsedTime = /* @__PURE__ */ new Date().getTime() - closeTimerStartTimeRef.current;\n                closeTimerRemainingTimeRef.current = closeTimerRemainingTimeRef.current - elapsedTime;\n                window.clearTimeout(closeTimerRef.current);\n                onPause?.();\n            };\n            viewport.addEventListener(VIEWPORT_PAUSE, handlePause);\n            viewport.addEventListener(VIEWPORT_RESUME, handleResume);\n            return ()=>{\n                viewport.removeEventListener(VIEWPORT_PAUSE, handlePause);\n                viewport.removeEventListener(VIEWPORT_RESUME, handleResume);\n            };\n        }\n    }, [\n        context.viewport,\n        duration,\n        onPause,\n        onResume,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (open && !context.isClosePausedRef.current) startTimer(duration);\n    }, [\n        open,\n        duration,\n        context.isClosePausedRef,\n        startTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        onToastAdd();\n        return ()=>onToastRemove();\n    }, [\n        onToastAdd,\n        onToastRemove\n    ]);\n    const announceTextContent = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        return node ? getAnnounceTextContent(node) : null;\n    }, [\n        node\n    ]);\n    if (!context.viewport) return null;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            announceTextContent && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounce, {\n                __scopeToast,\n                role: \"status\",\n                \"aria-live\": type === \"foreground\" ? \"assertive\" : \"polite\",\n                \"aria-atomic\": true,\n                children: announceTextContent\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastInteractiveProvider, {\n                scope: __scopeToast,\n                onClose: handleClose,\n                children: /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n                    scope: __scopeToast,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_6__.Root, {\n                        asChild: true,\n                        onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onEscapeKeyDown, ()=>{\n                            if (!context.isFocusedToastEscapeKeyDownRef.current) handleClose();\n                            context.isFocusedToastEscapeKeyDownRef.current = false;\n                        }),\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.li, {\n                            role: \"status\",\n                            \"aria-live\": \"off\",\n                            \"aria-atomic\": true,\n                            tabIndex: 0,\n                            \"data-state\": open ? \"open\" : \"closed\",\n                            \"data-swipe-direction\": context.swipeDirection,\n                            ...toastProps,\n                            ref: composedRefs,\n                            style: {\n                                userSelect: \"none\",\n                                touchAction: \"none\",\n                                ...props.style\n                            },\n                            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                                if (event.key !== \"Escape\") return;\n                                onEscapeKeyDown?.(event.nativeEvent);\n                                if (!event.nativeEvent.defaultPrevented) {\n                                    context.isFocusedToastEscapeKeyDownRef.current = true;\n                                    handleClose();\n                                }\n                            }),\n                            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                                if (event.button !== 0) return;\n                                pointerStartRef.current = {\n                                    x: event.clientX,\n                                    y: event.clientY\n                                };\n                            }),\n                            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                                if (!pointerStartRef.current) return;\n                                const x = event.clientX - pointerStartRef.current.x;\n                                const y = event.clientY - pointerStartRef.current.y;\n                                const hasSwipeMoveStarted = Boolean(swipeDeltaRef.current);\n                                const isHorizontalSwipe = [\n                                    \"left\",\n                                    \"right\"\n                                ].includes(context.swipeDirection);\n                                const clamp = [\n                                    \"left\",\n                                    \"up\"\n                                ].includes(context.swipeDirection) ? Math.min : Math.max;\n                                const clampedX = isHorizontalSwipe ? clamp(0, x) : 0;\n                                const clampedY = !isHorizontalSwipe ? clamp(0, y) : 0;\n                                const moveStartBuffer = event.pointerType === \"touch\" ? 10 : 2;\n                                const delta = {\n                                    x: clampedX,\n                                    y: clampedY\n                                };\n                                const eventDetail = {\n                                    originalEvent: event,\n                                    delta\n                                };\n                                if (hasSwipeMoveStarted) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_MOVE, onSwipeMove, eventDetail, {\n                                        discrete: false\n                                    });\n                                } else if (isDeltaInDirection(delta, context.swipeDirection, moveStartBuffer)) {\n                                    swipeDeltaRef.current = delta;\n                                    handleAndDispatchCustomEvent(TOAST_SWIPE_START, onSwipeStart, eventDetail, {\n                                        discrete: false\n                                    });\n                                    event.target.setPointerCapture(event.pointerId);\n                                } else if (Math.abs(x) > moveStartBuffer || Math.abs(y) > moveStartBuffer) {\n                                    pointerStartRef.current = null;\n                                }\n                            }),\n                            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                                const delta = swipeDeltaRef.current;\n                                const target = event.target;\n                                if (target.hasPointerCapture(event.pointerId)) {\n                                    target.releasePointerCapture(event.pointerId);\n                                }\n                                swipeDeltaRef.current = null;\n                                pointerStartRef.current = null;\n                                if (delta) {\n                                    const toast = event.currentTarget;\n                                    const eventDetail = {\n                                        originalEvent: event,\n                                        delta\n                                    };\n                                    if (isDeltaInDirection(delta, context.swipeDirection, context.swipeThreshold)) {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_END, onSwipeEnd, eventDetail, {\n                                            discrete: true\n                                        });\n                                    } else {\n                                        handleAndDispatchCustomEvent(TOAST_SWIPE_CANCEL, onSwipeCancel, eventDetail, {\n                                            discrete: true\n                                        });\n                                    }\n                                    toast.addEventListener(\"click\", (event2)=>event2.preventDefault(), {\n                                        once: true\n                                    });\n                                }\n                            })\n                        })\n                    })\n                }), context.viewport)\n            })\n        ]\n    });\n});\nvar ToastAnnounce = (props)=>{\n    const { __scopeToast, children, ...announceProps } = props;\n    const context = useToastProviderContext(TOAST_NAME, __scopeToast);\n    const [renderAnnounceText, setRenderAnnounceText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isAnnounced, setIsAnnounced] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    useNextFrame(()=>setRenderAnnounceText(true));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const timer = window.setTimeout(()=>setIsAnnounced(true), 1e3);\n        return ()=>window.clearTimeout(timer);\n    }, []);\n    return isAnnounced ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_8__.VisuallyHidden, {\n            ...announceProps,\n            children: renderAnnounceText && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n                children: [\n                    context.label,\n                    \" \",\n                    children\n                ]\n            })\n        })\n    });\n};\nvar TITLE_NAME = \"ToastTitle\";\nvar ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...titleProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...titleProps,\n        ref: forwardedRef\n    });\n});\nToastTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"ToastDescription\";\nvar ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...descriptionProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        ...descriptionProps,\n        ref: forwardedRef\n    });\n});\nToastDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"ToastAction\";\nvar ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { altText, ...actionProps } = props;\n    if (!altText.trim()) {\n        console.error(`Invalid prop \\`altText\\` supplied to \\`${ACTION_NAME}\\`. Expected non-empty \\`string\\`.`);\n        return null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        altText,\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastClose, {\n            ...actionProps,\n            ref: forwardedRef\n        })\n    });\n});\nToastAction.displayName = ACTION_NAME;\nvar CLOSE_NAME = \"ToastClose\";\nvar ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, ...closeProps } = props;\n    const interactiveContext = useToastInteractiveContext(CLOSE_NAME, __scopeToast);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ToastAnnounceExclude, {\n        asChild: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            type: \"button\",\n            ...closeProps,\n            ref: forwardedRef,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, interactiveContext.onClose)\n        })\n    });\n});\nToastClose.displayName = CLOSE_NAME;\nvar ToastAnnounceExclude = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeToast, altText, ...announceExcludeProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.div, {\n        \"data-radix-toast-announce-exclude\": \"\",\n        \"data-radix-toast-announce-alt\": altText || void 0,\n        ...announceExcludeProps,\n        ref: forwardedRef\n    });\n});\nfunction getAnnounceTextContent(container) {\n    const textContent = [];\n    const childNodes = Array.from(container.childNodes);\n    childNodes.forEach((node)=>{\n        if (node.nodeType === node.TEXT_NODE && node.textContent) textContent.push(node.textContent);\n        if (isHTMLElement(node)) {\n            const isHidden = node.ariaHidden || node.hidden || node.style.display === \"none\";\n            const isExcluded = node.dataset.radixToastAnnounceExclude === \"\";\n            if (!isHidden) {\n                if (isExcluded) {\n                    const altText = node.dataset.radixToastAnnounceAlt;\n                    if (altText) textContent.push(altText);\n                } else {\n                    textContent.push(...getAnnounceTextContent(node));\n                }\n            }\n        }\n    });\n    return textContent;\n}\nfunction handleAndDispatchCustomEvent(name, handler, detail, { discrete }) {\n    const currentTarget = detail.originalEvent.currentTarget;\n    const event = new CustomEvent(name, {\n        bubbles: true,\n        cancelable: true,\n        detail\n    });\n    if (handler) currentTarget.addEventListener(name, handler, {\n        once: true\n    });\n    if (discrete) {\n        (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.dispatchDiscreteCustomEvent)(currentTarget, event);\n    } else {\n        currentTarget.dispatchEvent(event);\n    }\n}\nvar isDeltaInDirection = (delta, direction, threshold = 0)=>{\n    const deltaX = Math.abs(delta.x);\n    const deltaY = Math.abs(delta.y);\n    const isDeltaX = deltaX > deltaY;\n    if (direction === \"left\" || direction === \"right\") {\n        return isDeltaX && deltaX > threshold;\n    } else {\n        return !isDeltaX && deltaY > threshold;\n    }\n};\nfunction useNextFrame(callback = ()=>{}) {\n    const fn = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_11__.useCallbackRef)(callback);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_14__.useLayoutEffect)(()=>{\n        let raf1 = 0;\n        let raf2 = 0;\n        raf1 = window.requestAnimationFrame(()=>raf2 = window.requestAnimationFrame(fn));\n        return ()=>{\n            window.cancelAnimationFrame(raf1);\n            window.cancelAnimationFrame(raf2);\n        };\n    }, [\n        fn\n    ]);\n}\nfunction isHTMLElement(node) {\n    return node.nodeType === node.ELEMENT_NODE;\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction focusFirst(candidates) {\n    const previouslyFocusedElement = document.activeElement;\n    return candidates.some((candidate)=>{\n        if (candidate === previouslyFocusedElement) return true;\n        candidate.focus();\n        return document.activeElement !== previouslyFocusedElement;\n    });\n}\nvar Provider = ToastProvider;\nvar Viewport = ToastViewport;\nvar Root2 = Toast;\nvar Title = ToastTitle;\nvar Description = ToastDescription;\nvar Action = ToastAction;\nvar Close = ToastClose;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-toast@1.2.14_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_f2d63fe9a772cc94531d3b740a819e3d/node_modules/@radix-ui/react-toast/dist/index.mjs\n");

/***/ })

};
;