"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+r_8ed47621286c24058c66cffcdce48db5";
exports.ids = ["vendor-chunks/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+r_8ed47621286c24058c66cffcdce48db5"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+r_8ed47621286c24058c66cffcdce48db5/node_modules/@radix-ui/react-collection/dist/index.mjs":
/*!************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+r_8ed47621286c24058c66cffcdce48db5/node_modules/@radix-ui/react-collection/dist/index.mjs ***!
  \************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollection: () => (/* binding */ createCollection),\n/* harmony export */   unstable_createCollection: () => (/* binding */ createCollection2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ createCollection,unstable_createCollection auto */ // src/collection-legacy.tsx\n\n\n\n\n\nfunction createCollection(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionProviderImpl, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionRef: {\n            current: null\n        },\n        itemMap: /* @__PURE__ */ new Map()\n    });\n    const CollectionProvider = (props)=>{\n        const { scope, children } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const itemMap = react__WEBPACK_IMPORTED_MODULE_0__.useRef(/* @__PURE__ */ new Map()).current;\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            scope,\n            itemMap,\n            collectionRef: ref,\n            children\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            context.itemMap.set(ref, {\n                ref,\n                ...itemData\n            });\n            return ()=>void context.itemMap.delete(ref);\n        });\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useCollection(scope) {\n        const context = useCollectionContext(name + \"CollectionConsumer\", scope);\n        const getItems = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            const collectionNode = context.collectionRef.current;\n            if (!collectionNode) return [];\n            const orderedNodes = Array.from(collectionNode.querySelectorAll(`[${ITEM_DATA_ATTR}]`));\n            const items = Array.from(context.itemMap.values());\n            const orderedItems = items.sort((a, b)=>orderedNodes.indexOf(a.ref.current) - orderedNodes.indexOf(b.ref.current));\n            return orderedItems;\n        }, [\n            context.collectionRef,\n            context.itemMap\n        ]);\n        return getItems;\n    }\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        useCollection,\n        createCollectionScope\n    ];\n}\n// src/collection.tsx\n\n\n\n\n// src/ordered-dictionary.ts\nvar __instanciated = /* @__PURE__ */ new WeakMap();\nvar OrderedDict = class _OrderedDict extends Map {\n    #keys;\n    constructor(entries){\n        super(entries);\n        this.#keys = [\n            ...super.keys()\n        ];\n        __instanciated.set(this, true);\n    }\n    set(key, value) {\n        if (__instanciated.get(this)) {\n            if (this.has(key)) {\n                this.#keys[this.#keys.indexOf(key)] = key;\n            } else {\n                this.#keys.push(key);\n            }\n        }\n        super.set(key, value);\n        return this;\n    }\n    insert(index, key, value) {\n        const has = this.has(key);\n        const length = this.#keys.length;\n        const relativeIndex = toSafeInteger(index);\n        let actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n        const safeIndex = actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n        if (safeIndex === this.size || has && safeIndex === this.size - 1 || safeIndex === -1) {\n            this.set(key, value);\n            return this;\n        }\n        const size = this.size + (has ? 0 : 1);\n        if (relativeIndex < 0) {\n            actualIndex++;\n        }\n        const keys = [\n            ...this.#keys\n        ];\n        let nextValue;\n        let shouldSkip = false;\n        for(let i = actualIndex; i < size; i++){\n            if (actualIndex === i) {\n                let nextKey = keys[i];\n                if (keys[i] === key) {\n                    nextKey = keys[i + 1];\n                }\n                if (has) {\n                    this.delete(key);\n                }\n                nextValue = this.get(nextKey);\n                this.set(key, value);\n            } else {\n                if (!shouldSkip && keys[i - 1] === key) {\n                    shouldSkip = true;\n                }\n                const currentKey = keys[shouldSkip ? i : i - 1];\n                const currentValue = nextValue;\n                nextValue = this.get(currentKey);\n                this.delete(currentKey);\n                this.set(currentKey, currentValue);\n            }\n        }\n        return this;\n    }\n    with(index, key, value) {\n        const copy = new _OrderedDict(this);\n        copy.insert(index, key, value);\n        return copy;\n    }\n    before(key) {\n        const index = this.#keys.indexOf(key) - 1;\n        if (index < 0) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position before the given key.\n   */ setBefore(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index, newKey, value);\n    }\n    after(key) {\n        let index = this.#keys.indexOf(key);\n        index = index === -1 || index === this.size - 1 ? -1 : index + 1;\n        if (index === -1) {\n            return void 0;\n        }\n        return this.entryAt(index);\n    }\n    /**\n   * Sets a new key-value pair at the position after the given key.\n   */ setAfter(key, newKey, value) {\n        const index = this.#keys.indexOf(key);\n        if (index === -1) {\n            return this;\n        }\n        return this.insert(index + 1, newKey, value);\n    }\n    first() {\n        return this.entryAt(0);\n    }\n    last() {\n        return this.entryAt(-1);\n    }\n    clear() {\n        this.#keys = [];\n        return super.clear();\n    }\n    delete(key) {\n        const deleted = super.delete(key);\n        if (deleted) {\n            this.#keys.splice(this.#keys.indexOf(key), 1);\n        }\n        return deleted;\n    }\n    deleteAt(index) {\n        const key = this.keyAt(index);\n        if (key !== void 0) {\n            return this.delete(key);\n        }\n        return false;\n    }\n    at(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return this.get(key);\n        }\n    }\n    entryAt(index) {\n        const key = at(this.#keys, index);\n        if (key !== void 0) {\n            return [\n                key,\n                this.get(key)\n            ];\n        }\n    }\n    indexOf(key) {\n        return this.#keys.indexOf(key);\n    }\n    keyAt(index) {\n        return at(this.#keys, index);\n    }\n    from(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.at(dest);\n    }\n    keyFrom(key, offset) {\n        const index = this.indexOf(key);\n        if (index === -1) {\n            return void 0;\n        }\n        let dest = index + offset;\n        if (dest < 0) dest = 0;\n        if (dest >= this.size) dest = this.size - 1;\n        return this.keyAt(dest);\n    }\n    find(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return entry;\n            }\n            index++;\n        }\n        return void 0;\n    }\n    findIndex(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return index;\n            }\n            index++;\n        }\n        return -1;\n    }\n    filter(predicate, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                entries.push(entry);\n            }\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    map(callbackfn, thisArg) {\n        const entries = [];\n        let index = 0;\n        for (const entry of this){\n            entries.push([\n                entry[0],\n                Reflect.apply(callbackfn, thisArg, [\n                    entry,\n                    index,\n                    this\n                ])\n            ]);\n            index++;\n        }\n        return new _OrderedDict(entries);\n    }\n    reduce(...args) {\n        const [callbackfn, initialValue] = args;\n        let index = 0;\n        let accumulator = initialValue ?? this.at(0);\n        for (const entry of this){\n            if (index === 0 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n            index++;\n        }\n        return accumulator;\n    }\n    reduceRight(...args) {\n        const [callbackfn, initialValue] = args;\n        let accumulator = initialValue ?? this.at(-1);\n        for(let index = this.size - 1; index >= 0; index--){\n            const entry = this.at(index);\n            if (index === this.size - 1 && args.length === 1) {\n                accumulator = entry;\n            } else {\n                accumulator = Reflect.apply(callbackfn, this, [\n                    accumulator,\n                    entry,\n                    index,\n                    this\n                ]);\n            }\n        }\n        return accumulator;\n    }\n    toSorted(compareFn) {\n        const entries = [\n            ...this.entries()\n        ].sort(compareFn);\n        return new _OrderedDict(entries);\n    }\n    toReversed() {\n        const reversed = new _OrderedDict();\n        for(let index = this.size - 1; index >= 0; index--){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            reversed.set(key, element);\n        }\n        return reversed;\n    }\n    toSpliced(...args) {\n        const entries = [\n            ...this.entries()\n        ];\n        entries.splice(...args);\n        return new _OrderedDict(entries);\n    }\n    slice(start, end) {\n        const result = new _OrderedDict();\n        let stop = this.size - 1;\n        if (start === void 0) {\n            return result;\n        }\n        if (start < 0) {\n            start = start + this.size;\n        }\n        if (end !== void 0 && end > 0) {\n            stop = end - 1;\n        }\n        for(let index = start; index <= stop; index++){\n            const key = this.keyAt(index);\n            const element = this.get(key);\n            result.set(key, element);\n        }\n        return result;\n    }\n    every(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (!Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return false;\n            }\n            index++;\n        }\n        return true;\n    }\n    some(predicate, thisArg) {\n        let index = 0;\n        for (const entry of this){\n            if (Reflect.apply(predicate, thisArg, [\n                entry,\n                index,\n                this\n            ])) {\n                return true;\n            }\n            index++;\n        }\n        return false;\n    }\n};\nfunction at(array, index) {\n    if (\"at\" in Array.prototype) {\n        return Array.prototype.at.call(array, index);\n    }\n    const actualIndex = toSafeIndex(array, index);\n    return actualIndex === -1 ? void 0 : array[actualIndex];\n}\nfunction toSafeIndex(array, index) {\n    const length = array.length;\n    const relativeIndex = toSafeInteger(index);\n    const actualIndex = relativeIndex >= 0 ? relativeIndex : length + relativeIndex;\n    return actualIndex < 0 || actualIndex >= length ? -1 : actualIndex;\n}\nfunction toSafeInteger(number) {\n    return number !== number || number === 0 ? 0 : Math.trunc(number);\n}\n// src/collection.tsx\n\nfunction createCollection2(name) {\n    const PROVIDER_NAME = name + \"CollectionProvider\";\n    const [createCollectionContext, createCollectionScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(PROVIDER_NAME);\n    const [CollectionContextProvider, useCollectionContext] = createCollectionContext(PROVIDER_NAME, {\n        collectionElement: null,\n        collectionRef: {\n            current: null\n        },\n        collectionRefObject: {\n            current: null\n        },\n        itemMap: new OrderedDict(),\n        setItemMap: ()=>void 0\n    });\n    const CollectionProvider = ({ state, ...props })=>{\n        return state ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionInit, {\n            ...props\n        });\n    };\n    CollectionProvider.displayName = PROVIDER_NAME;\n    const CollectionInit = (props)=>{\n        const state = useInitCollection();\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionProviderImpl, {\n            ...props,\n            state\n        });\n    };\n    CollectionInit.displayName = PROVIDER_NAME + \"Init\";\n    const CollectionProviderImpl = (props)=>{\n        const { scope, children, state } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [collectionElement, setCollectionElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(ref, setCollectionElement);\n        const [itemMap, setItemMap] = state;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            if (!collectionElement) return;\n            const observer = getChildListObserver(()=>{});\n            observer.observe(collectionElement, {\n                childList: true,\n                subtree: true\n            });\n            return ()=>{\n                observer.disconnect();\n            };\n        }, [\n            collectionElement\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionContextProvider, {\n            scope,\n            itemMap,\n            setItemMap,\n            collectionRef: composeRefs,\n            collectionRefObject: ref,\n            collectionElement,\n            children\n        });\n    };\n    CollectionProviderImpl.displayName = PROVIDER_NAME + \"Impl\";\n    const COLLECTION_SLOT_NAME = name + \"CollectionSlot\";\n    const CollectionSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(COLLECTION_SLOT_NAME);\n    const CollectionSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children } = props;\n        const context = useCollectionContext(COLLECTION_SLOT_NAME, scope);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, context.collectionRef);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionSlotImpl, {\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionSlot.displayName = COLLECTION_SLOT_NAME;\n    const ITEM_SLOT_NAME = name + \"CollectionItemSlot\";\n    const ITEM_DATA_ATTR = \"data-radix-collection-item\";\n    const CollectionItemSlotImpl = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.createSlot)(ITEM_SLOT_NAME);\n    const CollectionItemSlot = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n        const { scope, children, ...itemData } = props;\n        const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n        const [element, setElement] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n        const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, ref, setElement);\n        const context = useCollectionContext(ITEM_SLOT_NAME, scope);\n        const { setItemMap } = context;\n        const itemDataRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(itemData);\n        if (!shallowEqual(itemDataRef.current, itemData)) {\n            itemDataRef.current = itemData;\n        }\n        const memoizedItemData = itemDataRef.current;\n        react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n            const itemData2 = memoizedItemData;\n            setItemMap((map)=>{\n                if (!element) {\n                    return map;\n                }\n                if (!map.has(element)) {\n                    map.set(element, {\n                        ...itemData2,\n                        element\n                    });\n                    return map.toSorted(sortByDocumentPosition);\n                }\n                return map.set(element, {\n                    ...itemData2,\n                    element\n                }).toSorted(sortByDocumentPosition);\n            });\n            return ()=>{\n                setItemMap((map)=>{\n                    if (!element || !map.has(element)) {\n                        return map;\n                    }\n                    map.delete(element);\n                    return new OrderedDict(map);\n                });\n            };\n        }, [\n            element,\n            memoizedItemData,\n            setItemMap\n        ]);\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollectionItemSlotImpl, {\n            ...{\n                [ITEM_DATA_ATTR]: \"\"\n            },\n            ref: composedRefs,\n            children\n        });\n    });\n    CollectionItemSlot.displayName = ITEM_SLOT_NAME;\n    function useInitCollection() {\n        return react__WEBPACK_IMPORTED_MODULE_0__.useState(new OrderedDict());\n    }\n    function useCollection(scope) {\n        const { itemMap } = useCollectionContext(name + \"CollectionConsumer\", scope);\n        return itemMap;\n    }\n    const functions = {\n        createCollectionScope,\n        useCollection,\n        useInitCollection\n    };\n    return [\n        {\n            Provider: CollectionProvider,\n            Slot: CollectionSlot,\n            ItemSlot: CollectionItemSlot\n        },\n        functions\n    ];\n}\nfunction shallowEqual(a, b) {\n    if (a === b) return true;\n    if (typeof a !== \"object\" || typeof b !== \"object\") return false;\n    if (a == null || b == null) return false;\n    const keysA = Object.keys(a);\n    const keysB = Object.keys(b);\n    if (keysA.length !== keysB.length) return false;\n    for (const key of keysA){\n        if (!Object.prototype.hasOwnProperty.call(b, key)) return false;\n        if (a[key] !== b[key]) return false;\n    }\n    return true;\n}\nfunction isElementPreceding(a, b) {\n    return !!(b.compareDocumentPosition(a) & Node.DOCUMENT_POSITION_PRECEDING);\n}\nfunction sortByDocumentPosition(a, b) {\n    return !a[1].element || !b[1].element ? 0 : isElementPreceding(a[1].element, b[1].element) ? -1 : 1;\n}\nfunction getChildListObserver(callback) {\n    const observer = new MutationObserver((mutationsList)=>{\n        for (const mutation of mutationsList){\n            if (mutation.type === \"childList\") {\n                callback();\n                return;\n            }\n        }\n    });\n    return observer;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+r_8ed47621286c24058c66cffcdce48db5/node_modules/@radix-ui/react-collection/dist/index.mjs\n");

/***/ })

};
;