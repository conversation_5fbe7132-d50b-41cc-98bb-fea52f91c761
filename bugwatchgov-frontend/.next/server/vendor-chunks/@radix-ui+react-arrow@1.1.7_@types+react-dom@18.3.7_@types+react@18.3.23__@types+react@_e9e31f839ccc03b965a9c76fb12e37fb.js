"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-arrow@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_e9e31f839ccc03b965a9c76fb12e37fb";
exports.ids = ["vendor-chunks/@radix-ui+react-arrow@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_e9e31f839ccc03b965a9c76fb12e37fb"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-arrow@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_e9e31f839ccc03b965a9c76fb12e37fb/node_modules/@radix-ui/react-arrow/dist/index.mjs":
/*!*******************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-arrow@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_e9e31f839ccc03b965a9c76fb12e37fb/node_modules/@radix-ui/react-arrow/dist/index.mjs ***!
  \*******************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23__@types+re_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// src/arrow.tsx\n\n\n\nvar NAME = \"Arrow\";\nvar Arrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { children, width = 10, height = 5, ...arrowProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_2__.Primitive.svg, {\n        ...arrowProps,\n        ref: forwardedRef,\n        width,\n        height,\n        viewBox: \"0 0 30 10\",\n        preserveAspectRatio: \"none\",\n        children: props.asChild ? children : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"polygon\", {\n            points: \"0,0 30,0 15,10\"\n        })\n    });\n});\nArrow.displayName = NAME;\nvar Root = Arrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-arrow@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react@_e9e31f839ccc03b965a9c76fb12e37fb/node_modules/@radix-ui/react-arrow/dist/index.mjs\n");

/***/ })

};
;