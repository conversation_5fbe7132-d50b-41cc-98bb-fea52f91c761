/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?20e6\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\")), \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"/home/<USER>/Downloads/bugwatchgov-frontend/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fapp%2Fdashboard%2Fpage.tsx&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fapp%2Fdashboard%2Fpage.tsx&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(ssr)/./app/dashboard/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4wLjRfQGJhYmVsK2NvcmVANy4yNy40X3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSUyRmhvbWUlMkZtc2olMkZEb3dubG9hZHMlMkZidWd3YXRjaGdvdi1mcm9udGVuZCUyRmFwcCUyRmRhc2hib2FyZCUyRnBhZ2UudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL2J1Z3dhdGNoZ292LWZyb250ZW5kLz80ZmU5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvbXNqL0Rvd25sb2Fkcy9idWd3YXRjaGdvdi1mcm9udGVuZC9hcHAvZGFzaGJvYXJkL3BhZ2UudHN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fapp%2Fdashboard%2Fpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fcomponents%2Fui%2Ftoaster.tsx&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Ffeatures%2Fauth%2Fauth-context.tsx&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fstyles%2Fglobals.css&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fcomponents%2Fui%2Ftoaster.tsx&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Ffeatures%2Fauth%2Fauth-context.tsx&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fstyles%2Fglobals.css&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ui/toaster.tsx */ \"(ssr)/./components/ui/toaster.tsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./features/auth/auth-context.tsx */ \"(ssr)/./features/auth/auth-context.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNC4wLjRfQGJhYmVsK2NvcmVANy4yNy40X3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjEvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSUyRmhvbWUlMkZtc2olMkZEb3dubG9hZHMlMkZidWd3YXRjaGdvdi1mcm9udGVuZCUyRmNvbXBvbmVudHMlMkZ1aSUyRnRvYXN0ZXIudHN4Jm1vZHVsZXM9JTJGaG9tZSUyRm1zaiUyRkRvd25sb2FkcyUyRmJ1Z3dhdGNoZ292LWZyb250ZW5kJTJGZmVhdHVyZXMlMkZhdXRoJTJGYXV0aC1jb250ZXh0LnRzeCZtb2R1bGVzPSUyRmhvbWUlMkZtc2olMkZEb3dubG9hZHMlMkZidWd3YXRjaGdvdi1mcm9udGVuZCUyRm5vZGVfbW9kdWxlcyUyRi5wbnBtJTJGbmV4dCU0MDE0LjAuNF8lNDBiYWJlbCUyQmNvcmUlNDA3LjI3LjRfcmVhY3QtZG9tJTQwMTguMy4xX3JlYWN0JTQwMTguMy4xX19yZWFjdCU0MDE4LjMuMSUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiUyMnBhdGglMjIlM0ElMjJhcHAlMkZsYXlvdXQudHN4JTIyJTJDJTIyaW1wb3J0JTIyJTNBJTIySW50ZXIlMjIlMkMlMjJhcmd1bWVudHMlMjIlM0ElNUIlN0IlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPSUyRmhvbWUlMkZtc2olMkZEb3dubG9hZHMlMkZidWd3YXRjaGdvdi1mcm9udGVuZCUyRnN0eWxlcyUyRmdsb2JhbHMuY3NzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBdUc7QUFDdkciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWd3YXRjaGdvdi1mcm9udGVuZC8/MGM0MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9ob21lL21zai9Eb3dubG9hZHMvYnVnd2F0Y2hnb3YtZnJvbnRlbmQvY29tcG9uZW50cy91aS90b2FzdGVyLnRzeFwiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL2hvbWUvbXNqL0Rvd25sb2Fkcy9idWd3YXRjaGdvdi1mcm9udGVuZC9mZWF0dXJlcy9hdXRoL2F1dGgtY29udGV4dC50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fcomponents%2Fui%2Ftoaster.tsx&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Ffeatures%2Fauth%2Fauth-context.tsx&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%22path%22%3A%22app%2Flayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fstyles%2Fglobals.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js&modules=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fnode_modules%2F.pnpm%2Fnext%4014.0.4_%40babel%2Bcore%407.27.4_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _features_auth_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/features/auth/auth-context */ \"(ssr)/./features/auth/auth-context.tsx\");\n/* harmony import */ var _components_layout_app_shell__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/app-shell */ \"(ssr)/./components/layout/app-shell.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _features_bug_bug_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/features/bug/bug-card */ \"(ssr)/./features/bug/bug-card.tsx\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api-client */ \"(ssr)/./lib/api-client.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Plus!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Plus!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Plus!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Plus!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Bug,CheckCircle,Clock,Plus!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    const { user, loading } = (0,_features_auth_auth_context__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [reports, setReports] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading && !user) {\n            router.push(\"/auth/signin\");\n            return;\n        }\n        if (user) {\n            loadReports();\n        }\n    }, [\n        user,\n        loading,\n        router\n    ]);\n    const loadReports = async ()=>{\n        try {\n            setIsLoading(true);\n            const data = await _lib_api_client__WEBPACK_IMPORTED_MODULE_8__.ApiClient.getUserReports();\n            setReports(data);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to load reports\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (loading || !user) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                lineNumber: 47,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this);\n    }\n    const openReports = reports.filter((r)=>r.status === \"open\");\n    const inProgressReports = reports.filter((r)=>r.status === \"in_progress\");\n    const resolvedReports = reports.filter((r)=>r.status === \"resolved\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_app_shell__WEBPACK_IMPORTED_MODULE_4__.AppShell, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold\",\n                            children: [\n                                \"Welcome back, \",\n                                user.email\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"Here's an overview of your bug reports and system status.\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            onClick: ()=>router.push(\"/report\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 13\n                                }, this),\n                                \"Report New Bug\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>router.push(\"/my-reports\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this),\n                                \"View All My Reports\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Total Reports\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: reports.length\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Open\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 text-red-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-red-600\",\n                                        children: openReports.length\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"In Progress\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 text-yellow-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-yellow-600\",\n                                        children: inProgressReports.length\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"Resolved\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 113,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-green-600\",\n                                        children: resolvedReports.length\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    children: \"Recent Reports\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardDescription, {\n                                    children: \"Your most recently submitted bug reports\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-muted-foreground\",\n                                        children: \"Loading reports...\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 15\n                            }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-600\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        variant: \"outline\",\n                                        onClick: loadReports,\n                                        className: \"mt-2\",\n                                        children: \"Try Again\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 15\n                            }, this) : reports.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Bug_CheckCircle_Clock_Plus_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-12 w-12 text-muted-foreground mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"No bug reports yet\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: ()=>router.push(\"/report\"),\n                                        className: \"mt-4\",\n                                        children: \"Report Your First Bug\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                children: reports.slice(0, 6).map((report)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_bug_bug_card__WEBPACK_IMPORTED_MODULE_7__.BugCard, {\n                                        bug: report,\n                                        onClick: ()=>router.push(`/reports/${report.id}`)\n                                    }, report.id, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/layout/app-shell.tsx":
/*!*****************************************!*\
  !*** ./components/layout/app-shell.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppShell: () => (/* binding */ AppShell)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_navigation_sidebar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/navigation/sidebar */ \"(ssr)/./components/navigation/sidebar.tsx\");\n/* harmony import */ var _components_navigation_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/navigation/header */ \"(ssr)/./components/navigation/header.tsx\");\n/* __next_internal_client_entry_do_not_use__ AppShell auto */ \n\n\nfunction AppShell({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-background\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_sidebar__WEBPACK_IMPORTED_MODULE_1__.Sidebar, {}, void 0, false, {\n                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/layout/app-shell.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col overflow-hidden lg:ml-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navigation_header__WEBPACK_IMPORTED_MODULE_2__.Header, {}, void 0, false, {\n                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/layout/app-shell.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-auto p-6\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/layout/app-shell.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/layout/app-shell.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/layout/app-shell.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL2xheW91dC9hcHAtc2hlbGwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUl5RDtBQUNGO0FBTWhELFNBQVNFLFNBQVMsRUFBRUMsUUFBUSxFQUFpQjtJQUNsRCxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNMLG1FQUFPQTs7Ozs7MEJBQ1IsOERBQUNJO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0osaUVBQU1BOzs7OztrQ0FDUCw4REFBQ0s7d0JBQUtELFdBQVU7a0NBQTRCRjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSXBEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVnd2F0Y2hnb3YtZnJvbnRlbmQvLi9jb21wb25lbnRzL2xheW91dC9hcHAtc2hlbGwudHN4P2Q4YzMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHR5cGUgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgU2lkZWJhciB9IGZyb20gXCJAL2NvbXBvbmVudHMvbmF2aWdhdGlvbi9zaWRlYmFyXCJcbmltcG9ydCB7IEhlYWRlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvbmF2aWdhdGlvbi9oZWFkZXJcIlxuXG5pbnRlcmZhY2UgQXBwU2hlbGxQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIEFwcFNoZWxsKHsgY2hpbGRyZW4gfTogQXBwU2hlbGxQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLXNjcmVlbiBiZy1iYWNrZ3JvdW5kXCI+XG4gICAgICA8U2lkZWJhciAvPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBmbGV4LWNvbCBvdmVyZmxvdy1oaWRkZW4gbGc6bWwtMFwiPlxuICAgICAgICA8SGVhZGVyIC8+XG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy1hdXRvIHAtNlwiPntjaGlsZHJlbn08L21haW4+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlNpZGViYXIiLCJIZWFkZXIiLCJBcHBTaGVsbCIsImNoaWxkcmVuIiwiZGl2IiwiY2xhc3NOYW1lIiwibWFpbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/layout/app-shell.tsx\n");

/***/ }),

/***/ "(ssr)/./components/navigation/header.tsx":
/*!******************************************!*\
  !*** ./components/navigation/header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _features_auth_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/features/auth/auth-context */ \"(ssr)/./features/auth/auth-context.tsx\");\n/* harmony import */ var _barrel_optimize_names_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,User!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\nfunction Header() {\n    const { user, signOut } = (0,_features_auth_auth_context__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n        } catch (error) {\n            console.error(\"Error signing out:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-background border-b\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between h-16 px-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold\",\n                        children: \"Government Bug Reporting System\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/header.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/header.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/header.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm\",\n                                    children: user?.email\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/header.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/header.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: handleSignOut,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/header.tsx\",\n                                    lineNumber: 31,\n                                    columnNumber: 13\n                                }, this),\n                                \"Sign Out\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/header.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/header.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/header.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/header.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/navigation/header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/navigation/sidebar.tsx":
/*!*******************************************!*\
  !*** ./components/navigation/sidebar.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _features_auth_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/features/auth/auth-context */ \"(ssr)/./features/auth/auth-context.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bug_Home_Menu_Plus_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bug,Home,Menu,Plus,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bug_Home_Menu_Plus_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bug,Home,Menu,Plus,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bug_Home_Menu_Plus_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bug,Home,Menu,Plus,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bug.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bug_Home_Menu_Plus_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bug,Home,Menu,Plus,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bug_Home_Menu_Plus_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bug,Home,Menu,Plus,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bug_Home_Menu_Plus_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bug,Home,Menu,Plus,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bug_Home_Menu_Plus_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bug,Home,Menu,Plus,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Bug_Home_Menu_Plus_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Bug,Home,Menu,Plus,Settings,Users,X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \n\n\n\n\n\n\n\nconst navigation = [\n    {\n        name: \"Dashboard\",\n        href: \"/dashboard\",\n        icon: _barrel_optimize_names_BarChart3_Bug_Home_Menu_Plus_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        name: \"Report Bug\",\n        href: \"/report\",\n        icon: _barrel_optimize_names_BarChart3_Bug_Home_Menu_Plus_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        name: \"My Reports\",\n        href: \"/my-reports\",\n        icon: _barrel_optimize_names_BarChart3_Bug_Home_Menu_Plus_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    }\n];\nconst adminNavigation = [\n    {\n        name: \"All Reports\",\n        href: \"/admin/reports\",\n        icon: _barrel_optimize_names_BarChart3_Bug_Home_Menu_Plus_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    },\n    {\n        name: \"User Management\",\n        href: \"/admin/users\",\n        icon: _barrel_optimize_names_BarChart3_Bug_Home_Menu_Plus_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n    },\n    {\n        name: \"Settings\",\n        href: \"/admin/settings\",\n        icon: _barrel_optimize_names_BarChart3_Bug_Home_Menu_Plus_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n    }\n];\nfunction Sidebar() {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const { user } = (0,_features_auth_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const isAdmin = user?.role === \"admin\" || user?.role === \"super_admin\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"lg:hidden fixed top-4 left-4 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                    variant: \"outline\",\n                    size: \"icon\",\n                    onClick: ()=>setIsOpen(!isOpen),\n                    className: \"bg-background\",\n                    children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bug_Home_Menu_Plus_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 21\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Bug_Home_Menu_Plus_Settings_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 49\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"fixed inset-y-0 left-0 z-40 w-64 bg-background border-r transform transition-transform duration-200 ease-in-out lg:translate-x-0 lg:static lg:inset-0\", isOpen ? \"translate-x-0\" : \"-translate-x-full\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-16 px-4 border-b\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold text-primary\",\n                                children: \"BugWatchGov\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"flex-1 px-4 py-6 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: navigation.map((item)=>{\n                                        const Icon = item.icon;\n                                        const isActive = pathname === item.href;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: item.href,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors\", isActive ? \"bg-primary text-primary-foreground\" : \"text-muted-foreground hover:text-foreground hover:bg-accent\"),\n                                            onClick: ()=>setIsOpen(false),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                    className: \"mr-3 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 21\n                                                }, this),\n                                                item.name\n                                            ]\n                                        }, item.name, true, {\n                                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"px-3 text-xs font-semibold text-muted-foreground uppercase tracking-wider\",\n                                            children: \"Administration\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 space-y-1\",\n                                            children: adminNavigation.map((item)=>{\n                                                const Icon = item.icon;\n                                                const isActive = pathname === item.href;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                    href: item.href,\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors\", isActive ? \"bg-primary text-primary-foreground\" : \"text-muted-foreground hover:text-foreground hover:bg-accent\"),\n                                                    onClick: ()=>setIsOpen(false),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"mr-3 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                                                            lineNumber: 98,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        item.name\n                                                    ]\n                                                }, item.name, true, {\n                                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium\",\n                                            children: user?.email\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground capitalize\",\n                                            children: user?.role\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 17\n                                        }, this),\n                                        user?.ministry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: user.ministry\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-30 bg-black bg-opacity-50 lg:hidden\",\n                onClick: ()=>setIsOpen(false)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/navigation/sidebar.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/navigation/sidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/badge.tsx":
/*!*********************************!*\
  !*** ./components/ui/badge.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Badge: () => (/* binding */ Badge),\n/* harmony export */   badgeVariants: () => (/* binding */ badgeVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst badgeVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_1__.cva)(\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\", {\n    variants: {\n        variant: {\n            default: \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n            secondary: \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            destructive: \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n            outline: \"text-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nfunction Badge({ className, variant, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(badgeVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/badge.tsx\",\n        lineNumber: 26,\n        columnNumber: 10\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/badge.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/button.tsx\",\n        lineNumber: 42,\n        columnNumber: 12\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2J1dHRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUNhO0FBQ3NCO0FBRWpDO0FBRWhDLE1BQU1JLGlCQUFpQkYsNkRBQUdBLENBQ3hCLDBSQUNBO0lBQ0VHLFVBQVU7UUFDUkMsU0FBUztZQUNQQyxTQUFTO1lBQ1RDLGFBQWE7WUFDYkMsU0FBUztZQUNUQyxXQUFXO1lBQ1hDLE9BQU87WUFDUEMsTUFBTTtRQUNSO1FBQ0FDLE1BQU07WUFDSk4sU0FBUztZQUNUTyxJQUFJO1lBQ0pDLElBQUk7WUFDSkMsTUFBTTtRQUNSO0lBQ0Y7SUFDQUMsaUJBQWlCO1FBQ2ZYLFNBQVM7UUFDVE8sTUFBTTtJQUNSO0FBQ0Y7QUFTRixNQUFNSyx1QkFBU2xCLDZDQUFnQixDQUM3QixDQUFDLEVBQUVvQixTQUFTLEVBQUVkLE9BQU8sRUFBRU8sSUFBSSxFQUFFUSxVQUFVLEtBQUssRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQ3hELE1BQU1DLE9BQU9ILFVBQVVwQixzREFBSUEsR0FBRztJQUM5QixxQkFBTyw4REFBQ3VCO1FBQUtKLFdBQVdqQiw4Q0FBRUEsQ0FBQ0MsZUFBZTtZQUFFRTtZQUFTTztZQUFNTztRQUFVO1FBQUtHLEtBQUtBO1FBQU0sR0FBR0QsS0FBSzs7Ozs7O0FBQy9GO0FBRUZKLE9BQU9PLFdBQVcsR0FBRztBQUVZIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVnd2F0Y2hnb3YtZnJvbnRlbmQvLi9jb21wb25lbnRzL3VpL2J1dHRvbi50c3g/ODk0NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgU2xvdCB9IGZyb20gXCJAcmFkaXgtdWkvcmVhY3Qtc2xvdFwiXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgYnV0dG9uVmFyaWFudHMgPSBjdmEoXG4gIFwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHdoaXRlc3BhY2Utbm93cmFwIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIHRyYW5zaXRpb24tY29sb3JzIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAge1xuICAgIHZhcmlhbnRzOiB7XG4gICAgICB2YXJpYW50OiB7XG4gICAgICAgIGRlZmF1bHQ6IFwiYmctcHJpbWFyeSB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZCBob3ZlcjpiZy1wcmltYXJ5LzkwXCIsXG4gICAgICAgIGRlc3RydWN0aXZlOiBcImJnLWRlc3RydWN0aXZlIHRleHQtZGVzdHJ1Y3RpdmUtZm9yZWdyb3VuZCBob3ZlcjpiZy1kZXN0cnVjdGl2ZS85MFwiLFxuICAgICAgICBvdXRsaW5lOiBcImJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiLFxuICAgICAgICBzZWNvbmRhcnk6IFwiYmctc2Vjb25kYXJ5IHRleHQtc2Vjb25kYXJ5LWZvcmVncm91bmQgaG92ZXI6Ymctc2Vjb25kYXJ5LzgwXCIsXG4gICAgICAgIGdob3N0OiBcImhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kXCIsXG4gICAgICAgIGxpbms6IFwidGV4dC1wcmltYXJ5IHVuZGVybGluZS1vZmZzZXQtNCBob3Zlcjp1bmRlcmxpbmVcIixcbiAgICAgIH0sXG4gICAgICBzaXplOiB7XG4gICAgICAgIGRlZmF1bHQ6IFwiaC0xMCBweC00IHB5LTJcIixcbiAgICAgICAgc206IFwiaC05IHJvdW5kZWQtbWQgcHgtM1wiLFxuICAgICAgICBsZzogXCJoLTExIHJvdW5kZWQtbWQgcHgtOFwiLFxuICAgICAgICBpY29uOiBcImgtMTAgdy0xMFwiLFxuICAgICAgfSxcbiAgICB9LFxuICAgIGRlZmF1bHRWYXJpYW50czoge1xuICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXG4gICAgICBzaXplOiBcImRlZmF1bHRcIixcbiAgICB9LFxuICB9LFxuKVxuXG5leHBvcnQgaW50ZXJmYWNlIEJ1dHRvblByb3BzXG4gIGV4dGVuZHMgUmVhY3QuQnV0dG9uSFRNTEF0dHJpYnV0ZXM8SFRNTEJ1dHRvbkVsZW1lbnQ+LFxuICAgIFZhcmlhbnRQcm9wczx0eXBlb2YgYnV0dG9uVmFyaWFudHM+IHtcbiAgYXNDaGlsZD86IGJvb2xlYW5cbn1cblxuY29uc3QgQnV0dG9uID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MQnV0dG9uRWxlbWVudCwgQnV0dG9uUHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIHZhcmlhbnQsIHNpemUsIGFzQ2hpbGQgPSBmYWxzZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgY29uc3QgQ29tcCA9IGFzQ2hpbGQgPyBTbG90IDogXCJidXR0b25cIlxuICAgIHJldHVybiA8Q29tcCBjbGFzc05hbWU9e2NuKGJ1dHRvblZhcmlhbnRzKHsgdmFyaWFudCwgc2l6ZSwgY2xhc3NOYW1lIH0pKX0gcmVmPXtyZWZ9IHsuLi5wcm9wc30gLz5cbiAgfSxcbilcbkJ1dHRvbi5kaXNwbGF5TmFtZSA9IFwiQnV0dG9uXCJcblxuZXhwb3J0IHsgQnV0dG9uLCBidXR0b25WYXJpYW50cyB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJTbG90IiwiY3ZhIiwiY24iLCJidXR0b25WYXJpYW50cyIsInZhcmlhbnRzIiwidmFyaWFudCIsImRlZmF1bHQiLCJkZXN0cnVjdGl2ZSIsIm91dGxpbmUiLCJzZWNvbmRhcnkiLCJnaG9zdCIsImxpbmsiLCJzaXplIiwic20iLCJsZyIsImljb24iLCJkZWZhdWx0VmFyaWFudHMiLCJCdXR0b24iLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwiYXNDaGlsZCIsInByb3BzIiwicmVmIiwiQ29tcCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/card.tsx\",\n        lineNumber: 6,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/card.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/card.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/card.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/card.tsx\",\n        lineNumber: 32,\n        columnNumber: 37\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/card.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2NhcmQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBRWhDLE1BQU1FLHFCQUFPRiw2Q0FBZ0IsQ0FBdUQsQ0FBQyxFQUFFSSxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDNUcsOERBQUNDO1FBQUlELEtBQUtBO1FBQUtGLFdBQVdILDhDQUFFQSxDQUFDLDREQUE0REc7UUFBYSxHQUFHQyxLQUFLOzs7Ozs7QUFFaEhILEtBQUtNLFdBQVcsR0FBRztBQUVuQixNQUFNQywyQkFBYVQsNkNBQWdCLENBQ2pDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQ3hCLDhEQUFDQztRQUFJRCxLQUFLQTtRQUFLRixXQUFXSCw4Q0FBRUEsQ0FBQyxpQ0FBaUNHO1FBQWEsR0FBR0MsS0FBSzs7Ozs7O0FBR3ZGSSxXQUFXRCxXQUFXLEdBQUc7QUFFekIsTUFBTUUsMEJBQVlWLDZDQUFnQixDQUNoQyxDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUN4Qiw4REFBQ0s7UUFBR0wsS0FBS0E7UUFBS0YsV0FBV0gsOENBQUVBLENBQUMsc0RBQXNERztRQUFhLEdBQUdDLEtBQUs7Ozs7OztBQUczR0ssVUFBVUYsV0FBVyxHQUFHO0FBRXhCLE1BQU1JLGdDQUFrQlosNkNBQWdCLENBQ3RDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQ3hCLDhEQUFDTztRQUFFUCxLQUFLQTtRQUFLRixXQUFXSCw4Q0FBRUEsQ0FBQyxpQ0FBaUNHO1FBQWEsR0FBR0MsS0FBSzs7Ozs7O0FBR3JGTyxnQkFBZ0JKLFdBQVcsR0FBRztBQUU5QixNQUFNTSw0QkFBY2QsNkNBQWdCLENBQ2xDLENBQUMsRUFBRUksU0FBUyxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQVEsOERBQUNDO1FBQUlELEtBQUtBO1FBQUtGLFdBQVdILDhDQUFFQSxDQUFDLFlBQVlHO1FBQWEsR0FBR0MsS0FBSzs7Ozs7O0FBRWxHUyxZQUFZTixXQUFXLEdBQUc7QUFFMUIsTUFBTU8sMkJBQWFmLDZDQUFnQixDQUNqQyxDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDLG9CQUN4Qiw4REFBQ0M7UUFBSUQsS0FBS0E7UUFBS0YsV0FBV0gsOENBQUVBLENBQUMsOEJBQThCRztRQUFhLEdBQUdDLEtBQUs7Ozs7OztBQUdwRlUsV0FBV1AsV0FBVyxHQUFHO0FBRXVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVnd2F0Y2hnb3YtZnJvbnRlbmQvLi9jb21wb25lbnRzL3VpL2NhcmQudHN4P2FkOTEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBDYXJkID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MRGl2RWxlbWVudCwgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+PigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8ZGl2IHJlZj17cmVmfSBjbGFzc05hbWU9e2NuKFwicm91bmRlZC1sZyBib3JkZXIgYmctY2FyZCB0ZXh0LWNhcmQtZm9yZWdyb3VuZCBzaGFkb3ctc21cIiwgY2xhc3NOYW1lKX0gey4uLnByb3BzfSAvPlxuKSlcbkNhcmQuZGlzcGxheU5hbWUgPSBcIkNhcmRcIlxuXG5jb25zdCBDYXJkSGVhZGVyID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MRGl2RWxlbWVudCwgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+PihcbiAgKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgICA8ZGl2IHJlZj17cmVmfSBjbGFzc05hbWU9e2NuKFwiZmxleCBmbGV4LWNvbCBzcGFjZS15LTEuNSBwLTZcIiwgY2xhc3NOYW1lKX0gey4uLnByb3BzfSAvPlxuICApLFxuKVxuQ2FyZEhlYWRlci5kaXNwbGF5TmFtZSA9IFwiQ2FyZEhlYWRlclwiXG5cbmNvbnN0IENhcmRUaXRsZSA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTFBhcmFncmFwaEVsZW1lbnQsIFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxIZWFkaW5nRWxlbWVudD4+KFxuICAoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICAgIDxoMyByZWY9e3JlZn0gY2xhc3NOYW1lPXtjbihcInRleHQtMnhsIGZvbnQtc2VtaWJvbGQgbGVhZGluZy1ub25lIHRyYWNraW5nLXRpZ2h0XCIsIGNsYXNzTmFtZSl9IHsuLi5wcm9wc30gLz5cbiAgKSxcbilcbkNhcmRUaXRsZS5kaXNwbGF5TmFtZSA9IFwiQ2FyZFRpdGxlXCJcblxuY29uc3QgQ2FyZERlc2NyaXB0aW9uID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MUGFyYWdyYXBoRWxlbWVudCwgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTFBhcmFncmFwaEVsZW1lbnQ+PihcbiAgKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgICA8cCByZWY9e3JlZn0gY2xhc3NOYW1lPXtjbihcInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIsIGNsYXNzTmFtZSl9IHsuLi5wcm9wc30gLz5cbiAgKSxcbilcbkNhcmREZXNjcmlwdGlvbi5kaXNwbGF5TmFtZSA9IFwiQ2FyZERlc2NyaXB0aW9uXCJcblxuY29uc3QgQ2FyZENvbnRlbnQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxEaXZFbGVtZW50LCBSZWFjdC5IVE1MQXR0cmlidXRlczxIVE1MRGl2RWxlbWVudD4+KFxuICAoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gPGRpdiByZWY9e3JlZn0gY2xhc3NOYW1lPXtjbihcInAtNiBwdC0wXCIsIGNsYXNzTmFtZSl9IHsuLi5wcm9wc30gLz4sXG4pXG5DYXJkQ29udGVudC5kaXNwbGF5TmFtZSA9IFwiQ2FyZENvbnRlbnRcIlxuXG5jb25zdCBDYXJkRm9vdGVyID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MRGl2RWxlbWVudCwgUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+PihcbiAgKHsgY2xhc3NOYW1lLCAuLi5wcm9wcyB9LCByZWYpID0+IChcbiAgICA8ZGl2IHJlZj17cmVmfSBjbGFzc05hbWU9e2NuKFwiZmxleCBpdGVtcy1jZW50ZXIgcC02IHB0LTBcIiwgY2xhc3NOYW1lKX0gey4uLnByb3BzfSAvPlxuICApLFxuKVxuQ2FyZEZvb3Rlci5kaXNwbGF5TmFtZSA9IFwiQ2FyZEZvb3RlclwiXG5cbmV4cG9ydCB7IENhcmQsIENhcmRIZWFkZXIsIENhcmRGb290ZXIsIENhcmRUaXRsZSwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkQ29udGVudCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIkNhcmQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJyZWYiLCJkaXYiLCJkaXNwbGF5TmFtZSIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJoMyIsIkNhcmREZXNjcmlwdGlvbiIsInAiLCJDYXJkQ29udGVudCIsIkNhcmRGb290ZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toast.tsx":
/*!*********************************!*\
  !*** ./components/ui/toast.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toast: () => (/* binding */ Toast),\n/* harmony export */   ToastAction: () => (/* binding */ ToastAction),\n/* harmony export */   ToastClose: () => (/* binding */ ToastClose),\n/* harmony export */   ToastDescription: () => (/* binding */ ToastDescription),\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   ToastTitle: () => (/* binding */ ToastTitle),\n/* harmony export */   ToastViewport: () => (/* binding */ ToastViewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-toast */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-toast@1.2.14_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_f2d63fe9a772cc94531d3b740a819e3d/node_modules/@radix-ui/react-toast/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/.pnpm/class-variance-authority@0.7.1/node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=X!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\n\nconst ToastProvider = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Provider;\nconst ToastViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/toast.tsx\",\n        lineNumber: 14,\n        columnNumber: 3\n    }, undefined));\nToastViewport.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Viewport.displayName;\nconst toastVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\", {\n    variants: {\n        variant: {\n            default: \"border bg-background text-foreground\",\n            destructive: \"destructive border-destructive bg-destructive text-destructive-foreground\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Toast = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(toastVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/toast.tsx\",\n        lineNumber: 44,\n        columnNumber: 10\n    }, undefined);\n});\nToast.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\nconst ToastAction = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/toast.tsx\",\n        lineNumber: 52,\n        columnNumber: 3\n    }, undefined));\nToastAction.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Action.displayName;\nconst ToastClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\", className),\n        \"toast-close\": \"\",\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/toast.tsx\",\n            lineNumber: 76,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/toast.tsx\",\n        lineNumber: 67,\n        columnNumber: 3\n    }, undefined));\nToastClose.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Close.displayName;\nconst ToastTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm font-semibold\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/toast.tsx\",\n        lineNumber: 85,\n        columnNumber: 3\n    }, undefined));\nToastTitle.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Title.displayName;\nconst ToastDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm opacity-90\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/toast.tsx\",\n        lineNumber: 93,\n        columnNumber: 3\n    }, undefined));\nToastDescription.displayName = _radix_ui_react_toast__WEBPACK_IMPORTED_MODULE_4__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toast.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/toast */ \"(ssr)/./components/ui/toast.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(ssr)/./components/ui/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nfunction Toaster() {\n    const { toasts } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastProvider, {\n        children: [\n            toasts.map(({ id, title, description, action, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.Toast, {\n                    ...props,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-1\",\n                            children: [\n                                title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastTitle, {\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/toaster.tsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 23\n                                }, this),\n                                description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastDescription, {\n                                    children: description\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/toaster.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 29\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/toaster.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        action,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastClose, {}, void 0, false, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/toaster.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, id, true, {\n                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/toaster.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this)),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_1__.ToastViewport, {}, void 0, false, {\n                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/toaster.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/toaster.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/toaster.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/use-toast.ts":
/*!************************************!*\
  !*** ./components/ui/use-toast.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ reducer,useToast,toast auto */ \nconst TOAST_LIMIT = 1;\nconst TOAST_REMOVE_DELAY = 1000000;\nconst actionTypes = {\n    ADD_TOAST: \"ADD_TOAST\",\n    UPDATE_TOAST: \"UPDATE_TOAST\",\n    DISMISS_TOAST: \"DISMISS_TOAST\",\n    REMOVE_TOAST: \"REMOVE_TOAST\"\n};\nlet count = 0;\nfunction genId() {\n    count = (count + 1) % Number.MAX_SAFE_INTEGER;\n    return count.toString();\n}\nconst toastTimeouts = new Map();\nconst addToRemoveQueue = (toastId)=>{\n    if (toastTimeouts.has(toastId)) {\n        return;\n    }\n    const timeout = setTimeout(()=>{\n        toastTimeouts.delete(toastId);\n        dispatch({\n            type: \"REMOVE_TOAST\",\n            toastId: toastId\n        });\n    }, TOAST_REMOVE_DELAY);\n    toastTimeouts.set(toastId, timeout);\n};\nconst reducer = (state, action)=>{\n    switch(action.type){\n        case \"ADD_TOAST\":\n            return {\n                ...state,\n                toasts: [\n                    action.toast,\n                    ...state.toasts\n                ].slice(0, TOAST_LIMIT)\n            };\n        case \"UPDATE_TOAST\":\n            return {\n                ...state,\n                toasts: state.toasts.map((t)=>t.id === action.toast.id ? {\n                        ...t,\n                        ...action.toast\n                    } : t)\n            };\n        case \"DISMISS_TOAST\":\n            {\n                const { toastId } = action;\n                // ! Side effects ! - This could be extracted into a dismissToast() action,\n                // but I'll keep it here for simplicity\n                if (toastId) {\n                    addToRemoveQueue(toastId);\n                } else {\n                    state.toasts.forEach((toast)=>{\n                        addToRemoveQueue(toast.id);\n                    });\n                }\n                return {\n                    ...state,\n                    toasts: state.toasts.map((t)=>t.id === toastId || toastId === undefined ? {\n                            ...t,\n                            open: false\n                        } : t)\n                };\n            }\n        case \"REMOVE_TOAST\":\n            if (action.toastId === undefined) {\n                return {\n                    ...state,\n                    toasts: []\n                };\n            }\n            return {\n                ...state,\n                toasts: state.toasts.filter((t)=>t.id !== action.toastId)\n            };\n    }\n};\nconst listeners = [];\nlet memoryState = {\n    toasts: []\n};\nfunction dispatch(action) {\n    memoryState = reducer(memoryState, action);\n    listeners.forEach((listener)=>{\n        listener(memoryState);\n    });\n}\nfunction toast({ ...props }) {\n    const id = genId();\n    const update = (props)=>dispatch({\n            type: \"UPDATE_TOAST\",\n            toast: {\n                ...props,\n                id\n            }\n        });\n    const dismiss = ()=>dispatch({\n            type: \"DISMISS_TOAST\",\n            toastId: id\n        });\n    dispatch({\n        type: \"ADD_TOAST\",\n        toast: {\n            ...props,\n            id,\n            open: true,\n            onOpenChange: (open)=>{\n                if (!open) dismiss();\n            }\n        }\n    });\n    return {\n        id: id,\n        dismiss,\n        update\n    };\n}\nfunction useToast() {\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState(memoryState);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        listeners.push(setState);\n        return ()=>{\n            const index = listeners.indexOf(setState);\n            if (index > -1) {\n                listeners.splice(index, 1);\n            }\n        };\n    }, [\n        state\n    ]);\n    return {\n        ...state,\n        toast,\n        dismiss: (toastId)=>dispatch({\n                type: \"DISMISS_TOAST\",\n                toastId\n            })\n    };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/use-toast.ts\n");

/***/ }),

/***/ "(ssr)/./features/auth/auth-context.tsx":
/*!****************************************!*\
  !*** ./features/auth/auth-context.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase/client */ \"(ssr)/./lib/supabase/client.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api-client */ \"(ssr)/./lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_lib_supabase_client__WEBPACK_IMPORTED_MODULE_2__.createClient)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getSession = async ()=>{\n            try {\n                const { data: { session }, error } = await supabase.auth.getSession();\n                if (error) {\n                    console.error(\"Error getting session:\", error);\n                    setUser(null);\n                } else if (session?.user) {\n                    await loadUserProfile(session.user);\n                } else {\n                    setUser(null);\n                }\n            } catch (error) {\n                console.error(\"Error in getSession:\", error);\n                setUser(null);\n            } finally{\n                setLoading(false);\n            }\n        };\n        getSession();\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            try {\n                if (event === \"SIGNED_OUT\" || !session?.user) {\n                    setUser(null);\n                } else if (session?.user) {\n                    await loadUserProfile(session.user);\n                }\n            } catch (error) {\n                console.error(\"Error in auth state change:\", error);\n                setUser(null);\n            } finally{\n                setLoading(false);\n            }\n        });\n        return ()=>subscription.unsubscribe();\n    }, [\n        supabase.auth\n    ]);\n    const loadUserProfile = async (authUser)=>{\n        try {\n            // Get user role from the backend\n            const roleData = await _lib_api_client__WEBPACK_IMPORTED_MODULE_3__.ApiClient.getCurrentUserRole();\n            const userProfile = {\n                id: authUser.id,\n                email: authUser.email,\n                name: authUser.user_metadata?.name || authUser.user_metadata?.full_name,\n                role: roleData.role,\n                ministry: roleData.ministry\n            };\n            setUser(userProfile);\n        } catch (error) {\n            console.error(\"Error loading user profile:\", error);\n            // Fallback to basic user info\n            setUser({\n                id: authUser.id,\n                email: authUser.email,\n                name: authUser.user_metadata?.name || authUser.user_metadata?.full_name,\n                role: \"user\"\n            });\n        }\n    };\n    const refreshUser = async ()=>{\n        try {\n            const { data: { user: authUser }, error } = await supabase.auth.getUser();\n            if (error) {\n                throw error;\n            }\n            if (authUser) {\n                await loadUserProfile(authUser);\n            } else {\n                setUser(null);\n            }\n        } catch (error) {\n            console.error(\"Error refreshing user:\", error);\n            setUser(null);\n        }\n    };\n    const signIn = async (email, password)=>{\n        const { error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) throw error;\n    };\n    const signUp = async (email, password)=>{\n        const { error } = await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                emailRedirectTo: `${window.location.origin}/auth/callback`\n            }\n        });\n        if (error) throw error;\n    };\n    const signOut = async ()=>{\n        const { error } = await supabase.auth.signOut();\n        if (error) throw error;\n        setUser(null);\n    };\n    const value = {\n        user,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/auth/auth-context.tsx\",\n        lineNumber: 154,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./features/auth/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./features/bug/bug-card.tsx":
/*!***********************************!*\
  !*** ./features/bug/bug-card.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BugCard: () => (/* binding */ BugCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/badge */ \"(ssr)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,MapPin,Users!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,MapPin,Users!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,MapPin,Users!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Building_Calendar_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Building,Calendar,MapPin,Users!=!lucide-react */ \"(ssr)/./node_modules/.pnpm/lucide-react@0.303.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* __next_internal_client_entry_do_not_use__ BugCard auto */ \n\n\n\n\nfunction BugCard({ bug, onClick, showMinistry = false }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"cursor-pointer hover:shadow-md transition-shadow\",\n        onClick: onClick,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                className: \"pb-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"text-lg line-clamp-2\",\n                                    children: bug.title\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    className: \"mt-1 line-clamp-2\",\n                                    children: bug.description\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col gap-2 ml-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.getStatusColor)(bug.status),\n                                    children: bug.status.replace(\"_\", \" \")\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.getPriorityColor)(bug.priority),\n                                    children: bug.priority\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"pt-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 text-sm text-muted-foreground\",\n                        children: [\n                            showMinistry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: bug.ministry\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: bug.location\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                        lineNumber: 40,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: bug.unit\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Building_Calendar_MapPin_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                        lineNumber: 47,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(bug.created_at)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                        lineNumber: 31,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 pt-3 border-t\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"System:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: bug.affected_system\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between text-sm mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Type:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium capitalize\",\n                                        children: bug.bug_type.replace(\"_\", \" \")\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                        lineNumber: 58,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/features/bug/bug-card.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./features/bug/bug-card.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api-client.ts":
/*!***************************!*\
  !*** ./lib/api-client.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\n// Helper function for API requests\nasync function fetchApi(endpoint, options) {\n    const response = await fetch(`/api/${endpoint}`, {\n        ...options,\n        headers: {\n            \"Content-Type\": \"application/json\",\n            ...options?.headers\n        }\n    });\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`);\n    }\n    const data = await response.json();\n    if (!data.success) {\n        throw new Error(data.error?.message || \"API request failed\");\n    }\n    return data.data;\n}\n// Bug Reports API functions\nasync function createBugReport(data) {\n    return fetchApi(\"bug-reports\", {\n        method: \"POST\",\n        body: JSON.stringify(data)\n    });\n}\nasync function getUserReports(filters) {\n    const queryParams = new URLSearchParams();\n    if (filters?.status) queryParams.append(\"status\", filters.status);\n    if (filters?.bug_type) queryParams.append(\"bug_type\", filters.bug_type);\n    if (filters?.priority) queryParams.append(\"priority\", filters.priority);\n    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : \"\";\n    return fetchApi(`bug-reports${queryString}`);\n}\nasync function getBugReport(id) {\n    return fetchApi(`bug-reports/${id}`);\n}\nasync function updateBugReport(id, data) {\n    return fetchApi(`bug-reports/${id}`, {\n        method: \"PATCH\",\n        body: JSON.stringify(data)\n    });\n}\nasync function deleteBugReport(id) {\n    return fetchApi(`bug-reports/${id}`, {\n        method: \"DELETE\"\n    });\n}\nasync function getReportsByMinistry(ministry, filters) {\n    const queryParams = new URLSearchParams();\n    if (filters?.status) queryParams.append(\"status\", filters.status);\n    if (filters?.bug_type) queryParams.append(\"bug_type\", filters.bug_type);\n    if (filters?.priority) queryParams.append(\"priority\", filters.priority);\n    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : \"\";\n    return fetchApi(`bug-reports/ministry/${encodeURIComponent(ministry)}${queryString}`);\n}\nasync function getAllReports(filters) {\n    const queryParams = new URLSearchParams();\n    if (filters?.status) queryParams.append(\"status\", filters.status);\n    if (filters?.bug_type) queryParams.append(\"bug_type\", filters.bug_type);\n    if (filters?.ministry) queryParams.append(\"ministry\", filters.ministry);\n    if (filters?.priority) queryParams.append(\"priority\", filters.priority);\n    const queryString = queryParams.toString() ? `?${queryParams.toString()}` : \"\";\n    return fetchApi(`bug-reports/all${queryString}`);\n}\n// User Roles API functions\nasync function getCurrentUserRole() {\n    return fetchApi(\"user-roles\");\n}\nasync function assignUserRole(userId, role, ministry) {\n    return fetchApi(\"user-roles\", {\n        method: \"POST\",\n        body: JSON.stringify({\n            userId,\n            role,\n            ministry\n        })\n    });\n}\n// Export as a plain object with all the functions\n// This ensures it can't be used as a constructor\nconst apiClient = {\n    createBugReport,\n    getUserReports,\n    getBugReport,\n    updateBugReport,\n    deleteBugReport,\n    getReportsByMinistry,\n    getAllReports,\n    getCurrentUserRole,\n    assignUserRole\n};\n// Export the object directly\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api-client.ts\n");

/***/ }),

/***/ "(ssr)/./lib/supabase/client.ts":
/*!********************************!*\
  !*** ./lib/supabase/client.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(ssr)/./node_modules/.pnpm/@supabase+ssr@0.1.0_@supabase+supabase-js@2.49.8/node_modules/@supabase/ssr/dist/index.mjs\");\n\nfunction createClient() {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(\"https://cakjxkbarwacvzxkgjsj.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNha2p4a2JhcndhY3Z6eGtnanNqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3ODA5MTYsImV4cCI6MjA2NDM1NjkxNn0.GSfbuz9y1X1OlKtcaocsF5WggA7eoD-W9GcXz1dtUPk\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvc3VwYWJhc2UvY2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW1EO0FBRzVDLFNBQVNDO0lBQ2QsT0FBT0Qsa0VBQW1CQSxDQUN4QkUsMENBQW9DLEVBQ3BDQSxrTkFBeUM7QUFFN0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWd3YXRjaGdvdi1mcm9udGVuZC8uL2xpYi9zdXBhYmFzZS9jbGllbnQudHM/NWFiZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVCcm93c2VyQ2xpZW50IH0gZnJvbSBcIkBzdXBhYmFzZS9zc3JcIlxuaW1wb3J0IHR5cGUgeyBEYXRhYmFzZSB9IGZyb20gXCIuL3R5cGVzXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNyZWF0ZUNsaWVudCgpIHtcbiAgcmV0dXJuIGNyZWF0ZUJyb3dzZXJDbGllbnQ8RGF0YWJhc2U+KFxuICAgIHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCEsXG4gICAgcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkhLFxuICApXG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQnJvd3NlckNsaWVudCIsImNyZWF0ZUNsaWVudCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/supabase/client.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   getPriorityColor: () => (/* binding */ getPriorityColor),\n/* harmony export */   getStatusColor: () => (/* binding */ getStatusColor)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/.pnpm/tailwind-merge@2.6.0/node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    }).format(new Date(date));\n}\nfunction getStatusColor(status) {\n    const statusMap = {\n        open: \"bg-red-100 text-red-800\",\n        in_progress: \"bg-yellow-100 text-yellow-800\",\n        resolved: \"bg-green-100 text-green-800\",\n        closed: \"bg-gray-100 text-gray-800\",\n        rejected: \"bg-red-100 text-red-800\"\n    };\n    return statusMap[status] || \"bg-gray-100 text-gray-800\";\n}\nfunction getPriorityColor(priority) {\n    const priorityMap = {\n        low: \"bg-blue-100 text-blue-800\",\n        medium: \"bg-yellow-100 text-yellow-800\",\n        high: \"bg-orange-100 text-orange-800\",\n        critical: \"bg-red-100 text-red-800\"\n    };\n    return priorityMap[priority] || \"bg-gray-100 text-gray-800\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6940c77c9057\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWd3YXRjaGdvdi1mcm9udGVuZC8uL3N0eWxlcy9nbG9iYWxzLmNzcz8yOGQ5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNjk0MGM3N2M5MDU3XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Downloads/bugwatchgov-frontend/app/dashboard/page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _features_auth_auth_context__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/features/auth/auth-context */ \"(rsc)/./features/auth/auth-context.tsx\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/toaster */ \"(rsc)/./components/ui/toaster.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/globals.css */ \"(rsc)/./styles/globals.css\");\n\n\n\n\n\nconst metadata = {\n    title: \"BugWatchGov - Government Bug Reporting System\",\n    description: \"A secure platform for government workers to report software bugs and infrastructure issues across ministries in Gambia.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_features_auth_auth_context__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/Downloads/bugwatchgov-frontend/app/layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBT01BO0FBSnFEO0FBQ1Y7QUFDcEI7QUFJdEIsTUFBTUcsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFDRTtBQUNKLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1gsMkpBQWU7c0JBQzlCLDRFQUFDQyxxRUFBWUE7O29CQUNWTTtrQ0FDRCw4REFBQ0wsMkRBQU9BOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLbEIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idWd3YXRjaGdvdi1mcm9udGVuZC8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCJcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gXCJAL2ZlYXR1cmVzL2F1dGgvYXV0aC1jb250ZXh0XCJcbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3RvYXN0ZXJcIlxuaW1wb3J0IFwiQC9zdHlsZXMvZ2xvYmFscy5jc3NcIlxuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogW1wibGF0aW5cIl0gfSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiQnVnV2F0Y2hHb3YgLSBHb3Zlcm5tZW50IEJ1ZyBSZXBvcnRpbmcgU3lzdGVtXCIsXG4gIGRlc2NyaXB0aW9uOlxuICAgIFwiQSBzZWN1cmUgcGxhdGZvcm0gZm9yIGdvdmVybm1lbnQgd29ya2VycyB0byByZXBvcnQgc29mdHdhcmUgYnVncyBhbmQgaW5mcmFzdHJ1Y3R1cmUgaXNzdWVzIGFjcm9zcyBtaW5pc3RyaWVzIGluIEdhbWJpYS5cIixcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxBdXRoUHJvdmlkZXI+XG4gICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDxUb2FzdGVyIC8+XG4gICAgICAgIDwvQXV0aFByb3ZpZGVyPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwiQXV0aFByb3ZpZGVyIiwiVG9hc3RlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ui/toaster.tsx":
/*!***********************************!*\
  !*** ./components/ui/toaster.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/toaster.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Downloads/bugwatchgov-frontend/components/ui/toaster.tsx#Toaster`);


/***/ }),

/***/ "(rsc)/./features/auth/auth-context.tsx":
/*!****************************************!*\
  !*** ./features/auth/auth-context.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Downloads/bugwatchgov-frontend/features/auth/auth-context.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Downloads/bugwatchgov-frontend/features/auth/auth-context.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Downloads/bugwatchgov-frontend/features/auth/auth-context.tsx#useAuth`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/tr46@0.0.3","vendor-chunks/tailwind-merge@2.6.0","vendor-chunks/@supabase+auth-js@2.69.1","vendor-chunks/@supabase+realtime-js@2.11.2","vendor-chunks/@supabase+node-fetch@2.6.15","vendor-chunks/@supabase+postgrest-js@1.19.4","vendor-chunks/whatwg-url@5.0.0","vendor-chunks/@supabase+storage-js@2.7.1","vendor-chunks/@radix-ui+react-toast@1.2.14_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_f2d63fe9a772cc94531d3b740a819e3d","vendor-chunks/@radix-ui+react-collection@1.1.7_@types+react-dom@18.3.7_@types+react@18.3.23__@types+r_8ed47621286c24058c66cffcdce48db5","vendor-chunks/lucide-react@0.303.0_react@18.3.1","vendor-chunks/@supabase+supabase-js@2.49.8","vendor-chunks/@supabase+ssr@0.1.0_@supabase+supabase-js@2.49.8","vendor-chunks/@radix-ui+react-dismissable-layer@1.1.10_@types+react-dom@18.3.7_@types+react@18.3.23___dbf8386523191e50867cd199de52aa0e","vendor-chunks/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1","vendor-chunks/ramda@0.29.1","vendor-chunks/@supabase+functions-js@2.4.4","vendor-chunks/class-variance-authority@0.7.1","vendor-chunks/cookie@0.5.0","vendor-chunks/@radix-ui+react-presence@1.1.4_@types+react-dom@18.3.7_@types+react@18.3.23__@types+rea_587c7e8c3eecba09139e2afe2a783727","vendor-chunks/@radix-ui+react-use-controllable-state@1.2.2_@types+react@18.3.23_react@18.3.1","vendor-chunks/webidl-conversions@3.0.1","vendor-chunks/@radix-ui+react-context@1.1.2_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-compose-refs@1.1.2_@types+react@18.3.23_react@18.3.1","vendor-chunks/@swc+helpers@0.5.2","vendor-chunks/@radix-ui+react-primitive@2.1.3_@types+react-dom@18.3.7_@types+react@18.3.23__@types+re_db0ee435667e42f4b05fd5a9bb21abc3","vendor-chunks/clsx@2.1.1","vendor-chunks/@radix-ui+react-visually-hidden@1.2.3_@types+react-dom@18.3.7_@types+react@18.3.23__@ty_bd769e2c7ddceeff6e63be21c84dfac7","vendor-chunks/@radix-ui+react-portal@1.1.9_@types+react-dom@18.3.7_@types+react@18.3.23__@types+react_6c1cd0a6f7cc4779efee75f9fbbe7053","vendor-chunks/@radix-ui+react-use-effect-event@0.0.2_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-use-escape-keydown@1.1.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+primitive@1.1.2","vendor-chunks/@radix-ui+react-use-callback-ref@1.1.1_@types+react@18.3.23_react@18.3.1","vendor-chunks/@radix-ui+react-use-layout-effect@1.1.1_@types+react@18.3.23_react@18.3.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.0.4_@babel+core@7.27.4_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fmsj%2FDownloads%2Fbugwatchgov-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();