# BugWatchGov

A comprehensive bug tracking and reporting system for government-wide bug reporting across multiple ministries in Gambia.

## Project Structure

This project follows industry best practices with separate frontend and backend directories:

```
bugwatch-gov-backend/
├── frontend/          # Next.js frontend application (React/TypeScript)
├── backend/           # Next.js backend API (API routes)
├── database/          # Database schema and migrations
├── .env.local         # Shared environment configuration
├── package.json       # Workspace configuration
└── README.md          # This file
```

## Quick Start

### Prerequisites
- Node.js 18+
- pnpm 8+

### Installation & Development

1. **Install all dependencies:**
   ```bash
   pnpm run install:all
   ```

2. **Start both frontend and backend in development mode:**
   ```bash
   pnpm run dev
   ```
   - Backend will run on: http://localhost:3000
   - Frontend will run on: http://localhost:3001

3. **Or start them individually:**
   ```bash
   # Start backend only
   pnpm run dev:backend

   # Start frontend only
   pnpm run dev:frontend
   ```

### Production Build & Start

```bash
# Build both applications
pnpm run build

# Start both in production mode
pnpm run start
```

## Features

- 🔐 **Row-Level Security (RLS)** - Users can only access their own reports unless they're admins
- 👥 **Role-based Access Control** - User, Admin, and Super Admin roles
- 🏛️ **Multi-Ministry Support** - Structured reporting by ministry, location, and unit
- ✅ **Data Validation** - Comprehensive Zod schemas for input validation
- 📊 **Comprehensive Reporting** - Bug tracking with priority levels and status management
- 🔍 **Advanced Filtering** - Filter reports by status, type, ministry, and priority

## Detailed Setup Instructions

### 1. Database Setup

1. Create a new Supabase project
2. Run the SQL schema from `database/schema.sql` in your Supabase SQL editor
3. Enable Row Level Security (RLS) policies will be automatically created

### 2. Environment Configuration

1. Copy `.env.local.example` to `.env.local` (in the root directory)
2. Fill in your Supabase credentials:
   \`\`\`env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   NEXT_PUBLIC_APP_NAME=BugWatchGov
   NEXT_PUBLIC_APP_VERSION=1.0.0
   \`\`\`

### 3. Install Dependencies

The workspace is configured to manage dependencies for both frontend and backend:

\`\`\`bash
# Install root workspace dependencies
pnpm install

# Install all project dependencies (frontend + backend)
pnpm run install:all
\`\`\`

## Usage Examples

### Creating a Bug Report

\`\`\`typescript
import { BugReportService } from '@/lib/services/bug-reports'

const result = await BugReportService.reportBug({
  title: "Login system not working",
  description: "Users cannot log into the payroll system",
  ministry: "Ministry of Finance",
  location: "Banjul HQ",
  unit: "Payroll Department",
  bug_type: "authentication",
  affected_system: "Payroll Management System",
  priority: "high"
})

if (result.error) {
  console.error(result.error)
} else {
  console.log('Bug report created:', result.data)
}
\`\`\`

### Getting User Reports

\`\`\`typescript
const { data: reports, error } = await BugReportService.getUserReports()
\`\`\`

### Admin Functions

\`\`\`typescript
// Get reports by ministry (admin only)
const { data: reports, error } = await BugReportService.getReportsByMinistry("Ministry of Health")

// Get all reports with filters (admin only)
const { data: reports, error } = await BugReportService.getAllReports({
  status: "open",
  priority: "critical"
})
\`\`\`

## Security Features

- **Authentication Required** - All operations require valid Supabase authentication
- **Row-Level Security** - Database-level security ensures users can only access appropriate data
- **Role-based Permissions** - Different access levels for users, admins, and super admins
- **Input Validation** - All inputs are validated using Zod schemas
- **SQL Injection Protection** - Parameterized queries through Supabase client

## Database Schema

### bug_reports Table
- Comprehensive bug tracking with ministry/location/unit structure
- Status tracking from open to resolved
- Priority levels and bug type categorization
- Audit trail with created/updated timestamps

### user_roles Table
- Role management for access control
- Ministry assignment for admins
- Hierarchical permission structure

## API Reference

### BugReportService
- `reportBug(data)` - Create new bug report
- `getUserReports()` - Get current user's reports
- `getReportsByMinistry(ministry)` - Get reports by ministry (admin)
- `getAllReports(filters?)` - Get all reports with optional filters (admin)
- `updateBugReport(id, data)` - Update existing report
- `getBugReport(id)` - Get single report by ID
- `deleteBugReport(id)` - Delete report (super admin only)

### UserRoleService
- `getCurrentUserRole()` - Get current user's role
- `isAdmin()` - Check if user is admin
- `isSuperAdmin()` - Check if user is super admin
- `assignRole(userId, role, ministry?)` - Assign role to user (super admin only)

## Production Considerations

1. **Environment Variables** - Ensure all environment variables are properly set in production
2. **Database Backups** - Set up regular backups for your Supabase database
3. **Monitoring** - Implement logging and monitoring for the application
4. **Rate Limiting** - Consider implementing rate limiting for API endpoints
5. **Data Retention** - Establish policies for data retention and archival
