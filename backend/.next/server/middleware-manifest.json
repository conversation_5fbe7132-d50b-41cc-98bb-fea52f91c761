{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "myN0e_iD12BVyLcoo7KGz", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "thDP4aAzdEPaWeoOKc9B1LTTWbFQtNDswci/W7JFHmM=", "__NEXT_PREVIEW_MODE_ID": "2122f18cfac3221dee6be14b620b4f11", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ad41d70df8e7e1f66530cce7db5d2bbfe6cb3abac04b6088663c8d295efa0877", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ffa339a8445db1a65618f7cd6e0e21e58255110207e21160aae783b5d40562bf"}}}, "functions": {}, "sortedMiddleware": ["/"]}