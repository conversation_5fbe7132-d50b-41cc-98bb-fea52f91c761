"use strict";(()=>{var e={};e.id=683,e.ids=[683],e.modules={2934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8893:e=>{e.exports=require("buffer")},4770:e=>{e.exports=require("crypto")},7702:e=>{e.exports=require("events")},2615:e=>{e.exports=require("http")},5240:e=>{e.exports=require("https")},8216:e=>{e.exports=require("net")},8621:e=>{e.exports=require("punycode")},6162:e=>{e.exports=require("stream")},2452:e=>{e.exports=require("tls")},7360:e=>{e.exports=require("url")},1568:e=>{e.exports=require("zlib")},5166:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>f,patchFetch:()=>R,requestAsyncStorage:()=>g,routeModule:()=>h,serverHooks:()=>m,staticGenerationAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>p});var n=t(101),o=t(796),a=t(3055),i=t(8358),u=t(2383),c=t(9051),l=t(9310);let d=(0,i.G)({methods:["GET"],requireAuth:!0,handler:async()=>{let e=(0,c.f)(),r=new u.J(e);return await r.getCurrentUserRole()||{role:"user"}}}),p=(0,i.G)({methods:["POST"],requireAuth:!0,requireSuperAdmin:!0,schema:l.z.object({userId:l.z.string().uuid(),role:l.z.enum(["user","admin","super_admin"]),ministry:l.z.string().optional()}),handler:async(e,r,t)=>{let s=(0,c.f)(),n=new u.J(s);return await n.assignRole(t.userId,t.role,t.ministry)}}),h=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/user-roles/route",pathname:"/api/user-roles",filename:"route",bundlePath:"app/api/user-roles/route"},resolvedPagePath:"/home/<USER>/Downloads/bugwatch-gov-backend/backend/app/api/user-roles/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:g,staticGenerationAsyncStorage:w,serverHooks:m}=h,f="/api/user-roles/route";function R(){return(0,a.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:w})}},2383:(e,r,t)=>{t.d(r,{J:()=>n});var s=t(3526);class n{constructor(e){this.supabase=e}async getUserRole(e){try{let{data:r,error:t}=await this.supabase.from("user_roles").select("*").eq("user_id",e).single();if(t){if("PGRST116"===t.code)return null;throw(0,s.Iu)(t)}return r}catch(e){if(e instanceof s.gz)throw e;throw new s.gz("Failed to fetch user role",500)}}async getCurrentUserRole(){try{let{data:{user:e},error:r}=await this.supabase.auth.getUser();if(r||!e)throw new s.gz("Authentication required",401);return this.getUserRole(e.id)}catch(e){if(e instanceof s.gz)throw e;throw new s.gz("Failed to fetch current user role",500)}}async isAdmin(){try{let e=await this.getCurrentUserRole();return!!e&&["admin","super_admin"].includes(e.role)}catch(e){return!1}}async isSuperAdmin(){try{let e=await this.getCurrentUserRole();return!!e&&"super_admin"===e.role}catch(e){return!1}}async assignRole(e,r,t){try{let{data:n,error:o}=await this.supabase.from("user_roles").upsert({user_id:e,role:r,ministry:t}).select().single();if(o)throw(0,s.Iu)(o);if(!n)throw new s.gz("Failed to assign role",500);return n}catch(e){if(e instanceof s.gz)throw e;throw new s.gz("Failed to assign role",500)}}async ensureUserRole(e){try{let r=await this.getUserRole(e);if(r)return r;let{data:t,error:n}=await this.supabase.from("user_roles").insert({user_id:e,role:"user"}).select().single();if(n)throw(0,s.Iu)(n);if(!t)throw new s.gz("Failed to create user role",500);return t}catch(e){if(e instanceof s.gz)throw e;throw new s.gz("Failed to ensure user role",500)}}}},9051:(e,r,t)=>{t.d(r,{f:()=>o});var s=t(4e3),n=t(6348);function o(){let e=(0,n.cookies)();return(0,s.createServerClient)("https://cakjxkbarwacvzxkgjsj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNha2p4a2JhcndhY3Z6eGtnanNqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3ODA5MTYsImV4cCI6MjA2NDM1NjkxNn0.GSfbuz9y1X1OlKtcaocsF5WggA7eoD-W9GcXz1dtUPk",{cookies:{get:r=>e.get(r)?.value,set(r,t,s){e.set({name:r,value:t,...s})},remove(r,t){e.set({name:r,value:"",...t})}}})}},8358:(e,r,t)=>{t.d(r,{G:()=>u});var s=t(2903),n=t(3526);function o(e){return(0,n.HC)(e)?{success:!1,error:{message:e.message,code:e.code}}:e instanceof Error?{success:!1,error:{message:e.message,code:"UNKNOWN_ERROR"}}:{success:!1,error:{message:"An unknown error occurred",code:"UNKNOWN_ERROR",details:e}}}var a=t(9051),i=t(2383);function u({methods:e,schema:r,requireAuth:t=!0,requireAdmin:u=!1,requireSuperAdmin:c=!1,handler:l}){return async(d,{params:p})=>{try{let h,g;if(!e.includes(d.method))return s.NextResponse.json(o(new n.gz(`Method ${d.method} Not Allowed`,405,"METHOD_NOT_ALLOWED")),{status:405});if(["POST","PUT","PATCH"].includes(d.method)&&d.body)try{h=await d.json()}catch(e){return s.NextResponse.json(o(new n.gz("Invalid JSON body",400,"INVALID_JSON")),{status:400})}if(r&&h)try{h=r.parse(h)}catch(e){return s.NextResponse.json(o(new n.gz("Validation error",400,"VALIDATION_ERROR",e)),{status:400})}if(t||u||c){let e=(0,a.f)(),{data:{session:r}}=await e.auth.getSession();if(!r)return s.NextResponse.json(o(new n.gz("Authentication required",401,"UNAUTHORIZED")),{status:401});if(g=r.user.id,u||c){let r=new i.J(e);if(c){if(!await r.isSuperAdmin())return s.NextResponse.json(o(new n.gz("Super admin access required",403,"FORBIDDEN")),{status:403})}else if(u&&!await r.isAdmin())return s.NextResponse.json(o(new n.gz("Admin access required",403,"FORBIDDEN")),{status:403})}}let w=await l(d,p,h,g);return s.NextResponse.json({success:!0,data:w})}catch(r){console.error("API error:",r);let e=r instanceof n.gz?r.statusCode:500;return s.NextResponse.json(o(r),{status:e})}}}},3526:(e,r,t)=>{t.d(r,{HC:()=>o,Iu:()=>n,gz:()=>s});class s extends Error{constructor(e,r=500,t){super(e),this.statusCode=r,this.code=t,this.name="AppError"}}function n(e){return"PGRST301"===e.code?new s("Access denied",403,"ACCESS_DENIED"):"PGRST116"===e.code?new s("Resource not found",404,"NOT_FOUND"):"23505"===e.code?new s("Resource already exists",409,"DUPLICATE"):"23503"===e.code?new s("Invalid reference",400,"INVALID_REFERENCE"):new s(e.message||"Database error",500,"DATABASE_ERROR")}function o(e){return e instanceof s}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[761],()=>t(5166));module.exports=s})();