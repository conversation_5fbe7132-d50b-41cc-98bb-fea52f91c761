"use strict";(()=>{var e={};e.id=459,e.ids=[459],e.modules={2934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},8893:e=>{e.exports=require("buffer")},4770:e=>{e.exports=require("crypto")},7702:e=>{e.exports=require("events")},2615:e=>{e.exports=require("http")},5240:e=>{e.exports=require("https")},8216:e=>{e.exports=require("net")},8621:e=>{e.exports=require("punycode")},6162:e=>{e.exports=require("stream")},2452:e=>{e.exports=require("tls")},7360:e=>{e.exports=require("url")},1568:e=>{e.exports=require("zlib")},9094:(e,t,r)=>{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>b,requestAsyncStorage:()=>m,routeModule:()=>g,serverHooks:()=>f,staticGenerationAsyncStorage:()=>w});var s={};r.r(s),r.d(s,{DELETE:()=>h,GET:()=>l,PATCH:()=>p});var a=r(101),i=r(796),o=r(3055),n=r(8358),u=r(5668),c=r(290),d=r(9051);let l=(0,n.G)({methods:["GET"],requireAuth:!0,handler:async(e,{params:t})=>{let{id:r}=t,s=(0,d.f)(),a=new c.C(s);return await a.getBugReport(r)}}),p=(0,n.G)({methods:["PATCH"],requireAuth:!0,schema:u.A5,handler:async(e,{params:t},r)=>{let{id:s}=t,a=(0,d.f)(),i=new c.C(a);return await i.updateBugReport(s,r)}}),h=(0,n.G)({methods:["DELETE"],requireAuth:!0,requireSuperAdmin:!0,handler:async(e,{params:t})=>{let{id:r}=t,s=(0,d.f)(),a=new c.C(s);return await a.deleteBugReport(r),{success:!0,message:"Bug report deleted successfully"}}}),g=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/bug-reports/[id]/route",pathname:"/api/bug-reports/[id]",filename:"route",bundlePath:"app/api/bug-reports/[id]/route"},resolvedPagePath:"/home/<USER>/Downloads/bugwatch-gov-backend/backend/app/api/bug-reports/[id]/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:w,serverHooks:f}=g,y="/api/bug-reports/[id]/route";function b(){return(0,o.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:w})}},290:(e,t,r)=>{r.d(t,{C:()=>i});var s=r(5668),a=r(3526);class i{constructor(e){this.supabase=e}async reportBug(e,t){try{let r={...s.wD.parse(e),created_by:t},{data:i,error:o}=await this.supabase.from("bug_reports").insert(r).select().single();if(o)throw(0,a.Iu)(o);if(!i)throw new a.gz("Failed to create bug report",500);return i}catch(e){if(e instanceof a.gz)throw e;throw new a.gz("Failed to create bug report",500)}}async getUserReports(e,t){try{let r=this.supabase.from("bug_reports").select("*").eq("created_by",e);t?.status&&(r=r.eq("status",t.status)),t?.bug_type&&(r=r.eq("bug_type",t.bug_type)),t?.priority&&(r=r.eq("priority",t.priority));let{data:s,error:i}=await r.order("created_at",{ascending:!1});if(i)throw(0,a.Iu)(i);return s||[]}catch(e){if(e instanceof a.gz)throw e;throw new a.gz("Failed to fetch user reports",500)}}async getReportsByMinistry(e,t){try{let r=this.supabase.from("bug_reports").select("*").eq("ministry",e);t?.status&&(r=r.eq("status",t.status)),t?.bug_type&&(r=r.eq("bug_type",t.bug_type)),t?.priority&&(r=r.eq("priority",t.priority));let{data:s,error:i}=await r.order("created_at",{ascending:!1});if(i)throw(0,a.Iu)(i);return s||[]}catch(e){if(e instanceof a.gz)throw e;throw new a.gz("Failed to fetch ministry reports",500)}}async getAllReports(e){try{let t=this.supabase.from("bug_reports").select("*");e?.status&&(t=t.eq("status",e.status)),e?.bug_type&&(t=t.eq("bug_type",e.bug_type)),e?.ministry&&(t=t.eq("ministry",e.ministry)),e?.priority&&(t=t.eq("priority",e.priority));let{data:r,error:s}=await t.order("created_at",{ascending:!1});if(s)throw(0,a.Iu)(s);return r||[]}catch(e){if(e instanceof a.gz)throw e;throw new a.gz("Failed to fetch reports",500)}}async updateBugReport(e,t){try{let r=s.A5.parse(t),i={...r,updated_at:new Date().toISOString()};"resolved"!==r.status||r.resolved_at||(i.resolved_at=new Date().toISOString());let{data:o,error:n}=await this.supabase.from("bug_reports").update(i).eq("id",e).select().single();if(n)throw(0,a.Iu)(n);if(!o)throw new a.gz("Bug report not found",404);return o}catch(e){if(e instanceof a.gz)throw e;throw new a.gz("Failed to update bug report",500)}}async getBugReport(e){try{let{data:t,error:r}=await this.supabase.from("bug_reports").select("*").eq("id",e).single();if(r)throw(0,a.Iu)(r);if(!t)throw new a.gz("Bug report not found",404);return t}catch(e){if(e instanceof a.gz)throw e;throw new a.gz("Failed to fetch bug report",500)}}async deleteBugReport(e){try{let{error:t}=await this.supabase.from("bug_reports").delete().eq("id",e);if(t)throw(0,a.Iu)(t)}catch(e){if(e instanceof a.gz)throw e;throw new a.gz("Failed to delete bug report",500)}}}},2383:(e,t,r)=>{r.d(t,{J:()=>a});var s=r(3526);class a{constructor(e){this.supabase=e}async getUserRole(e){try{let{data:t,error:r}=await this.supabase.from("user_roles").select("*").eq("user_id",e).single();if(r){if("PGRST116"===r.code)return null;throw(0,s.Iu)(r)}return t}catch(e){if(e instanceof s.gz)throw e;throw new s.gz("Failed to fetch user role",500)}}async getCurrentUserRole(){try{let{data:{user:e},error:t}=await this.supabase.auth.getUser();if(t||!e)throw new s.gz("Authentication required",401);return this.getUserRole(e.id)}catch(e){if(e instanceof s.gz)throw e;throw new s.gz("Failed to fetch current user role",500)}}async isAdmin(){try{let e=await this.getCurrentUserRole();return!!e&&["admin","super_admin"].includes(e.role)}catch(e){return!1}}async isSuperAdmin(){try{let e=await this.getCurrentUserRole();return!!e&&"super_admin"===e.role}catch(e){return!1}}async assignRole(e,t,r){try{let{data:a,error:i}=await this.supabase.from("user_roles").upsert({user_id:e,role:t,ministry:r}).select().single();if(i)throw(0,s.Iu)(i);if(!a)throw new s.gz("Failed to assign role",500);return a}catch(e){if(e instanceof s.gz)throw e;throw new s.gz("Failed to assign role",500)}}async ensureUserRole(e){try{let t=await this.getUserRole(e);if(t)return t;let{data:r,error:a}=await this.supabase.from("user_roles").insert({user_id:e,role:"user"}).select().single();if(a)throw(0,s.Iu)(a);if(!r)throw new s.gz("Failed to create user role",500);return r}catch(e){if(e instanceof s.gz)throw e;throw new s.gz("Failed to ensure user role",500)}}}},9051:(e,t,r)=>{r.d(t,{f:()=>i});var s=r(4e3),a=r(6348);function i(){let e=(0,a.cookies)();return(0,s.createServerClient)("https://cakjxkbarwacvzxkgjsj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNha2p4a2JhcndhY3Z6eGtnanNqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3ODA5MTYsImV4cCI6MjA2NDM1NjkxNn0.GSfbuz9y1X1OlKtcaocsF5WggA7eoD-W9GcXz1dtUPk",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){e.set({name:t,value:r,...s})},remove(t,r){e.set({name:t,value:"",...r})}}})}},8358:(e,t,r)=>{r.d(t,{G:()=>u});var s=r(2903),a=r(3526);function i(e){return(0,a.HC)(e)?{success:!1,error:{message:e.message,code:e.code}}:e instanceof Error?{success:!1,error:{message:e.message,code:"UNKNOWN_ERROR"}}:{success:!1,error:{message:"An unknown error occurred",code:"UNKNOWN_ERROR",details:e}}}var o=r(9051),n=r(2383);function u({methods:e,schema:t,requireAuth:r=!0,requireAdmin:u=!1,requireSuperAdmin:c=!1,handler:d}){return async(l,{params:p})=>{try{let h,g;if(!e.includes(l.method))return s.NextResponse.json(i(new a.gz(`Method ${l.method} Not Allowed`,405,"METHOD_NOT_ALLOWED")),{status:405});if(["POST","PUT","PATCH"].includes(l.method)&&l.body)try{h=await l.json()}catch(e){return s.NextResponse.json(i(new a.gz("Invalid JSON body",400,"INVALID_JSON")),{status:400})}if(t&&h)try{h=t.parse(h)}catch(e){return s.NextResponse.json(i(new a.gz("Validation error",400,"VALIDATION_ERROR",e)),{status:400})}if(r||u||c){let e=(0,o.f)(),{data:{session:t}}=await e.auth.getSession();if(!t)return s.NextResponse.json(i(new a.gz("Authentication required",401,"UNAUTHORIZED")),{status:401});if(g=t.user.id,u||c){let t=new n.J(e);if(c){if(!await t.isSuperAdmin())return s.NextResponse.json(i(new a.gz("Super admin access required",403,"FORBIDDEN")),{status:403})}else if(u&&!await t.isAdmin())return s.NextResponse.json(i(new a.gz("Admin access required",403,"FORBIDDEN")),{status:403})}}let m=await d(l,p,h,g);return s.NextResponse.json({success:!0,data:m})}catch(t){console.error("API error:",t);let e=t instanceof a.gz?t.statusCode:500;return s.NextResponse.json(i(t),{status:e})}}}},3526:(e,t,r)=>{r.d(t,{HC:()=>i,Iu:()=>a,gz:()=>s});class s extends Error{constructor(e,t=500,r){super(e),this.statusCode=t,this.code=r,this.name="AppError"}}function a(e){return"PGRST301"===e.code?new s("Access denied",403,"ACCESS_DENIED"):"PGRST116"===e.code?new s("Resource not found",404,"NOT_FOUND"):"23505"===e.code?new s("Resource already exists",409,"DUPLICATE"):"23503"===e.code?new s("Invalid reference",400,"INVALID_REFERENCE"):new s(e.message||"Database error",500,"DATABASE_ERROR")}function i(e){return e instanceof s}},5668:(e,t,r)=>{r.d(t,{A5:()=>u,wD:()=>n});var s=r(9310);let a=s.z.enum(["security","ui","network","performance","data","authentication","integration","hardware","other"]),i=s.z.enum(["open","in_progress","resolved","closed","rejected"]),o=s.z.enum(["low","medium","high","critical"]),n=s.z.object({title:s.z.string().min(5,"Title must be at least 5 characters").max(200,"Title must not exceed 200 characters").trim(),description:s.z.string().min(10,"Description must be at least 10 characters").max(5e3,"Description must not exceed 5000 characters").trim(),ministry:s.z.string().min(2,"Ministry must be at least 2 characters").max(100,"Ministry must not exceed 100 characters").trim(),location:s.z.string().min(2,"Location must be at least 2 characters").max(100,"Location must not exceed 100 characters").trim(),unit:s.z.string().min(2,"Unit must be at least 2 characters").max(100,"Unit must not exceed 100 characters").trim(),bug_type:a.default("other"),affected_system:s.z.string().min(2,"Affected system must be at least 2 characters").max(100,"Affected system must not exceed 100 characters").trim(),priority:o.default("medium")}),u=s.z.object({title:s.z.string().min(5,"Title must be at least 5 characters").max(200,"Title must not exceed 200 characters").trim().optional(),description:s.z.string().min(10,"Description must be at least 10 characters").max(5e3,"Description must not exceed 5000 characters").trim().optional(),ministry:s.z.string().min(2,"Ministry must be at least 2 characters").max(100,"Ministry must not exceed 100 characters").trim().optional(),location:s.z.string().min(2,"Location must be at least 2 characters").max(100,"Location must not exceed 100 characters").trim().optional(),unit:s.z.string().min(2,"Unit must be at least 2 characters").max(100,"Unit must not exceed 100 characters").trim().optional(),bug_type:a.optional(),affected_system:s.z.string().min(2,"Affected system must be at least 2 characters").max(100,"Affected system must not exceed 100 characters").trim().optional(),status:i.optional(),priority:o.optional(),assigned_to:s.z.string().uuid().nullable().optional(),resolved_at:s.z.string().datetime().nullable().optional()})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[761],()=>r(9094));module.exports=s})();