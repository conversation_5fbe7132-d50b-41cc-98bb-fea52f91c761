(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[826],{67:e=>{"use strict";e.exports=require("node:async_hooks")},195:e=>{"use strict";e.exports=require("node:buffer")},913:(e,t,r)=>{"use strict";let i;r.r(t),r.d(t,{default:()=>eJ});var s,n,a,o,l,c,u,h,d,p,f,g,v={};async function m(){let e="_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&(await _ENTRIES.middleware_instrumentation).register;if(e)try{await e()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}r.r(v),r.d(v,{config:()=>ez,middleware:()=>eq});let b=null;function w(){return b||(b=m()),b}function y(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Error(y(e))},construct(){throw Error(y(e))},apply(r,i,s){if("function"==typeof s[0])return s[0](t);throw Error(y(e))}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),w();class _ extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class S extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class k extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}function x(e){var t,r,i,s,n,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,n=!1;l();)if(","===(r=e.charAt(o))){for(i=o,o+=1,l(),s=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(n=!0,o=s,a.push(e.substring(t,i)),t=o):o=i+1}else o+=1;(!n||o>=e.length)&&a.push(e.substring(t,e.length))}return a}function O(e){let t={},r=[];if(e)for(let[i,s]of e.entries())"set-cookie"===i.toLowerCase()?(r.push(...x(s)),t[i]=1===r.length?r[0]:r):t[i]=s;return t}function T(e){try{return String(new URL(String(e)))}catch(t){throw Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t})}}let C=Symbol("response"),P=Symbol("passThrough"),E=Symbol("waitUntil");class I{constructor(e){this[E]=[],this[P]=!1}respondWith(e){this[C]||(this[C]=Promise.resolve(e))}passThroughOnException(){this[P]=!0}waitUntil(e){this[E].push(e)}}class j extends I{constructor(e){super(e.request),this.sourcePage=e.page}get request(){throw new _({page:this.sourcePage})}respondWith(){throw new _({page:this.sourcePage})}}function R(e){return e.replace(/\/$/,"")||"/"}function A(e){let t=e.indexOf("#"),r=e.indexOf("?"),i=r>-1&&(t<0||r<t);return i||t>-1?{pathname:e.substring(0,i?r:t),query:i?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function N(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:s}=A(e);return""+t+r+i+s}function L(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:s}=A(e);return""+r+t+i+s}function M(e,t){if("string"!=typeof e)return!1;let{pathname:r}=A(e);return r===t||r.startsWith(t+"/")}function $(e,t){let r;let i=e.split("/");return(t||[]).some(t=>!!i[1]&&i[1].toLowerCase()===t.toLowerCase()&&(r=t,i.splice(1,1),e=i.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}let D=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function U(e,t){return new URL(String(e).replace(D,"localhost"),t&&String(t).replace(D,"localhost"))}let B=Symbol("NextURLInternal");class q{constructor(e,t,r){let i,s;"object"==typeof t&&"pathname"in t||"string"==typeof t?(i=t,s=r||{}):s=r||t||{},this[B]={url:U(e,i??s.base),options:s,basePath:""},this.analyze()}analyze(){var e,t,r,i,s;let n=function(e,t){var r,i;let{basePath:s,i18n:n,trailingSlash:a}=null!=(r=t.nextConfig)?r:{},o={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):a};s&&M(o.pathname,s)&&(o.pathname=function(e,t){if(!M(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(o.pathname,s),o.basePath=s);let l=o.pathname;if(o.pathname.startsWith("/_next/data/")&&o.pathname.endsWith(".json")){let e=o.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),r=e[0];o.buildId=r,l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(o.pathname=l)}if(n){let e=t.i18nProvider?t.i18nProvider.analyze(o.pathname):$(o.pathname,n.locales);o.locale=e.detectedLocale,o.pathname=null!=(i=e.pathname)?i:o.pathname,!e.detectedLocale&&o.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):$(l,n.locales)).detectedLocale&&(o.locale=e.detectedLocale)}return o}(this[B].url.pathname,{nextConfig:this[B].options.nextConfig,parseData:!0,i18nProvider:this[B].options.i18nProvider}),a=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[B].url,this[B].options.headers);this[B].domainLocale=this[B].options.i18nProvider?this[B].options.i18nProvider.detectDomainLocale(a):function(e,t,r){if(e)for(let n of(r&&(r=r.toLowerCase()),e)){var i,s;if(t===(null==(i=n.domain)?void 0:i.split(":",1)[0].toLowerCase())||r===n.defaultLocale.toLowerCase()||(null==(s=n.locales)?void 0:s.some(e=>e.toLowerCase()===r)))return n}}(null==(t=this[B].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.domains,a);let o=(null==(r=this[B].domainLocale)?void 0:r.defaultLocale)||(null==(s=this[B].options.nextConfig)?void 0:null==(i=s.i18n)?void 0:i.defaultLocale);this[B].url.pathname=n.pathname,this[B].defaultLocale=o,this[B].basePath=n.basePath??"",this[B].buildId=n.buildId,this[B].locale=n.locale??o,this[B].trailingSlash=n.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,i){if(!t||t===r)return e;let s=e.toLowerCase();return!i&&(M(s,"/api")||M(s,"/"+t.toLowerCase()))?e:N(e,"/"+t)}((e={basePath:this[B].basePath,buildId:this[B].buildId,defaultLocale:this[B].options.forceLocale?void 0:this[B].defaultLocale,locale:this[B].locale,pathname:this[B].url.pathname,trailingSlash:this[B].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=R(t)),e.buildId&&(t=L(N(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=N(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:L(t,"/"):R(t)}formatSearch(){return this[B].url.search}get buildId(){return this[B].buildId}set buildId(e){this[B].buildId=e}get locale(){return this[B].locale??""}set locale(e){var t,r;if(!this[B].locale||!(null==(r=this[B].options.nextConfig)?void 0:null==(t=r.i18n)?void 0:t.locales.includes(e)))throw TypeError(`The NextURL configuration includes no locale "${e}"`);this[B].locale=e}get defaultLocale(){return this[B].defaultLocale}get domainLocale(){return this[B].domainLocale}get searchParams(){return this[B].url.searchParams}get host(){return this[B].url.host}set host(e){this[B].url.host=e}get hostname(){return this[B].url.hostname}set hostname(e){this[B].url.hostname=e}get port(){return this[B].url.port}set port(e){this[B].url.port=e}get protocol(){return this[B].url.protocol}set protocol(e){this[B].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[B].url=U(e),this.analyze()}get origin(){return this[B].url.origin}get pathname(){return this[B].url.pathname}set pathname(e){this[B].url.pathname=e}get hash(){return this[B].url.hash}set hash(e){this[B].url.hash=e}get search(){return this[B].url.search}set search(e){this[B].url.search=e}get password(){return this[B].url.password}set password(e){this[B].url.password=e}get username(){return this[B].url.username}set username(e){this[B].url.username=e}get basePath(){return this[B].basePath}set basePath(e){this[B].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new q(String(this),this[B].options)}}var z=r(994);let V=Symbol("internal request");class F extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);T(r),e instanceof Request?super(e,t):super(r,t);let i=new q(r,{headers:O(this.headers),nextConfig:t.nextConfig});this[V]={cookies:new z.RequestCookies(this.headers),geo:t.geo||{},ip:t.ip,nextUrl:i,url:i.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,geo:this.geo,ip:this.ip,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[V].cookies}get geo(){return this[V].geo}get ip(){return this[V].ip}get nextUrl(){return this[V].nextUrl}get page(){throw new S}get ua(){throw new k}get url(){return this[V].url}}class G{static get(e,t,r){let i=Reflect.get(e,t,r);return"function"==typeof i?i.bind(e):i}static set(e,t,r,i){return Reflect.set(e,t,r,i)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}let J=Symbol("internal response"),H=new Set([301,302,303,307,308]);function K(e,t){var r;if(null==e?void 0:null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Error("request.headers must be an instance of Headers");let r=[];for(let[i,s]of e.request.headers)t.set("x-middleware-request-"+i,s),r.push(i);t.set("x-middleware-override-headers",r.join(","))}}class W extends Response{constructor(e,t={}){super(e,t);let r=this.headers,i=new Proxy(new z.ResponseCookies(r),{get(e,i,s){switch(i){case"delete":case"set":return(...s)=>{let n=Reflect.apply(e[i],e,s),a=new Headers(r);return n instanceof z.ResponseCookies&&r.set("x-middleware-set-cookie",n.getAll().map(e=>(0,z.stringifyCookie)(e)).join(",")),K(t,a),n};default:return G.get(e,i,s)}}});this[J]={cookies:i,url:t.url?new q(t.url,{headers:O(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[J].cookies}static json(e,t){let r=Response.json(e,t);return new W(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!H.has(r))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');let i="object"==typeof t?t:{},s=new Headers(null==i?void 0:i.headers);return s.set("Location",T(e)),new W(null,{...i,headers:s,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",T(e)),K(t,r),new W(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),K(e,t),new W(null,{...e,headers:t})}}function X(e,t){let r="string"==typeof t?new URL(t):t,i=new URL(e,t),s=r.protocol+"//"+r.host;return i.protocol+"//"+i.host===s?i.toString().replace(s,""):i.toString()}let Y=[["RSC"],["Next-Router-State-Tree"],["Next-Router-Prefetch"]],Z=["__nextFallback","__nextLocale","__nextInferredLocaleFromDefault","__nextDefaultLocale","__nextIsNotFound","_rsc"],Q=["__nextDataReq"],ee="nxtP",et={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"};({...et,GROUP:{serverOnly:[et.reactServerComponents,et.actionBrowser,et.appMetadataRoute,et.appRouteHandler,et.instrument],clientOnly:[et.serverSideRendering,et.appPagesBrowser],nonClientServerTarget:[et.middleware,et.api],app:[et.reactServerComponents,et.actionBrowser,et.appMetadataRoute,et.appRouteHandler,et.serverSideRendering,et.appPagesBrowser,et.shared,et.instrument]}});class er extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new er}}class ei extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return G.get(t,r,i);let s=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===s);if(void 0!==n)return G.get(t,n,i)},set(t,r,i,s){if("symbol"==typeof r)return G.set(t,r,i,s);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);return G.set(t,a??r,i,s)},has(t,r){if("symbol"==typeof r)return G.has(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==s&&G.has(t,s)},deleteProperty(t,r){if("symbol"==typeof r)return G.deleteProperty(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===s||G.deleteProperty(t,s)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return er.callable;default:return G.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new ei(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,i]of this.entries())e.call(t,i,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}let es=Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available");class en{disable(){throw es}getStore(){}run(){throw es}exit(){throw es}enterWith(){throw es}}let ea=globalThis.AsyncLocalStorage;function eo(){return ea?new ea:new en}let el=eo();class ec extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new ec}}class eu{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return ec.callable;default:return G.get(e,t,r)}}})}}let eh=Symbol.for("next.mutated.cookies");class ed{static wrap(e,t){let r=new z.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let i=[],s=new Set,n=()=>{let e=el.getStore();if(e&&(e.pathWasRevalidated=!0),i=r.getAll().filter(e=>s.has(e.name)),t){let e=[];for(let t of i){let r=new z.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case eh:return i;case"delete":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{n()}};case"set":return function(...t){s.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{n()}};default:return G.get(e,t,r)}}})}}!function(e){e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404"}(s||(s={})),function(e){e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents"}(n||(n={})),function(e){e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer"}(a||(a={})),function(e){e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch"}(o||(o={})),(l||(l={})).startServer="startServer.startServer",function(e){e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult"}(c||(c={})),function(e){e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch"}(u||(u={})),(h||(h={})).executeRoute="Router.executeRoute",(d||(d={})).runHandler="Node.runHandler",(p||(p={})).runHandler="AppRouteRouteHandlers.runHandler",function(e){e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport"}(f||(f={})),(g||(g={})).execute="Middleware.execute";let ep=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],ef=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"],{context:eg,propagation:ev,trace:em,SpanStatusCode:eb,SpanKind:ew,ROOT_CONTEXT:ey}=i=r(9),e_=e=>null!==e&&"object"==typeof e&&"function"==typeof e.then,eS=(e,t)=>{(null==t?void 0:t.bubble)===!0?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:eb.ERROR,message:null==t?void 0:t.message})),e.end()},ek=new Map,ex=i.createContextKey("next.rootSpanId"),eO=0,eT=()=>eO++;class eC{getTracerInstance(){return em.getTracer("next.js","0.0.1")}getContext(){return eg}getActiveScopeSpan(){return em.getSpan(null==eg?void 0:eg.active())}withPropagatedContext(e,t,r){let i=eg.active();if(em.getSpanContext(i))return t();let s=ev.extract(i,e,r);return eg.with(s,t)}trace(...e){var t;let[r,i,s]=e,{fn:n,options:a}="function"==typeof i?{fn:i,options:{}}:{fn:s,options:{...i}},o=a.spanName??r;if(!ep.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||a.hideSpan)return n();let l=this.getSpanContext((null==a?void 0:a.parentSpan)??this.getActiveScopeSpan()),c=!1;l?(null==(t=em.getSpanContext(l))?void 0:t.isRemote)&&(c=!0):(l=(null==eg?void 0:eg.active())??ey,c=!0);let u=eT();return a.attributes={"next.span_name":o,"next.span_type":r,...a.attributes},eg.with(l.setValue(ex,u),()=>this.getTracerInstance().startActiveSpan(o,a,e=>{let t="performance"in globalThis?globalThis.performance.now():void 0,i=()=>{ek.delete(u),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&ef.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};c&&ek.set(u,new Map(Object.entries(a.attributes??{})));try{if(n.length>1)return n(e,t=>eS(e,t));let t=n(e);if(e_(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eS(e,t),t}).finally(i);return e.end(),i(),t}catch(t){throw eS(e,t),i(),t}}))}wrap(...e){let t=this,[r,i,s]=3===e.length?e:[e[0],{},e[1]];return ep.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=i;"function"==typeof e&&"function"==typeof s&&(e=e.apply(this,arguments));let n=arguments.length-1,a=arguments[n];if("function"!=typeof a)return t.trace(r,e,()=>s.apply(this,arguments));{let i=t.getContext().bind(eg.active(),a);return t.trace(r,e,(e,t)=>(arguments[n]=function(e){return null==t||t(e),i.apply(this,arguments)},s.apply(this,arguments)))}}:s}startSpan(...e){let[t,r]=e,i=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,i)}getSpanContext(e){return e?em.setSpan(eg.active(),e):void 0}getRootSpanAttributes(){let e=eg.active().getValue(ex);return ek.get(e)}}let eP=(()=>{let e=new eC;return()=>e})(),eE="__prerender_bypass";Symbol("__next_preview_data"),Symbol(eE);class eI{constructor(e,t,r,i){var s;let n=e&&function(e,t){let r=ei.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,a=null==(s=r.get(eE))?void 0:s.value;this.isEnabled=!!(!n&&a&&e&&a===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=i}enable(){if(!this._previewModeId)throw Error("Invariant: previewProps missing previewModeId this should never happen");this._mutableCookies.set({name:eE,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"})}disable(){this._mutableCookies.set({name:eE,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)})}}function ej(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],i=new Headers;for(let e of x(r))i.append("set-cookie",e);for(let e of new z.ResponseCookies(i).getAll())t.set(e)}}let eR={wrap(e,{req:t,res:r,renderOpts:i},s){let n;function a(e){r&&r.setHeader("Set-Cookie",e)}i&&"previewProps"in i&&(n=i.previewProps);let o={},l={get headers(){return o.headers||(o.headers=function(e){let t=ei.from(e);for(let e of Y)t.delete(e.toString().toLowerCase());return ei.seal(t)}(t.headers)),o.headers},get cookies(){if(!o.cookies){let e=new z.RequestCookies(ei.from(t.headers));ej(t,e),o.cookies=eu.seal(e)}return o.cookies},get mutableCookies(){if(!o.mutableCookies){let e=function(e,t){let r=new z.RequestCookies(ei.from(e));return ed.wrap(r,t)}(t.headers,(null==i?void 0:i.onUpdateCookies)||(r?a:void 0));ej(t,e),o.mutableCookies=e}return o.mutableCookies},get draftMode(){return o.draftMode||(o.draftMode=new eI(n,t,this.cookies,this.mutableCookies)),o.draftMode},reactLoadableManifest:(null==i?void 0:i.reactLoadableManifest)||{},assetPrefix:(null==i?void 0:i.assetPrefix)||""};return e.run(l,s,l)}},eA=eo();function eN(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}class eL extends F{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw new _({page:this.sourcePage})}respondWith(){throw new _({page:this.sourcePage})}waitUntil(){throw new _({page:this.sourcePage})}}let eM={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},e$=(e,t)=>eP().withPropagatedContext(e.headers,t,eM),eD=!1;async function eU(e){let t,i;!function(){if(!eD&&(eD=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(424);e(),e$=t(e$)}}(),await w();let s=void 0!==self.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let n=new q(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...n.searchParams.keys()]){let t=n.searchParams.getAll(e);if(e!==ee&&e.startsWith(ee)){let r=e.substring(ee.length);for(let e of(n.searchParams.delete(r),t))n.searchParams.append(r,e);n.searchParams.delete(e)}}let a=n.buildId;n.buildId="";let o=e.request.headers["x-nextjs-data"];o&&"/index"===n.pathname&&(n.pathname="/");let l=function(e){let t=new Headers;for(let[r,i]of Object.entries(e))for(let e of Array.isArray(i)?i:[i])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),c=new Map;if(!s)for(let e of Y){let t=e.toString().toLowerCase();l.get(t)&&(c.set(t,l.get(t)),l.delete(t))}let u=new eL({page:e.page,input:(function(e,t){let r="string"==typeof e,i=r?new URL(e):e;for(let e of Z)i.searchParams.delete(e);if(t)for(let e of Q)i.searchParams.delete(e);return r?i.toString():i})(n,!0).toString(),init:{body:e.request.body,geo:e.request.geo,headers:l,ip:e.request.ip,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});o&&Object.defineProperty(u,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:eN()})}));let h=new j({request:u,page:e.page});if((t=await e$(u,()=>"/middleware"===e.page||"/src/middleware"===e.page?eP().trace(g.execute,{spanName:`middleware ${u.method} ${u.nextUrl.pathname}`,attributes:{"http.target":u.nextUrl.pathname,"http.method":u.method}},()=>eR.wrap(eA,{req:u,renderOpts:{onUpdateCookies:e=>{i=e},previewProps:eN()}},()=>e.handler(u,h))):e.handler(u,h)))&&!(t instanceof Response))throw TypeError("Expected an instance of Response to be returned");t&&i&&t.headers.set("set-cookie",i);let d=null==t?void 0:t.headers.get("x-middleware-rewrite");if(t&&d&&!s){let r=new q(d,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});r.host===u.nextUrl.host&&(r.buildId=a||r.buildId,t.headers.set("x-middleware-rewrite",String(r)));let i=X(String(r),String(n));o&&t.headers.set("x-nextjs-rewrite",i)}let p=null==t?void 0:t.headers.get("Location");if(t&&p&&!s){let r=new q(p,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});t=new Response(t.body,t),r.host===u.nextUrl.host&&(r.buildId=a||r.buildId,t.headers.set("Location",String(r))),o&&(t.headers.delete("Location"),t.headers.set("x-nextjs-redirect",X(String(r),String(n))))}let f=t||W.next(),v=f.headers.get("x-middleware-override-headers"),m=[];if(v){for(let[e,t]of c)f.headers.set(`x-middleware-request-${e}`,t),m.push(e);m.length>0&&f.headers.set("x-middleware-override-headers",v+","+m.join(","))}return{response:f,waitUntil:Promise.all(h[E]),fetchMetrics:u.fetchMetrics}}r(850),"undefined"==typeof URLPattern||URLPattern;var eB=r(346);async function eq(e){let t=W.next(),r=(0,eB.createMiddlewareClient)({req:e,res:t}),{data:{session:i}}=await r.auth.getSession();return!i&&e.nextUrl.pathname.startsWith("/api/")?W.json({error:"Authentication required",code:"UNAUTHORIZED"},{status:401}):t}let ez={matcher:["/api/:path*"]},eV={...v},eF=eV.middleware||eV.default,eG="/middleware";if("function"!=typeof eF)throw Error(`The Middleware "${eG}" must export a \`middleware\` or a \`default\` function`);function eJ(e){return eU({...e,page:eG,handler:eF})}},346:(e,t,r)=>{"use strict";var i,s=Object.defineProperty,n=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.prototype.hasOwnProperty,l={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(l,{createBrowserSupabaseClient:()=>C,createClientComponentClient:()=>u,createMiddlewareClient:()=>w,createMiddlewareSupabaseClient:()=>E,createPagesBrowserClient:()=>h,createPagesServerClient:()=>g,createRouteHandlerClient:()=>O,createServerActionClient:()=>T,createServerComponentClient:()=>S,createServerSupabaseClient:()=>P}),e.exports=((e,t,r,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let l of a(t))o.call(e,l)||l===r||s(e,l,{get:()=>t[l],enumerable:!(i=n(t,l))||i.enumerable});return e})(s({},"__esModule",{value:!0}),l);var c=r(602);function u({supabaseUrl:e="https://cakjxkbarwacvzxkgjsj.supabase.co",supabaseKey:t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNha2p4a2JhcndhY3Z6eGtnanNqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3ODA5MTYsImV4cCI6MjA2NDM1NjkxNn0.GSfbuz9y1X1OlKtcaocsF5WggA7eoD-W9GcXz1dtUPk",options:r,cookieOptions:s,isSingleton:n=!0}={}){if(!e||!t)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");let a=()=>{var i;return(0,c.createSupabaseClient)(e,t,{...r,global:{...null==r?void 0:r.global,headers:{...null==(i=null==r?void 0:r.global)?void 0:i.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new c.BrowserCookieAuthStorageAdapter(s)}})};if(n){let e=i??a();return"undefined"==typeof window?e:(i||(i=e),i)}return a()}var h=u,d=r(602),p=r(393),f=class extends d.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t,r,i;return(0,p.splitCookiesString)((null==(r=null==(t=this.context.res)?void 0:t.getHeader("set-cookie"))?void 0:r.toString())??"").map(t=>(0,d.parseCookies)(t)[e]).find(e=>!!e)??(null==(i=this.context.req)?void 0:i.cookies[e])}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){var i;let s=(0,p.splitCookiesString)((null==(i=this.context.res.getHeader("set-cookie"))?void 0:i.toString())??"").filter(t=>!(e in(0,d.parseCookies)(t))),n=(0,d.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.setHeader("set-cookie",[...s,n])}};function g(e,{supabaseUrl:t="https://cakjxkbarwacvzxkgjsj.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNha2p4a2JhcndhY3Z6eGtnanNqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3ODA5MTYsImV4cCI6MjA2NDM1NjkxNn0.GSfbuz9y1X1OlKtcaocsF5WggA7eoD-W9GcXz1dtUPk",options:i,cookieOptions:s}={}){var n;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,d.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(n=null==i?void 0:i.global)?void 0:n.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new f(e,s)}})}var v=r(602),m=r(393),b=class extends v.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return(0,m.splitCookiesString)((null==(t=this.context.res.headers.get("set-cookie"))?void 0:t.toString())??"").map(t=>(0,v.parseCookies)(t)[e]).find(e=>!!e)||(0,v.parseCookies)(this.context.req.headers.get("cookie")??"")[e]}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){let i=(0,v.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.headers&&this.context.res.headers.append("set-cookie",i)}};function w(e,{supabaseUrl:t="https://cakjxkbarwacvzxkgjsj.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNha2p4a2JhcndhY3Z6eGtnanNqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3ODA5MTYsImV4cCI6MjA2NDM1NjkxNn0.GSfbuz9y1X1OlKtcaocsF5WggA7eoD-W9GcXz1dtUPk",options:i,cookieOptions:s}={}){var n;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,v.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(n=null==i?void 0:i.global)?void 0:n.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new b(e,s)}})}var y=r(602),_=class extends y.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e,this.isServer=!0}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){}deleteCookie(e){}};function S(e,{supabaseUrl:t="https://cakjxkbarwacvzxkgjsj.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNha2p4a2JhcndhY3Z6eGtnanNqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3ODA5MTYsImV4cCI6MjA2NDM1NjkxNn0.GSfbuz9y1X1OlKtcaocsF5WggA7eoD-W9GcXz1dtUPk",options:i,cookieOptions:s}={}){var n;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,y.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(n=null==i?void 0:i.global)?void 0:n.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new _(e,s)}})}var k=r(602),x=class extends k.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){this.context.cookies().set(e,t,this.cookieOptions)}deleteCookie(e){this.context.cookies().set(e,"",{...this.cookieOptions,maxAge:0})}};function O(e,{supabaseUrl:t="https://cakjxkbarwacvzxkgjsj.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNha2p4a2JhcndhY3Z6eGtnanNqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3ODA5MTYsImV4cCI6MjA2NDM1NjkxNn0.GSfbuz9y1X1OlKtcaocsF5WggA7eoD-W9GcXz1dtUPk",options:i,cookieOptions:s}={}){var n;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,k.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(n=null==i?void 0:i.global)?void 0:n.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.10.0"}},auth:{storage:new x(e,s)}})}var T=O;function C({supabaseUrl:e="https://cakjxkbarwacvzxkgjsj.supabase.co",supabaseKey:t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNha2p4a2JhcndhY3Z6eGtnanNqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3ODA5MTYsImV4cCI6MjA2NDM1NjkxNn0.GSfbuz9y1X1OlKtcaocsF5WggA7eoD-W9GcXz1dtUPk",options:r,cookieOptions:i}={}){return console.warn("Please utilize the `createPagesBrowserClient` function instead of the deprecated `createBrowserSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),h({supabaseUrl:e,supabaseKey:t,options:r,cookieOptions:i})}function P(e,{supabaseUrl:t="https://cakjxkbarwacvzxkgjsj.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNha2p4a2JhcndhY3Z6eGtnanNqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3ODA5MTYsImV4cCI6MjA2NDM1NjkxNn0.GSfbuz9y1X1OlKtcaocsF5WggA7eoD-W9GcXz1dtUPk",options:i,cookieOptions:s}={}){return console.warn("Please utilize the `createPagesServerClient` function instead of the deprecated `createServerSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),g(e,{supabaseUrl:t,supabaseKey:r,options:i,cookieOptions:s})}function E(e,{supabaseUrl:t="https://cakjxkbarwacvzxkgjsj.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNha2p4a2JhcndhY3Z6eGtnanNqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3ODA5MTYsImV4cCI6MjA2NDM1NjkxNn0.GSfbuz9y1X1OlKtcaocsF5WggA7eoD-W9GcXz1dtUPk",options:i,cookieOptions:s}={}){return console.warn("Please utilize the `createMiddlewareClient` function instead of the deprecated `createMiddlewareSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#middleware"),w(e,{supabaseUrl:t,supabaseKey:r,options:i,cookieOptions:s})}},23:(e,t,r)=>{"use strict";r.r(t),r.d(t,{Headers:()=>a,Request:()=>o,Response:()=>l,default:()=>n,fetch:()=>s});var i=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw Error("unable to locate global object")}();let s=i.fetch,n=i.fetch.bind(i),a=i.Headers,o=i.Request,l=i.Response},317:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(23)),n=i(r(756));class a{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=s.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,r,i;let s=null,a=null,o=null,l=e.status,c=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(a="text/csv"===this.headers.Accept?t:this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let i=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),n=null===(r=e.headers.get("content-range"))||void 0===r?void 0:r.split("/");i&&n&&n.length>1&&(o=parseInt(n[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(a)&&(a.length>1?(s={code:"PGRST116",details:`Results contain ${a.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},a=null,o=null,l=406,c="Not Acceptable"):a=1===a.length?a[0]:null)}else{let t=await e.text();try{s=JSON.parse(t),Array.isArray(s)&&404===e.status&&(a=[],s=null,l=200,c="OK")}catch(r){404===e.status&&""===t?(l=204,c="No Content"):s={message:t}}if(s&&this.isMaybeSingle&&(null===(i=null==s?void 0:s.details)||void 0===i?void 0:i.includes("0 rows"))&&(s=null,l=200,c="OK"),s&&this.shouldThrowOnError)throw new n.default(s)}return{error:s,data:a,count:o,status:l,statusText:c}});return this.shouldThrowOnError||(r=r.catch(e=>{var t,r,i;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(r=null==e?void 0:e.stack)&&void 0!==r?r:""}`,hint:"",code:`${null!==(i=null==e?void 0:e.code)&&void 0!==i?i:""}`},data:null,count:null,status:0,statusText:""}})),r.then(e,t)}returns(){return this}overrideTypes(){return this}}t.default=a},767:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(586)),n=i(r(614)),a=r(382);class o{constructor(e,{headers:t={},schema:r,fetch:i}={}){this.url=e,this.headers=Object.assign(Object.assign({},a.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=i}from(e){let t=new URL(`${this.url}/${e}`);return new s.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new o(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:i=!1,count:s}={}){let a,o;let l=new URL(`${this.url}/rpc/${e}`);r||i?(a=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{l.searchParams.append(e,t)})):(a="POST",o=t);let c=Object.assign({},this.headers);return s&&(c.Prefer=`count=${s}`),new n.default({method:a,url:l,headers:c,schema:this.schemaName,body:o,fetch:this.fetch,allowEmpty:!1})}}t.default=o},756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=r},614:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(798));class n extends s.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let r=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:i}={}){let s="";"plain"===i?s="pl":"phrase"===i?s="ph":"websearch"===i&&(s="w");let n=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${s}fts${n}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){let i=r?`${r}.or`:"or";return this.url.searchParams.append(i,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}}t.default=n},586:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(614));class n{constructor(e,{headers:t={},schema:r,fetch:i}){this.url=e,this.headers=t,this.schema=r,this.fetch=i}select(e,{head:t=!1,count:r}={}){let i=!1,n=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!i?"":('"'===e&&(i=!i),e)).join("");return this.url.searchParams.set("select",n),r&&(this.headers.Prefer=`count=${r}`),new s.default({method:t?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:r=!0}={}){let i=[];if(this.headers.Prefer&&i.push(this.headers.Prefer),t&&i.push(`count=${t}`),r||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new s.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:i,defaultToNull:n=!0}={}){let a=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&a.push(this.headers.Prefer),i&&a.push(`count=${i}`),n||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new s.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){let r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new s.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new s.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}t.default=n},798:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(317));class n extends s.default{select(e){let t=!1,r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:i,referencedTable:s=i}={}){let n=s?`${s}.order`:"order",a=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){let i=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:i=r}={}){let s=void 0===i?"offset":`${i}.offset`,n=void 0===i?"limit":`${i}.limit`;return this.url.searchParams.set(s,`${e}`),this.url.searchParams.set(n,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:i=!1,wal:s=!1,format:n="text"}={}){var a;let o=[e?"analyze":null,t?"verbose":null,r?"settings":null,i?"buffers":null,s?"wal":null].filter(Boolean).join("|"),l=null!==(a=this.headers.Accept)&&void 0!==a?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${n}; for="${l}"; options=${o};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=n},382:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;let i=r(517);t.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${i.version}`}},124:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;let s=i(r(767));t.PostgrestClient=s.default;let n=i(r(586));t.PostgrestQueryBuilder=n.default;let a=i(r(614));t.PostgrestFilterBuilder=a.default;let o=i(r(798));t.PostgrestTransformBuilder=o.default;let l=i(r(317));t.PostgrestBuilder=l.default;let c=i(r(756));t.PostgrestError=c.default,t.default={PostgrestClient:s.default,PostgrestQueryBuilder:n.default,PostgrestFilterBuilder:a.default,PostgrestTransformBuilder:o.default,PostgrestBuilder:l.default,PostgrestError:c.default}},517:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},994:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,n={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),i=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?i:`${i}; ${r.join("; ")}`}function o(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[i,s]=[r.slice(0,e),r.slice(e+1)];try{t.set(i,decodeURIComponent(null!=s?s:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[i,s],...n]=o(e),{domain:a,expires:l,httponly:h,maxage:d,path:p,samesite:f,secure:g,partitioned:v,priority:m}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:i,value:decodeURIComponent(s),domain:a,...l&&{expires:new Date(l)},...h&&{httpOnly:!0},..."string"==typeof d&&{maxAge:Number(d)},path:p,...f&&{sameSite:c.includes(t=(t=f).toLowerCase())?t:void 0},...g&&{secure:!0},...m&&{priority:u.includes(r=(r=m).toLowerCase())?r:void 0},...v&&{partitioned:!0}})}((e,r)=>{for(var i in r)t(e,i,{get:r[i],enumerable:!0})})(n,{RequestCookies:()=>h,ResponseCookies:()=>d,parseCookie:()=>o,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,n,a,o)=>{if(n&&"object"==typeof n||"function"==typeof n)for(let l of i(n))s.call(e,l)||l===a||t(e,l,{get:()=>n[l],enumerable:!(o=r(n,l))||o.enumerable});return e})(t({},"__esModule",{value:!0}),n);var c=["strict","lax","none"],u=["low","medium","high"],h=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of o(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===i).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,i=this._parsed;return i.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(i).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},d=class{constructor(e){var t,r,i;this._parsed=new Map,this._headers=e;let s=null!=(i=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?i:[];for(let e of Array.isArray(s)?s:function(e){if(!e)return[];var t,r,i,s,n,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,n=!1;l();)if(","===(r=e.charAt(o))){for(i=o,o+=1,l(),s=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(n=!0,o=s,a.push(e.substring(t,i)),t=o):o=i+1}else o+=1;(!n||o>=e.length)&&a.push(e.substring(t,e.length))}return a}(s)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===i)}has(e){return this._parsed.has(e)}set(...e){let[t,r,i]=1===e.length?[e[0].name,e[0].value,e[0]]:e,s=this._parsed;return s.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...i})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(s,this._headers),this}delete(...e){let[t,r,i]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:i,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},9:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let i=r(223),s=r(172),n=r(930),a="context",o=new i.NoopContextManager;class l{constructor(){}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalContextManager(e){return(0,s.registerGlobal)(a,e,n.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...i){return this._getContextManager().with(e,t,r,...i)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,s.getGlobal)(a)||o}disable(){this._getContextManager().disable(),(0,s.unregisterGlobal)(a,n.DiagAPI.instance())}}t.ContextAPI=l},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let i=r(56),s=r(912),n=r(957),a=r(172);class o{constructor(){function e(e){return function(...t){let r=(0,a.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:n.DiagLogLevel.INFO})=>{var i,o,l;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!==(i=e.stack)&&void 0!==i?i:e.message),!1}"number"==typeof r&&(r={logLevel:r});let c=(0,a.getGlobal)("diag"),u=(0,s.createLogLevelDiagLogger)(null!==(o=r.logLevel)&&void 0!==o?o:n.DiagLogLevel.INFO,e);if(c&&!r.suppressOverrideMessage){let e=null!==(l=Error().stack)&&void 0!==l?l:"<failed to generate stacktrace>";c.warn(`Current logger will be overwritten from ${e}`),u.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,a.registerGlobal)("diag",u,t,!0)},t.disable=()=>{(0,a.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new i.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new o),this._instance}}t.DiagAPI=o},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let i=r(660),s=r(172),n=r(930),a="metrics";class o{constructor(){}static getInstance(){return this._instance||(this._instance=new o),this._instance}setGlobalMeterProvider(e){return(0,s.registerGlobal)(a,e,n.DiagAPI.instance())}getMeterProvider(){return(0,s.getGlobal)(a)||i.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,s.unregisterGlobal)(a,n.DiagAPI.instance())}}t.MetricsAPI=o},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let i=r(172),s=r(874),n=r(194),a=r(277),o=r(369),l=r(930),c="propagation",u=new s.NoopTextMapPropagator;class h{constructor(){this.createBaggage=o.createBaggage,this.getBaggage=a.getBaggage,this.getActiveBaggage=a.getActiveBaggage,this.setBaggage=a.setBaggage,this.deleteBaggage=a.deleteBaggage}static getInstance(){return this._instance||(this._instance=new h),this._instance}setGlobalPropagator(e){return(0,i.registerGlobal)(c,e,l.DiagAPI.instance())}inject(e,t,r=n.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=n.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,i.unregisterGlobal)(c,l.DiagAPI.instance())}_getGlobalPropagator(){return(0,i.getGlobal)(c)||u}}t.PropagationAPI=h},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let i=r(172),s=r(846),n=r(139),a=r(607),o=r(930),l="trace";class c{constructor(){this._proxyTracerProvider=new s.ProxyTracerProvider,this.wrapSpanContext=n.wrapSpanContext,this.isSpanContextValid=n.isSpanContextValid,this.deleteSpan=a.deleteSpan,this.getSpan=a.getSpan,this.getActiveSpan=a.getActiveSpan,this.getSpanContext=a.getSpanContext,this.setSpan=a.setSpan,this.setSpanContext=a.setSpanContext}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalTracerProvider(e){let t=(0,i.registerGlobal)(l,this._proxyTracerProvider,o.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,i.getGlobal)(l)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,i.unregisterGlobal)(l,o.DiagAPI.instance()),this._proxyTracerProvider=new s.ProxyTracerProvider}}t.TraceAPI=c},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let i=r(491),s=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function n(e){return e.getValue(s)||void 0}t.getBaggage=n,t.getActiveBaggage=function(){return n(i.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(s,t)},t.deleteBaggage=function(e){return e.deleteValue(s)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let i=new r(this._entries);return i._entries.set(e,t),i}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let i=r(930),s=r(993),n=r(830),a=i.DiagAPI.instance();t.createBaggage=function(e={}){return new s.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(a.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:n.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0;let i=r(491);t.context=i.ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let i=r(780);class s{active(){return i.ROOT_CONTEXT}with(e,t,r,...i){return t.call(r,...i)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=s},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,i)=>{let s=new r(t._currentContext);return s._currentContext.set(e,i),s},t.deleteValue=e=>{let i=new r(t._currentContext);return i._currentContext.delete(e),i}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0;let i=r(930);t.diag=i.DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let i=r(172);class s{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return n("debug",this._namespace,e)}error(...e){return n("error",this._namespace,e)}info(...e){return n("info",this._namespace,e)}warn(...e){return n("warn",this._namespace,e)}verbose(...e){return n("verbose",this._namespace,e)}}function n(e,t,r){let s=(0,i.getGlobal)("diag");if(s)return r.unshift(t),s[e](...r)}t.DiagComponentLogger=s},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class i{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=i},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let i=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,i){let s=t[r];return"function"==typeof s&&e>=i?s.bind(t):function(){}}return e<i.DiagLogLevel.NONE?e=i.DiagLogLevel.NONE:e>i.DiagLogLevel.ALL&&(e=i.DiagLogLevel.ALL),t=t||{},{error:r("error",i.DiagLogLevel.ERROR),warn:r("warn",i.DiagLogLevel.WARN),info:r("info",i.DiagLogLevel.INFO),debug:r("debug",i.DiagLogLevel.DEBUG),verbose:r("verbose",i.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let i=r(200),s=r(521),n=r(130),a=s.VERSION.split(".")[0],o=Symbol.for(`opentelemetry.js.api.${a}`),l=i._globalThis;t.registerGlobal=function(e,t,r,i=!1){var n;let a=l[o]=null!==(n=l[o])&&void 0!==n?n:{version:s.VERSION};if(!i&&a[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(a.version!==s.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${a.version} for ${e} does not match previously registered API v${s.VERSION}`);return r.error(t.stack||t.message),!1}return a[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${s.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let i=null===(t=l[o])||void 0===t?void 0:t.version;if(i&&(0,n.isCompatible)(i))return null===(r=l[o])||void 0===r?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${s.VERSION}.`);let r=l[o];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let i=r(521),s=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function n(e){let t=new Set([e]),r=new Set,i=e.match(s);if(!i)return()=>!1;let n={major:+i[1],minor:+i[2],patch:+i[3],prerelease:i[4]};if(null!=n.prerelease)return function(t){return t===e};function a(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let i=e.match(s);if(!i)return a(e);let o={major:+i[1],minor:+i[2],patch:+i[3],prerelease:i[4]};return null!=o.prerelease||n.major!==o.major?a(e):0===n.major?n.minor===o.minor&&n.patch<=o.patch?(t.add(e),!0):a(e):n.minor<=o.minor?(t.add(e),!0):a(e)}}t._makeCompatibilityCheck=n,t.isCompatible=n(i.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0;let i=r(653);t.metrics=i.MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class i{}t.NoopMetric=i;class s extends i{add(e,t){}}t.NoopCounterMetric=s;class n extends i{add(e,t){}}t.NoopUpDownCounterMetric=n;class a extends i{record(e,t){}}t.NoopHistogramMetric=a;class o{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=o;class l extends o{}t.NoopObservableCounterMetric=l;class c extends o{}t.NoopObservableGaugeMetric=c;class u extends o{}t.NoopObservableUpDownCounterMetric=u,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new s,t.NOOP_HISTOGRAM_METRIC=new a,t.NOOP_UP_DOWN_COUNTER_METRIC=new n,t.NOOP_OBSERVABLE_COUNTER_METRIC=new l,t.NOOP_OBSERVABLE_GAUGE_METRIC=new c,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new u,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let i=r(102);class s{getMeter(e,t,r){return i.NOOP_METER}}t.NoopMeterProvider=s,t.NOOP_METER_PROVIDER=new s},200:function(e,t,r){var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),s=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||i(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),s(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),s=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||i(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),s(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0;let i=r(181);t.propagation=i.PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0;let i=r(997);t.trace=i.TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let i=r(476);class s{constructor(e=i.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=s},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let i=r(491),s=r(607),n=r(403),a=r(139),o=i.ContextAPI.getInstance();class l{startSpan(e,t,r=o.active()){if(null==t?void 0:t.root)return new n.NonRecordingSpan;let i=r&&(0,s.getSpanContext)(r);return"object"==typeof i&&"string"==typeof i.spanId&&"string"==typeof i.traceId&&"number"==typeof i.traceFlags&&(0,a.isSpanContextValid)(i)?new n.NonRecordingSpan(i):new n.NonRecordingSpan}startActiveSpan(e,t,r,i){let n,a,l;if(arguments.length<2)return;2==arguments.length?l=t:3==arguments.length?(n=t,l=r):(n=t,a=r,l=i);let c=null!=a?a:o.active(),u=this.startSpan(e,n,c),h=(0,s.setSpan)(c,u);return o.with(h,l,void 0,u)}}t.NoopTracer=l},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let i=r(614);class s{getTracer(e,t,r){return new i.NoopTracer}}t.NoopTracerProvider=s},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let i=new(r(614)).NoopTracer;class s{constructor(e,t,r,i){this._provider=e,this.name=t,this.version=r,this.options=i}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,i){let s=this._getTracer();return Reflect.apply(s.startActiveSpan,s,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):i}}t.ProxyTracer=s},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let i=r(125),s=new(r(124)).NoopTracerProvider;class n{getTracer(e,t,r){var s;return null!==(s=this.getDelegateTracer(e,t,r))&&void 0!==s?s:new i.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!==(e=this._delegate)&&void 0!==e?e:s}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var i;return null===(i=this._delegate)||void 0===i?void 0:i.getTracer(e,t,r)}}t.ProxyTracerProvider=n},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let i=r(780),s=r(403),n=r(491),a=(0,i.createContextKey)("OpenTelemetry Context Key SPAN");function o(e){return e.getValue(a)||void 0}function l(e,t){return e.setValue(a,t)}t.getSpan=o,t.getActiveSpan=function(){return o(n.ContextAPI.getInstance().active())},t.setSpan=l,t.deleteSpan=function(e){return e.deleteValue(a)},t.setSpanContext=function(e,t){return l(e,new s.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null===(t=o(e))||void 0===t?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let i=r(564);class s{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),s=r.indexOf("=");if(-1!==s){let n=r.slice(0,s),a=r.slice(s+1,t.length);(0,i.validateKey)(n)&&(0,i.validateValue)(a)&&e.set(n,a)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new s;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=s},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",i=`[a-z]${r}{0,255}`,s=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,n=RegExp(`^(?:${i}|${s})$`),a=/^[ -~]{0,255}[!-~]$/,o=/,|=/;t.validateKey=function(e){return n.test(e)},t.validateValue=function(e){return a.test(e)&&!o.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let i=r(325);t.createTraceState=function(e){return new i.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let i=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:i.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let i=r(476),s=r(403),n=/^([0-9a-f]{32})$/i,a=/^[0-9a-f]{16}$/i;function o(e){return n.test(e)&&e!==i.INVALID_TRACEID}function l(e){return a.test(e)&&e!==i.INVALID_SPANID}t.isValidTraceId=o,t.isValidSpanId=l,t.isSpanContextValid=function(e){return o(e.traceId)&&l(e.spanId)},t.wrapSpanContext=function(e){return new s.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},i={};function s(e){var r=i[e];if(void 0!==r)return r.exports;var n=i[e]={exports:{}},a=!0;try{t[e].call(n.exports,n,n.exports,s),a=!1}finally{a&&delete i[e]}return n.exports}s.ab="//";var n={};(()=>{Object.defineProperty(n,"__esModule",{value:!0}),n.trace=n.propagation=n.metrics=n.diag=n.context=n.INVALID_SPAN_CONTEXT=n.INVALID_TRACEID=n.INVALID_SPANID=n.isValidSpanId=n.isValidTraceId=n.isSpanContextValid=n.createTraceState=n.TraceFlags=n.SpanStatusCode=n.SpanKind=n.SamplingDecision=n.ProxyTracerProvider=n.ProxyTracer=n.defaultTextMapSetter=n.defaultTextMapGetter=n.ValueType=n.createNoopMeter=n.DiagLogLevel=n.DiagConsoleLogger=n.ROOT_CONTEXT=n.createContextKey=n.baggageEntryMetadataFromString=void 0;var e=s(369);Object.defineProperty(n,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=s(780);Object.defineProperty(n,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(n,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=s(972);Object.defineProperty(n,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var i=s(957);Object.defineProperty(n,"DiagLogLevel",{enumerable:!0,get:function(){return i.DiagLogLevel}});var a=s(102);Object.defineProperty(n,"createNoopMeter",{enumerable:!0,get:function(){return a.createNoopMeter}});var o=s(901);Object.defineProperty(n,"ValueType",{enumerable:!0,get:function(){return o.ValueType}});var l=s(194);Object.defineProperty(n,"defaultTextMapGetter",{enumerable:!0,get:function(){return l.defaultTextMapGetter}}),Object.defineProperty(n,"defaultTextMapSetter",{enumerable:!0,get:function(){return l.defaultTextMapSetter}});var c=s(125);Object.defineProperty(n,"ProxyTracer",{enumerable:!0,get:function(){return c.ProxyTracer}});var u=s(846);Object.defineProperty(n,"ProxyTracerProvider",{enumerable:!0,get:function(){return u.ProxyTracerProvider}});var h=s(996);Object.defineProperty(n,"SamplingDecision",{enumerable:!0,get:function(){return h.SamplingDecision}});var d=s(357);Object.defineProperty(n,"SpanKind",{enumerable:!0,get:function(){return d.SpanKind}});var p=s(847);Object.defineProperty(n,"SpanStatusCode",{enumerable:!0,get:function(){return p.SpanStatusCode}});var f=s(475);Object.defineProperty(n,"TraceFlags",{enumerable:!0,get:function(){return f.TraceFlags}});var g=s(98);Object.defineProperty(n,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var v=s(139);Object.defineProperty(n,"isSpanContextValid",{enumerable:!0,get:function(){return v.isSpanContextValid}}),Object.defineProperty(n,"isValidTraceId",{enumerable:!0,get:function(){return v.isValidTraceId}}),Object.defineProperty(n,"isValidSpanId",{enumerable:!0,get:function(){return v.isValidSpanId}});var m=s(476);Object.defineProperty(n,"INVALID_SPANID",{enumerable:!0,get:function(){return m.INVALID_SPANID}}),Object.defineProperty(n,"INVALID_TRACEID",{enumerable:!0,get:function(){return m.INVALID_TRACEID}}),Object.defineProperty(n,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return m.INVALID_SPAN_CONTEXT}});let b=s(67);Object.defineProperty(n,"context",{enumerable:!0,get:function(){return b.context}});let w=s(506);Object.defineProperty(n,"diag",{enumerable:!0,get:function(){return w.diag}});let y=s(886);Object.defineProperty(n,"metrics",{enumerable:!0,get:function(){return y.metrics}});let _=s(939);Object.defineProperty(n,"propagation",{enumerable:!0,get:function(){return _.propagation}});let S=s(845);Object.defineProperty(n,"trace",{enumerable:!0,get:function(){return S.trace}}),n.default={context:b.context,diag:w.diag,metrics:y.metrics,propagation:_.propagation,trace:S.trace}})(),e.exports=n})()},606:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var s={},n=t.split(i),a=(r||{}).decode||e,o=0;o<n.length;o++){var l=n[o],c=l.indexOf("=");if(!(c<0)){var u=l.substr(0,c).trim(),h=l.substr(++c,l.length).trim();'"'==h[0]&&(h=h.slice(1,-1)),void 0==s[u]&&(s[u]=function(e,t){try{return t(e)}catch(t){return e}}(h,a))}}return s},t.serialize=function(e,t,i){var n=i||{},a=n.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!s.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!s.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=n.maxAge){var c=n.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(n.domain){if(!s.test(n.domain))throw TypeError("option domain is invalid");l+="; Domain="+n.domain}if(n.path){if(!s.test(n.path))throw TypeError("option path is invalid");l+="; Path="+n.path}if(n.expires){if("function"!=typeof n.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(l+="; HttpOnly"),n.secure&&(l+="; Secure"),n.sameSite)switch("string"==typeof n.sameSite?n.sameSite.toLowerCase():n.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,i=/; */,s=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},850:(e,t,r)=>{var i;(()=>{var s={226:function(s,n){!function(a,o){"use strict";var l="function",c="undefined",u="object",h="string",d="major",p="model",f="name",g="type",v="vendor",m="version",b="architecture",w="console",y="mobile",_="tablet",S="smarttv",k="wearable",x="embedded",O="Amazon",T="Apple",C="ASUS",P="BlackBerry",E="Browser",I="Chrome",j="Firefox",R="Google",A="Huawei",N="Microsoft",L="Motorola",M="Opera",$="Samsung",D="Sharp",U="Sony",B="Xiaomi",q="Zebra",z="Facebook",V="Chromium OS",F="Mac OS",G=function(e,t){var r={};for(var i in e)t[i]&&t[i].length%2==0?r[i]=t[i].concat(e[i]):r[i]=e[i];return r},J=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},H=function(e,t){return typeof e===h&&-1!==K(t).indexOf(K(e))},K=function(e){return e.toLowerCase()},W=function(e,t){if(typeof e===h)return e=e.replace(/^\s\s*/,""),typeof t===c?e:e.substring(0,350)},X=function(e,t){for(var r,i,s,n,a,c,h=0;h<t.length&&!a;){var d=t[h],p=t[h+1];for(r=i=0;r<d.length&&!a&&d[r];)if(a=d[r++].exec(e))for(s=0;s<p.length;s++)c=a[++i],typeof(n=p[s])===u&&n.length>0?2===n.length?typeof n[1]==l?this[n[0]]=n[1].call(this,c):this[n[0]]=n[1]:3===n.length?typeof n[1]!==l||n[1].exec&&n[1].test?this[n[0]]=c?c.replace(n[1],n[2]):void 0:this[n[0]]=c?n[1].call(this,c,n[2]):void 0:4===n.length&&(this[n[0]]=c?n[3].call(this,c.replace(n[1],n[2])):void 0):this[n]=c||o;h+=2}},Y=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var i=0;i<t[r].length;i++)if(H(t[r][i],e))return"?"===r?o:r}else if(H(t[r],e))return"?"===r?o:r;return e},Z={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Q={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[m,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[m,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,m],[/opios[\/ ]+([\w\.]+)/i],[m,[f,M+" Mini"]],[/\bopr\/([\w\.]+)/i],[m,[f,M]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,m],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[m,[f,"UC"+E]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[m,[f,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[m,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[m,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[m,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[m,[f,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+E],m],[/\bfocus\/([\w\.]+)/i],[m,[f,j+" Focus"]],[/\bopt\/([\w\.]+)/i],[m,[f,M+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[m,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[m,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[m,[f,M+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[m,[f,"MIUI "+E]],[/fxios\/([-\w\.]+)/i],[m,[f,j]],[/\bqihu|(qi?ho?o?|360)browser/i],[[f,"360 "+E]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1 "+E],m],[/(comodo_dragon)\/([\w\.]+)/i],[[f,/_/g," "],m],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[f,m],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,z],m],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[f,m],[/\bgsa\/([\w\.]+) .*safari\//i],[m,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[m,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[m,[f,I+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,I+" WebView"],m],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[m,[f,"Android "+E]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,m],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[m,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[m,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[m,Y,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[f,m],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],m],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[m,[f,j+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[f,m],[/(cobalt)\/([\w\.]+)/i],[f,[m,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[b,"amd64"]],[/(ia32(?=;))/i],[[b,K]],[/((?:i[346]|x)86)[;\)]/i],[[b,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[b,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[b,"armhf"]],[/windows (ce|mobile); ppc;/i],[[b,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[b,/ower/,"",K]],[/(sun4\w)[;\)]/i],[[b,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[b,K]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[p,[v,$],[g,_]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[p,[v,$],[g,y]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[p,[v,T],[g,y]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[p,[v,T],[g,_]],[/(macintosh);/i],[p,[v,T]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[p,[v,D],[g,y]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[p,[v,A],[g,_]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[p,[v,A],[g,y]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[p,/_/g," "],[v,B],[g,y]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[p,/_/g," "],[v,B],[g,_]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[p,[v,"OPPO"],[g,y]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[p,[v,"Vivo"],[g,y]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[p,[v,"Realme"],[g,y]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[p,[v,L],[g,y]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[p,[v,L],[g,_]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[p,[v,"LG"],[g,_]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[p,[v,"LG"],[g,y]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[p,[v,"Lenovo"],[g,_]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[p,/_/g," "],[v,"Nokia"],[g,y]],[/(pixel c)\b/i],[p,[v,R],[g,_]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[p,[v,R],[g,y]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[p,[v,U],[g,y]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[p,"Xperia Tablet"],[v,U],[g,_]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[p,[v,"OnePlus"],[g,y]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[p,[v,O],[g,_]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[p,/(.+)/g,"Fire Phone $1"],[v,O],[g,y]],[/(playbook);[-\w\),; ]+(rim)/i],[p,v,[g,_]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[p,[v,P],[g,y]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[p,[v,C],[g,_]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[p,[v,C],[g,y]],[/(nexus 9)/i],[p,[v,"HTC"],[g,_]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[v,[p,/_/g," "],[g,y]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[p,[v,"Acer"],[g,_]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[p,[v,"Meizu"],[g,y]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[v,p,[g,y]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[v,p,[g,_]],[/(surface duo)/i],[p,[v,N],[g,_]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[p,[v,"Fairphone"],[g,y]],[/(u304aa)/i],[p,[v,"AT&T"],[g,y]],[/\bsie-(\w*)/i],[p,[v,"Siemens"],[g,y]],[/\b(rct\w+) b/i],[p,[v,"RCA"],[g,_]],[/\b(venue[\d ]{2,7}) b/i],[p,[v,"Dell"],[g,_]],[/\b(q(?:mv|ta)\w+) b/i],[p,[v,"Verizon"],[g,_]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[p,[v,"Barnes & Noble"],[g,_]],[/\b(tm\d{3}\w+) b/i],[p,[v,"NuVision"],[g,_]],[/\b(k88) b/i],[p,[v,"ZTE"],[g,_]],[/\b(nx\d{3}j) b/i],[p,[v,"ZTE"],[g,y]],[/\b(gen\d{3}) b.+49h/i],[p,[v,"Swiss"],[g,y]],[/\b(zur\d{3}) b/i],[p,[v,"Swiss"],[g,_]],[/\b((zeki)?tb.*\b) b/i],[p,[v,"Zeki"],[g,_]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[v,"Dragon Touch"],p,[g,_]],[/\b(ns-?\w{0,9}) b/i],[p,[v,"Insignia"],[g,_]],[/\b((nxa|next)-?\w{0,9}) b/i],[p,[v,"NextBook"],[g,_]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[v,"Voice"],p,[g,y]],[/\b(lvtel\-)?(v1[12]) b/i],[[v,"LvTel"],p,[g,y]],[/\b(ph-1) /i],[p,[v,"Essential"],[g,y]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[p,[v,"Envizen"],[g,_]],[/\b(trio[-\w\. ]+) b/i],[p,[v,"MachSpeed"],[g,_]],[/\btu_(1491) b/i],[p,[v,"Rotor"],[g,_]],[/(shield[\w ]+) b/i],[p,[v,"Nvidia"],[g,_]],[/(sprint) (\w+)/i],[v,p,[g,y]],[/(kin\.[onetw]{3})/i],[[p,/\./g," "],[v,N],[g,y]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[p,[v,q],[g,_]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[p,[v,q],[g,y]],[/smart-tv.+(samsung)/i],[v,[g,S]],[/hbbtv.+maple;(\d+)/i],[[p,/^/,"SmartTV"],[v,$],[g,S]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[v,"LG"],[g,S]],[/(apple) ?tv/i],[v,[p,T+" TV"],[g,S]],[/crkey/i],[[p,I+"cast"],[v,R],[g,S]],[/droid.+aft(\w)( bui|\))/i],[p,[v,O],[g,S]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[p,[v,D],[g,S]],[/(bravia[\w ]+)( bui|\))/i],[p,[v,U],[g,S]],[/(mitv-\w{5}) bui/i],[p,[v,B],[g,S]],[/Hbbtv.*(technisat) (.*);/i],[v,p,[g,S]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[v,W],[p,W],[g,S]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,S]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[v,p,[g,w]],[/droid.+; (shield) bui/i],[p,[v,"Nvidia"],[g,w]],[/(playstation [345portablevi]+)/i],[p,[v,U],[g,w]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[p,[v,N],[g,w]],[/((pebble))app/i],[v,p,[g,k]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[p,[v,T],[g,k]],[/droid.+; (glass) \d/i],[p,[v,R],[g,k]],[/droid.+; (wt63?0{2,3})\)/i],[p,[v,q],[g,k]],[/(quest( 2| pro)?)/i],[p,[v,z],[g,k]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[v,[g,x]],[/(aeobc)\b/i],[p,[v,O],[g,x]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[p,[g,y]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[p,[g,_]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,_]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,y]],[/(android[-\w\. ]{0,9});.+buil/i],[p,[v,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[m,[f,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[m,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,m],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[m,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,m],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[f,[m,Y,Z]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,"Windows"],[m,Y,Z]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[m,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,F],[m,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[m,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,m],[/\(bb(10);/i],[m,[f,P]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[m,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[m,[f,j+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[m,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[m,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[m,[f,I+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,V],m],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,m],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],m],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,m]]},ee=function(e,t){if(typeof e===u&&(t=e,e=o),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof a!==c&&a.navigator?a.navigator:o,i=e||(r&&r.userAgent?r.userAgent:""),s=r&&r.userAgentData?r.userAgentData:o,n=t?G(Q,t):Q,w=r&&r.userAgent==i;return this.getBrowser=function(){var e,t={};return t[f]=o,t[m]=o,X.call(t,i,n.browser),t[d]=typeof(e=t[m])===h?e.replace(/[^\d\.]/g,"").split(".")[0]:o,w&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[f]="Brave"),t},this.getCPU=function(){var e={};return e[b]=o,X.call(e,i,n.cpu),e},this.getDevice=function(){var e={};return e[v]=o,e[p]=o,e[g]=o,X.call(e,i,n.device),w&&!e[g]&&s&&s.mobile&&(e[g]=y),w&&"Macintosh"==e[p]&&r&&typeof r.standalone!==c&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[p]="iPad",e[g]=_),e},this.getEngine=function(){var e={};return e[f]=o,e[m]=o,X.call(e,i,n.engine),e},this.getOS=function(){var e={};return e[f]=o,e[m]=o,X.call(e,i,n.os),w&&!e[f]&&s&&"Unknown"!=s.platform&&(e[f]=s.platform.replace(/chrome os/i,V).replace(/macos/i,F)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return i},this.setUA=function(e){return i=typeof e===h&&e.length>350?W(e,350):e,this},this.setUA(i),this};ee.VERSION="1.0.35",ee.BROWSER=J([f,m,d]),ee.CPU=J([b]),ee.DEVICE=J([p,v,g,w,y,S,_,k,x]),ee.ENGINE=ee.OS=J([f,m]),typeof n!==c?(s.exports&&(n=s.exports=ee),n.UAParser=ee):r.amdO?void 0!==(i=(function(){return ee}).call(t,r,t,e))&&(e.exports=i):typeof a!==c&&(a.UAParser=ee);var et=typeof a!==c&&(a.jQuery||a.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},n={};function a(e){var t=n[e];if(void 0!==t)return t.exports;var r=n[e]={exports:{}},i=!0;try{s[e].call(r.exports,r,r.exports,a),i=!1}finally{i&&delete n[e]}return r.exports}a.ab="//";var o=a(226);e.exports=o})()},829:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return a},withRequest:function(){return n}});let i=new(r(67)).AsyncLocalStorage;function s(e,t){let r=t.header(e,"next-test-proxy-port");if(r)return{url:t.url(e),proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function n(e,t,r){let n=s(e,t);return n?i.run(n,r):r()}function a(e,t){return i.getStore()||(e&&t?s(e,t):void 0)}},806:(e,t,r)=>{"use strict";var i=r(195).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return o},interceptFetch:function(){return l},reader:function(){return n}});let s=r(829),n={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function a(e,t){let{url:r,method:s,headers:n,body:a,cache:o,credentials:l,integrity:c,mode:u,redirect:h,referrer:d,referrerPolicy:p}=t;return{testData:e,api:"fetch",request:{url:r,method:s,headers:[...Array.from(n),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:a?i.from(await t.arrayBuffer()).toString("base64"):null,cache:o,credentials:l,integrity:c,mode:u,redirect:h,referrer:d,referrerPolicy:p}}}async function o(e,t){let r=(0,s.getTestReqInfo)(t,n);if(!r)return e(t);let{testData:o,proxyPort:l}=r,c=await a(o,t),u=await e(`http://localhost:${l}`,{method:"POST",body:JSON.stringify(c),next:{internal:!0}});if(!u.ok)throw Error(`Proxy request failed: ${u.status}`);let h=await u.json(),{api:d}=h;switch(d){case"continue":return e(t);case"abort":case"unhandled":throw Error(`Proxy request aborted [${t.method} ${t.url}]`)}return function(e){let{status:t,headers:r,body:s}=e.response;return new Response(s?i.from(s,"base64"):null,{status:t,headers:new Headers(r)})}(h)}function l(e){return r.g.fetch=function(t,r){var i;return(null==r?void 0:null==(i=r.next)?void 0:i.internal)?e(t,r):o(e,new Request(t,r))},()=>{r.g.fetch=e}}},424:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return n},wrapRequestHandler:function(){return a}});let i=r(829),s=r(806);function n(){return(0,s.interceptFetch)(r.g.fetch)}function a(e){return(t,r)=>(0,i.withRequest)(t,s.reader,()=>e(t,r))}},393:e=>{"use strict";var t={decodeValues:!0,map:!1,silent:!1};function r(e){return"string"==typeof e&&!!e.trim()}function i(e,i){var s,n,a,o,l=e.split(";").filter(r),c=(s=l.shift(),n="",a="",(o=s.split("=")).length>1?(n=o.shift(),a=o.join("=")):a=s,{name:n,value:a}),u=c.name,h=c.value;i=i?Object.assign({},t,i):t;try{h=i.decodeValues?decodeURIComponent(h):h}catch(e){console.error("set-cookie-parser encountered an error while decoding a cookie with value '"+h+"'. Set options.decodeValues to false to disable this feature.",e)}var d={name:u,value:h};return l.forEach(function(e){var t=e.split("="),r=t.shift().trimLeft().toLowerCase(),i=t.join("=");"expires"===r?d.expires=new Date(i):"max-age"===r?d.maxAge=parseInt(i,10):"secure"===r?d.secure=!0:"httponly"===r?d.httpOnly=!0:"samesite"===r?d.sameSite=i:"partitioned"===r?d.partitioned=!0:d[r]=i}),d}function s(e,s){if(s=s?Object.assign({},t,s):t,!e)return s.map?{}:[];if(e.headers){if("function"==typeof e.headers.getSetCookie)e=e.headers.getSetCookie();else if(e.headers["set-cookie"])e=e.headers["set-cookie"];else{var n=e.headers[Object.keys(e.headers).find(function(e){return"set-cookie"===e.toLowerCase()})];n||!e.headers.cookie||s.silent||console.warn("Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning."),e=n}}return(Array.isArray(e)||(e=[e]),s.map)?e.filter(r).reduce(function(e,t){var r=i(t,s);return e[r.name]=r,e},{}):e.filter(r).map(function(e){return i(e,s)})}e.exports=s,e.exports.parse=s,e.exports.parseString=i,e.exports.splitCookiesString=function(e){if(Array.isArray(e))return e;if("string"!=typeof e)return[];var t,r,i,s,n,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,n=!1;l();)if(","===(r=e.charAt(o))){for(i=o,o+=1,l(),s=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(n=!0,o=s,a.push(e.substring(t,i)),t=o):o=i+1}else o+=1;(!n||o>=e.length)&&a.push(e.substring(t,e.length))}return a}},409:e=>{"use strict";e.exports=function(){throw Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}},602:(e,t,r)=>{"use strict";let i,s;r.r(t),r.d(t,{BrowserCookieAuthStorageAdapter:()=>tZ,CookieAuthStorageAdapter:()=>tY,DEFAULT_COOKIE_OPTIONS:()=>tW,createSupabaseClient:()=>tQ,isBrowser:()=>tK,parseCookies:()=>t0,parseSupabaseCookie:()=>tJ,serializeCookie:()=>t1,stringifySupabaseSession:()=>tH}),new TextEncoder;let n=new TextDecoder,a=e=>{let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r},o=e=>{let t=e;t instanceof Uint8Array&&(t=n.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return a(t)}catch(e){throw TypeError("The input to be decoded is not correctly encoded.")}},l=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(r.bind(r,23)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)};class c extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class u extends c{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class h extends c{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class d extends c{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(K||(K={}));class p{constructor(e,{headers:t={},customFetch:r,region:i=K.Any}={}){this.url=e,this.headers=t,this.region=i,this.fetch=l(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r,i,s,n,a;return i=this,s=void 0,n=void 0,a=function*(){try{let i;let{headers:s,method:n,body:a}=t,o={},{region:l}=t;l||(l=this.region),l&&"any"!==l&&(o["x-region"]=l),a&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&("undefined"!=typeof Blob&&a instanceof Blob||a instanceof ArrayBuffer?(o["Content-Type"]="application/octet-stream",i=a):"string"==typeof a?(o["Content-Type"]="text/plain",i=a):"undefined"!=typeof FormData&&a instanceof FormData?i=a:(o["Content-Type"]="application/json",i=JSON.stringify(a)));let c=yield this.fetch(`${this.url}/${e}`,{method:n||"POST",headers:Object.assign(Object.assign(Object.assign({},o),this.headers),s),body:i}).catch(e=>{throw new u(e)}),p=c.headers.get("x-relay-error");if(p&&"true"===p)throw new h(c);if(!c.ok)throw new d(c);let f=(null!==(r=c.headers.get("Content-Type"))&&void 0!==r?r:"text/plain").split(";")[0].trim();return{data:"application/json"===f?yield c.json():"application/octet-stream"===f?yield c.blob():"text/event-stream"===f?c:"multipart/form-data"===f?yield c.formData():yield c.text(),error:null}}catch(e){return{data:null,error:e}}},new(n||(n=Promise))(function(e,t){function r(e){try{l(a.next(e))}catch(e){t(e)}}function o(e){try{l(a.throw(e))}catch(e){t(e)}}function l(t){var i;t.done?e(t.value):((i=t.value)instanceof n?i:new n(function(e){e(i)})).then(r,o)}l((a=a.apply(i,s||[])).next())})}}let{PostgrestClient:f,PostgrestQueryBuilder:g,PostgrestFilterBuilder:v,PostgrestTransformBuilder:m,PostgrestBuilder:b,PostgrestError:w}=r(124),y={"X-Client-Info":"realtime-js/2.11.2"};!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(W||(W={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(X||(X={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(Y||(Y={})),(Z||(Z={})).websocket="websocket",function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(Q||(Q={}));class _{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){let i=t.getUint8(1),s=t.getUint8(2),n=this.HEADER_LENGTH+2,a=r.decode(e.slice(n,n+i));n+=i;let o=r.decode(e.slice(n,n+s));return n+=s,{ref:null,topic:a,event:o,payload:JSON.parse(r.decode(e.slice(n,e.byteLength)))}}}class S{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(ee||(ee={}));let k=(e,t,r={})=>{var i;let s=null!==(i=r.skipTypes)&&void 0!==i?i:[];return Object.keys(t).reduce((r,i)=>(r[i]=x(i,e,t,s),r),{})},x=(e,t,r,i)=>{let s=t.find(t=>t.name===e),n=null==s?void 0:s.type,a=r[e];return n&&!i.includes(n)?O(n,a):T(a)},O=(e,t)=>{if("_"===e.charAt(0))return I(t,e.slice(1,e.length));switch(e){case ee.bool:return C(t);case ee.float4:case ee.float8:case ee.int2:case ee.int4:case ee.int8:case ee.numeric:case ee.oid:return P(t);case ee.json:case ee.jsonb:return E(t);case ee.timestamp:return j(t);case ee.abstime:case ee.date:case ee.daterange:case ee.int4range:case ee.int8range:case ee.money:case ee.reltime:case ee.text:case ee.time:case ee.timestamptz:case ee.timetz:case ee.tsrange:case ee.tstzrange:default:return T(t)}},T=e=>e,C=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},P=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},E=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},I=(e,t)=>{if("string"!=typeof e)return e;let r=e.length-1,i=e[r];if("{"===e[0]&&"}"===i){let i;let s=e.slice(1,r);try{i=JSON.parse("["+s+"]")}catch(e){i=s?s.split(","):[]}return i.map(e=>O(t,e))}return e},j=e=>"string"==typeof e?e.replace(" ","T"):e,R=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};class A{constructor(e,t,r={},i=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null===(r=this.receivedResp)||void 0===r?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(et||(et={}));class N{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{let{onJoin:t,onLeave:r,onSync:i}=this.caller;this.joinRef=this.channel._joinRef(),this.state=N.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=N.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],i()}),this.channel._on(r.diff,{},e=>{let{onJoin:t,onLeave:r,onSync:i}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=N.syncDiff(this.state,e,t,r),i())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,i){let s=this.cloneDeep(e),n=this.transformState(t),a={},o={};return this.map(s,(e,t)=>{n[e]||(o[e]=t)}),this.map(n,(e,t)=>{let r=s[e];if(r){let i=t.map(e=>e.presence_ref),s=r.map(e=>e.presence_ref),n=t.filter(e=>0>s.indexOf(e.presence_ref)),l=r.filter(e=>0>i.indexOf(e.presence_ref));n.length>0&&(a[e]=n),l.length>0&&(o[e]=l)}else a[e]=t}),this.syncDiff(s,{joins:a,leaves:o},r,i)}static syncDiff(e,t,r,i){let{joins:s,leaves:n}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),i||(i=()=>{}),this.map(s,(t,i)=>{var s;let n=null!==(s=e[t])&&void 0!==s?s:[];if(e[t]=this.cloneDeep(i),n.length>0){let r=e[t].map(e=>e.presence_ref),i=n.filter(e=>0>r.indexOf(e.presence_ref));e[t].unshift(...i)}r(t,n,i)}),this.map(n,(t,r)=>{let s=e[t];if(!s)return;let n=r.map(e=>e.presence_ref);s=s.filter(e=>0>n.indexOf(e.presence_ref)),e[t]=s,i(t,s,r),0===s.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,r)=>{let i=e[r];return"metas"in i?t[r]=i.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[r]=i,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(er||(er={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}(ei||(ei={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(es||(es={}));class L{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=X.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new A(this,Y.join,this.params,this.timeout),this.rejoinTimer=new S(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=X.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=X.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=X.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=X.errored,this.rejoinTimer.scheduleTimeout())}),this._on(Y.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new N(this),this.broadcastEndpointURL=R(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,i;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{let{config:{broadcast:s,presence:n,private:a}}=this.params;this._onError(t=>null==e?void 0:e(es.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(es.CLOSED));let o={},l={broadcast:s,presence:n,postgres_changes:null!==(i=null===(r=this.bindings.postgres_changes)||void 0===r?void 0:r.map(e=>e.filter))&&void 0!==i?i:[],private:a};this.socket.accessTokenValue&&(o.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},o)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var r;if(this.socket.setAuth(),void 0===t){null==e||e(es.SUBSCRIBED);return}{let i=this.bindings.postgres_changes,s=null!==(r=null==i?void 0:i.length)&&void 0!==r?r:0,n=[];for(let r=0;r<s;r++){let s=i[r],{filter:{event:a,schema:o,table:l,filter:c}}=s,u=t&&t[r];if(u&&u.event===a&&u.schema===o&&u.table===l&&u.filter===c)n.push(Object.assign(Object.assign({},s),{id:u.id}));else{this.unsubscribe(),null==e||e(es.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=n,e&&e(es.SUBSCRIBED);return}}).receive("error",t=>{null==e||e(es.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(es.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this._on(e,t,r)}async send(e,t={}){var r,i;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var i,s,n;let a=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(n=null===(s=null===(i=this.params)||void 0===i?void 0:i.config)||void 0===s?void 0:s.broadcast)||void 0===n?void 0:n.ack)||r("ok"),a.receive("ok",()=>r("ok")),a.receive("error",()=>r("error")),a.receive("timeout",()=>r("timed out"))});{let{event:s,payload:n}=e,a={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:s,payload:n,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,a,null!==(r=t.timeout)&&void 0!==r?r:this.timeout);return await (null===(i=e.body)||void 0===i?void 0:i.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=X.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(Y.close,"leave",this._joinRef())};return this.rejoinTimer.reset(),this.joinPush.destroy(),new Promise(r=>{let i=new A(this,Y.leave,{},e);i.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),i.send(),this._canPush()||i.trigger("ok",{})})}async _fetchWithTimeout(e,t,r){let i=new AbortController,s=setTimeout(()=>i.abort(),r),n=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:i.signal}));return clearTimeout(s),n}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let i=new A(this,e,t,r);return this._canPush()?i.send():(i.startTimeout(),this.pushBuffer.push(i)),i}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var i,s;let n=e.toLocaleLowerCase(),{close:a,error:o,leave:l,join:c}=Y;if(r&&[a,o,l,c].indexOf(n)>=0&&r!==this._joinRef())return;let u=this._onMessage(n,t,r);if(t&&!u)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(n)?null===(i=this.bindings.postgres_changes)||void 0===i||i.filter(e=>{var t,r,i;return(null===(t=e.filter)||void 0===t?void 0:t.event)==="*"||(null===(i=null===(r=e.filter)||void 0===r?void 0:r.event)||void 0===i?void 0:i.toLocaleLowerCase())===n}).map(e=>e.callback(u,r)):null===(s=this.bindings[n])||void 0===s||s.filter(e=>{var r,i,s,a,o,l;if(!["broadcast","presence","postgres_changes"].includes(n))return e.type.toLocaleLowerCase()===n;if("id"in e){let n=e.id,a=null===(r=e.filter)||void 0===r?void 0:r.event;return n&&(null===(i=t.ids)||void 0===i?void 0:i.includes(n))&&("*"===a||(null==a?void 0:a.toLocaleLowerCase())===(null===(s=t.data)||void 0===s?void 0:s.type.toLocaleLowerCase()))}{let r=null===(o=null===(a=null==e?void 0:e.filter)||void 0===a?void 0:a.event)||void 0===o?void 0:o.toLocaleLowerCase();return"*"===r||r===(null===(l=null==t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof u&&"ids"in u){let e=u.data,{schema:t,table:r,commit_timestamp:i,type:s,errors:n}=e;u=Object.assign(Object.assign({},{schema:t,table:r,commit_timestamp:i,eventType:s,new:{},old:{},errors:n}),this._getPayloadRecords(e))}e.callback(u,r)})}_isClosed(){return this.state===X.closed}_isJoined(){return this.state===X.joined}_isJoining(){return this.state===X.joining}_isLeaving(){return this.state===X.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){let i=e.toLocaleLowerCase(),s={type:i,filter:t,callback:r};return this.bindings[i]?this.bindings[i].push(s):this.bindings[i]=[s],this}_off(e,t){let r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(e=>{var i;return!((null===(i=e.type)||void 0===i?void 0:i.toLocaleLowerCase())===r&&L.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(Y.close,{},e)}_onError(e){this._on(Y.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=X.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=k(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=k(e.columns,e.old_record)),t}}let M=()=>{},$="undefined"!=typeof WebSocket,D=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class U{constructor(e,t){var i;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers=y,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=3e4,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.ref=0,this.logger=M,this.conn=null,this.sendBuffer=[],this.serializer=new _,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(r.bind(r,23)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)},this.endPoint=`${e}/${Z.websocket}`,this.httpEndpoint=R(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);let s=null===(i=null==t?void 0:t.params)||void 0===i?void 0:i.apikey;if(s&&(this.accessTokenValue=s,this.apiKey=s),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new S(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport){this.conn=new this.transport(this.endpointURL(),void 0,{headers:this.headers});return}if($){this.conn=new WebSocket(this.endpointURL()),this.setupConnection();return}this.conn=new B(this.endpointURL(),void 0,{close:()=>{this.conn=null}}),Promise.resolve().then(r.t.bind(r,409,23)).then(({default:e})=>{this.conn=new e(this.endpointURL(),void 0,{headers:this.headers}),this.setupConnection()})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset())}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case W.connecting:return Q.Connecting;case W.open:return Q.Open;case W.closing:return Q.Closing;default:return Q.Closed}}isConnected(){return this.connectionState()===Q.Open}channel(e,t={config:{}}){let r=new L(`realtime:${e}`,t,this);return this.channels.push(r),r}push(e){let{topic:t,event:r,payload:i,ref:s}=e,n=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push",`${t} ${r} (${s})`,i),this.isConnected()?n():this.sendBuffer.push(n)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;if(t){let e=null;try{e=JSON.parse(atob(t.split(".")[1]))}catch(e){}if(e&&e.exp&&!(Math.floor(Date.now()/1e3)-e.exp<0))return this.log("auth",`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${e.exp}`),Promise.reject(`InvalidJWTToken: Invalid value for JWT claim "exp" with value ${e.exp}`);this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t}),e.joinedOnce&&e._isJoined()&&e._push(Y.access_token,{access_token:t})})}}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.setAuth()}}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t._joinRef()!==e._joinRef())}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:i,ref:s}=e;s&&s===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${t} ${r} ${s&&"("+s+")"||""}`,i),this.channels.filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,i,s)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}async _onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach(e=>e())}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(Y.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let r=e.match(/\?/)?"&":"?",i=new URLSearchParams(t);return`${e}${r}${i}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([D],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class B{constructor(e,t,r){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=W.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=r.close}}class q extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function z(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class V extends q{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class F extends q{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}let G=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(r.bind(r,23)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)},J=()=>(function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(r.bind(r,23))).Response:Response}),H=e=>{if(Array.isArray(e))return e.map(e=>H(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,r])=>{t[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=H(r)}),t};var K,W,X,Y,Z,Q,ee,et,er,ei,es,en=function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};let ea=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),eo=(e,t,r)=>en(void 0,void 0,void 0,function*(){e instanceof(yield J())&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{t(new V(ea(r),e.status||500))}).catch(e=>{t(new F(ea(e),e))}):t(new F(ea(e),e))}),el=(e,t,r,i)=>{let s={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?s:(s.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),i&&(s.body=JSON.stringify(i)),Object.assign(Object.assign({},s),r))};function ec(e,t,r,i,s,n){return en(this,void 0,void 0,function*(){return new Promise((a,o)=>{e(r,el(t,i,s,n)).then(e=>{if(!e.ok)throw e;return(null==i?void 0:i.noResolveJson)?e:e.json()}).then(e=>a(e)).catch(e=>eo(e,o,i))})})}function eu(e,t,r,i){return en(this,void 0,void 0,function*(){return ec(e,"GET",t,r,i)})}function eh(e,t,r,i,s){return en(this,void 0,void 0,function*(){return ec(e,"POST",t,i,s,r)})}function ed(e,t,r,i,s){return en(this,void 0,void 0,function*(){return ec(e,"DELETE",t,i,s,r)})}var ep=r(195).Buffer,ef=function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};let eg={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},ev={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class em{constructor(e,t={},r,i){this.url=e,this.headers=t,this.bucketId=r,this.fetch=G(i)}uploadOrUpdate(e,t,r,i){return ef(this,void 0,void 0,function*(){try{let s;let n=Object.assign(Object.assign({},ev),i),a=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(n.upsert)}),o=n.metadata;"undefined"!=typeof Blob&&r instanceof Blob?((s=new FormData).append("cacheControl",n.cacheControl),o&&s.append("metadata",this.encodeMetadata(o)),s.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?((s=r).append("cacheControl",n.cacheControl),o&&s.append("metadata",this.encodeMetadata(o))):(s=r,a["cache-control"]=`max-age=${n.cacheControl}`,a["content-type"]=n.contentType,o&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(o)))),(null==i?void 0:i.headers)&&(a=Object.assign(Object.assign({},a),i.headers));let l=this._removeEmptyFolders(t),c=this._getFinalPath(l),u=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:e,body:s,headers:a},(null==n?void 0:n.duplex)?{duplex:n.duplex}:{})),h=yield u.json();if(u.ok)return{data:{path:l,id:h.Id,fullPath:h.Key},error:null};return{data:null,error:h}}catch(e){if(z(e))return{data:null,error:e};throw e}})}upload(e,t,r){return ef(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,i){return ef(this,void 0,void 0,function*(){let s=this._removeEmptyFolders(e),n=this._getFinalPath(s),a=new URL(this.url+`/object/upload/sign/${n}`);a.searchParams.set("token",t);try{let e;let t=Object.assign({upsert:ev.upsert},i),n=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r).append("cacheControl",t.cacheControl):(e=r,n["cache-control"]=`max-age=${t.cacheControl}`,n["content-type"]=t.contentType);let o=yield this.fetch(a.toString(),{method:"PUT",body:e,headers:n}),l=yield o.json();if(o.ok)return{data:{path:s,fullPath:l.Key},error:null};return{data:null,error:l}}catch(e){if(z(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return ef(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),i=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(i["x-upsert"]="true");let s=yield eh(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:i}),n=new URL(this.url+s.url),a=n.searchParams.get("token");if(!a)throw new q("No token returned by API");return{data:{signedUrl:n.toString(),path:e,token:a},error:null}}catch(e){if(z(e))return{data:null,error:e};throw e}})}update(e,t,r){return ef(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return ef(this,void 0,void 0,function*(){try{return{data:yield eh(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(e){if(z(e))return{data:null,error:e};throw e}})}copy(e,t,r){return ef(this,void 0,void 0,function*(){try{return{data:{path:(yield eh(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if(z(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,r){return ef(this,void 0,void 0,function*(){try{let i=this._getFinalPath(e),s=yield eh(this.fetch,`${this.url}/object/sign/${i}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers}),n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:s={signedUrl:encodeURI(`${this.url}${s.signedURL}${n}`)},error:null}}catch(e){if(z(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,r){return ef(this,void 0,void 0,function*(){try{let i=yield eh(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),s=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:i.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${s}`):null})),error:null}}catch(e){if(z(e))return{data:null,error:e};throw e}})}download(e,t){return ef(this,void 0,void 0,function*(){let r=void 0!==(null==t?void 0:t.transform),i=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),s=i?`?${i}`:"";try{let t=this._getFinalPath(e),i=yield eu(this.fetch,`${this.url}/${r?"render/image/authenticated":"object"}/${t}${s}`,{headers:this.headers,noResolveJson:!0});return{data:yield i.blob(),error:null}}catch(e){if(z(e))return{data:null,error:e};throw e}})}info(e){return ef(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield eu(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:H(e),error:null}}catch(e){if(z(e))return{data:null,error:e};throw e}})}exists(e){return ef(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield function(e,t,r,i){return en(this,void 0,void 0,function*(){return ec(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),void 0)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if(z(e)&&e instanceof F){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let r=this._getFinalPath(e),i=[],s=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==s&&i.push(s);let n=void 0!==(null==t?void 0:t.transform),a=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==a&&i.push(a);let o=i.join("&");return""!==o&&(o=`?${o}`),{data:{publicUrl:encodeURI(`${this.url}/${n?"render/image":"object"}/public/${r}${o}`)}}}remove(e){return ef(this,void 0,void 0,function*(){try{return{data:yield ed(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if(z(e))return{data:null,error:e};throw e}})}list(e,t,r){return ef(this,void 0,void 0,function*(){try{let i=Object.assign(Object.assign(Object.assign({},eg),t),{prefix:e||""});return{data:yield eh(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},r),error:null}}catch(e){if(z(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return void 0!==ep?ep.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}let eb={"X-Client-Info":"storage-js/2.7.1"};var ew=function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})};class ey{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},eb),t),this.fetch=G(r)}listBuckets(){return ew(this,void 0,void 0,function*(){try{return{data:yield eu(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(z(e))return{data:null,error:e};throw e}})}getBucket(e){return ew(this,void 0,void 0,function*(){try{return{data:yield eu(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if(z(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return ew(this,void 0,void 0,function*(){try{return{data:yield eh(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(z(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return ew(this,void 0,void 0,function*(){try{return{data:yield function(e,t,r,i,s){return en(this,void 0,void 0,function*(){return ec(e,"PUT",t,i,void 0,r)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(z(e))return{data:null,error:e};throw e}})}emptyBucket(e){return ew(this,void 0,void 0,function*(){try{return{data:yield eh(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(z(e))return{data:null,error:e};throw e}})}deleteBucket(e){return ew(this,void 0,void 0,function*(){try{return{data:yield ed(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if(z(e))return{data:null,error:e};throw e}})}}class e_ extends ey{constructor(e,t={},r){super(e,t,r)}from(e){return new em(this.url,this.headers,e,this.fetch)}}let eS="";"undefined"!=typeof Deno?eS="deno":"undefined"!=typeof document?eS="web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?eS="react-native":eS="node";let ek={headers:{"X-Client-Info":`supabase-js-${eS}/2.49.8`}},ex={schema:"public"},eO={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},eT={};var eC=r(23);let eP=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=eC.default:t=fetch,(...e)=>t(...e)},eE=()=>"undefined"==typeof Headers?eC.Headers:Headers,eI=(e,t,r)=>{let i=eP(r),s=eE();return(r,n)=>(function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})})(void 0,void 0,void 0,function*(){var a;let o=null!==(a=yield t())&&void 0!==a?a:e,l=new s(null==n?void 0:n.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${o}`),i(r,Object.assign(Object.assign({},n),{headers:l}))})},ej="2.69.1",eR={"X-Client-Info":`gotrue-js/${ej}`},eA="X-Supabase-Api-Version",eN={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},eL=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class eM extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function e$(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class eD extends eM{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}class eU extends eM{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class eB extends eM{constructor(e,t,r,i){super(e,r,i),this.name=t,this.status=r}}class eq extends eB{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class ez extends eB{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class eV extends eB{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class eF extends eB{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class eG extends eB{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class eJ extends eB{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function eH(e){return e$(e)&&"AuthRetryableFetchError"===e.name}class eK extends eB{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class eW extends eB{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}let eX="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),eY=" 	\n\r=".split(""),eZ=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<eY.length;t+=1)e[eY[t].charCodeAt(0)]=-2;for(let t=0;t<eX.length;t+=1)e[eX[t].charCodeAt(0)]=t;return e})();function eQ(e,t,r){let i=eZ[e];if(i>-1)for(t.queue=t.queue<<6|i,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===i)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function e0(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},i={utf8seq:0,codepoint:0},s={queue:0,queuedBits:0},n=e=>{!function(e,t,r){if(0===t.utf8seq){if(e<=127){r(e);return}for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,i,r)};for(let t=0;t<e.length;t+=1)eQ(e.charCodeAt(t),s,n);return t.join("")}let e1=()=>"undefined"!=typeof window&&"undefined"!=typeof document,e2={tested:!1,writable:!1},e3=()=>{if(!e1())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(e2.tested)return e2.writable;let e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),e2.tested=!0,e2.writable=!0}catch(e){e2.tested=!0,e2.writable=!1}return e2.writable},e6=e=>{let t;return e?t=e:"undefined"==typeof fetch?t=(...e)=>Promise.resolve().then(r.bind(r,23)).then(({default:t})=>t(...e)):t=fetch,(...e)=>t(...e)},e4=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,e9=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},e5=async(e,t)=>{let r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch(e){return r}},e8=async(e,t)=>{await e.removeItem(t)};class e7{constructor(){this.promise=new e7.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function te(e){let t=e.split(".");if(3!==t.length)throw new eW("Invalid JWT structure");for(let e=0;e<t.length;e++)if(!eL.test(t[e]))throw new eW("JWT not in base64url format");return{header:JSON.parse(e0(t[0])),payload:JSON.parse(e0(t[1])),signature:function(e){let t=[],r={queue:0,queuedBits:0},i=e=>{t.push(e)};for(let t=0;t<e.length;t+=1)eQ(e.charCodeAt(t),r,i);return new Uint8Array(t)}(t[2]),raw:{header:t[0],payload:t[1]}}}async function tt(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function tr(e){return("0"+e.toString(16)).substr(-2)}async function ti(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>String.fromCharCode(e)).join("")}async function ts(e){return"undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder?btoa(await ti(e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""):(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e)}async function tn(e,t,r=!1){let i=function(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length,r="";for(let i=0;i<56;i++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,tr).join("")}(),s=i;r&&(s+="/PASSWORD_RECOVERY"),await e9(e,`${t}-code-verifier`,s);let n=await ts(i),a=i===n?"plain":"s256";return[n,a]}e7.promiseConstructor=Promise;let ta=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;var to=function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,i=Object.getOwnPropertySymbols(e);s<i.length;s++)0>t.indexOf(i[s])&&Object.prototype.propertyIsEnumerable.call(e,i[s])&&(r[i[s]]=e[i[s]]);return r};let tl=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),tc=[502,503,504];async function tu(e){var t;let r,i;if(!e4(e))throw new eJ(tl(e),0);if(tc.includes(e.status))throw new eJ(tl(e),e.status);try{r=await e.json()}catch(e){throw new eU(tl(e),e)}let s=function(e){let t=e.headers.get(eA);if(!t||!t.match(ta))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(e){return null}}(e);if(s&&s.getTime()>=eN["2024-01-01"].timestamp&&"object"==typeof r&&r&&"string"==typeof r.code?i=r.code:"object"==typeof r&&r&&"string"==typeof r.error_code&&(i=r.error_code),i){if("weak_password"===i)throw new eK(tl(r),e.status,(null===(t=r.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===i)throw new eq}else if("object"==typeof r&&r&&"object"==typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new eK(tl(r),e.status,r.weak_password.reasons);throw new eD(tl(r),e.status||500,i)}let th=(e,t,r,i)=>{let s={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?s:(s.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),s.body=JSON.stringify(i),Object.assign(Object.assign({},s),r))};async function td(e,t,r,i){var s;let n=Object.assign({},null==i?void 0:i.headers);n[eA]||(n[eA]=eN["2024-01-01"].name),(null==i?void 0:i.jwt)&&(n.Authorization=`Bearer ${i.jwt}`);let a=null!==(s=null==i?void 0:i.query)&&void 0!==s?s:{};(null==i?void 0:i.redirectTo)&&(a.redirect_to=i.redirectTo);let o=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await tp(e,t,r+o,{headers:n,noResolveJson:null==i?void 0:i.noResolveJson},{},null==i?void 0:i.body);return(null==i?void 0:i.xform)?null==i?void 0:i.xform(l):{data:Object.assign({},l),error:null}}async function tp(e,t,r,i,s,n){let a;let o=th(t,i,s,n);try{a=await e(r,Object.assign({},o))}catch(e){throw console.error(e),new eJ(tl(e),0)}if(a.ok||await tu(a),null==i?void 0:i.noResolveJson)return a;try{return await a.json()}catch(e){await tu(e)}}function tf(e){var t,r;let i=null;return e.access_token&&e.refresh_token&&e.expires_in&&(i=Object.assign({},e),!e.expires_at)&&(i.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)),{data:{session:i,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function tg(e){let t=tf(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function tv(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function tm(e){return{data:e,error:null}}function tb(e){let{action_link:t,email_otp:r,hashed_token:i,redirect_to:s,verification_type:n}=e;return{data:{properties:{action_link:t,email_otp:r,hashed_token:i,redirect_to:s,verification_type:n},user:Object.assign({},to(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function tw(e){return e}var ty=function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,i=Object.getOwnPropertySymbols(e);s<i.length;s++)0>t.indexOf(i[s])&&Object.prototype.propertyIsEnumerable.call(e,i[s])&&(r[i[s]]=e[i[s]]);return r};class t_{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=e6(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t="global"){try{return await td(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if(e$(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,t={}){try{return await td(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:tv})}catch(e){if(e$(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:t}=e,r=ty(e,["options"]),i=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(i.new_email=null==r?void 0:r.newEmail,delete i.newEmail),await td(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:tb,redirectTo:null==t?void 0:t.redirectTo})}catch(e){if(e$(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await td(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:tv})}catch(e){if(e$(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var t,r,i,s,n,a,o;try{let l={nextPage:null,lastPage:0,total:0},c=await td(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(r=null===(t=null==e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==r?r:"",per_page:null!==(s=null===(i=null==e?void 0:e.perPage)||void 0===i?void 0:i.toString())&&void 0!==s?s:""},xform:tw});if(c.error)throw c.error;let u=await c.json(),h=null!==(n=c.headers.get("x-total-count"))&&void 0!==n?n:0,d=null!==(o=null===(a=c.headers.get("link"))||void 0===a?void 0:a.split(","))&&void 0!==o?o:[];return d.length>0&&(d.forEach(e=>{let t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);l[`${r}Page`]=t}),l.total=parseInt(h)),{data:Object.assign(Object.assign({},u),l),error:null}}catch(e){if(e$(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){try{return await td(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:tv})}catch(e){if(e$(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,t){try{return await td(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:tv})}catch(e){if(e$(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,t=!1){try{return await td(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:tv})}catch(e){if(e$(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){try{let{data:t,error:r}=await td(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:r}}catch(e){if(e$(e))return{data:null,error:e};throw e}}async _deleteFactor(e){try{return{data:await td(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(e){if(e$(e))return{data:null,error:e};throw e}}}let tS={getItem:e=>e3()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{e3()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{e3()&&globalThis.localStorage.removeItem(e)}};function tk(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}let tx={debug:!!(globalThis&&e3()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class tO extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class tT extends tO{}async function tC(e,t,r){tx.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);let i=new globalThis.AbortController;return t>0&&setTimeout(()=>{i.abort(),tx.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:i.signal},async i=>{if(i){tx.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,i.name);try{return await r()}finally{tx.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,i.name)}}else{if(0===t)throw tx.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new tT(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(tx.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}}))}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();let tP={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:eR,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function tE(e,t,r){return await r()}class tI{constructor(e){var t,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=tI.nextInstanceID,tI.nextInstanceID+=1,this.instanceID>0&&e1()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let i=Object.assign(Object.assign({},tP),e);if(this.logDebugMessages=!!i.debug,"function"==typeof i.debug&&(this.logger=i.debug),this.persistSession=i.persistSession,this.storageKey=i.storageKey,this.autoRefreshToken=i.autoRefreshToken,this.admin=new t_({url:i.url,headers:i.headers,fetch:i.fetch}),this.url=i.url,this.headers=i.headers,this.fetch=e6(i.fetch),this.lock=i.lock||tE,this.detectSessionInUrl=i.detectSessionInUrl,this.flowType=i.flowType,this.hasCustomAuthorizationHeader=i.hasCustomAuthorizationHeader,i.lock?this.lock=i.lock:e1()&&(null===(t=null==globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=tC:this.lock=tE,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?i.storage?this.storage=i.storage:e3()?this.storage=tS:(this.memoryStorage={},this.storage=tk(this.memoryStorage)):(this.memoryStorage={},this.storage=tk(this.memoryStorage)),e1()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null===(r=this.broadcastChannel)||void 0===r||r.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${ej}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let t=function(e){let t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(e){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}(window.location.href),r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),e1()&&this.detectSessionInUrl&&"none"!==r){let{data:i,error:s}=await this._getSessionFromURL(t,r);if(s){if(this._debug("#_initialize()","error detecting session from URL",s),e$(s)&&"AuthImplicitGrantRedirectError"===s.name){let t=null===(e=s.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:s}}return await this._removeSession(),{error:s}}let{session:n,redirectType:a}=i;return this._debug("#_initialize()","detected session in URL",n,"redirect type",a),await this._saveSession(n),setTimeout(async()=>{"recovery"===a?await this._notifyAllSubscribers("PASSWORD_RECOVERY",n):await this._notifyAllSubscribers("SIGNED_IN",n)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if(e$(e))return{error:e};return{error:new eU("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,i;try{let{data:s,error:n}=await td(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(r=null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==r?r:{},gotrue_meta_security:{captcha_token:null===(i=null==e?void 0:e.options)||void 0===i?void 0:i.captchaToken}},xform:tf});if(n||!s)return{data:{user:null,session:null},error:n};let a=s.session,o=s.user;return s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:o,session:a},error:null}}catch(e){if(e$(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var t,r,i;try{let s;if("email"in e){let{email:r,password:i,options:n}=e,a=null,o=null;"pkce"===this.flowType&&([a,o]=await tn(this.storage,this.storageKey)),s=await td(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==n?void 0:n.emailRedirectTo,body:{email:r,password:i,data:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:a,code_challenge_method:o},xform:tf})}else if("phone"in e){let{phone:t,password:n,options:a}=e;s=await td(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:n,data:null!==(r=null==a?void 0:a.data)&&void 0!==r?r:{},channel:null!==(i=null==a?void 0:a.channel)&&void 0!==i?i:"sms",gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:tf})}else throw new eV("You must provide either an email or phone number and a password");let{data:n,error:a}=s;if(a||!n)return{data:{user:null,session:null},error:a};let o=n.session,l=n.user;return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(e$(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let t;if("email"in e){let{email:r,password:i,options:s}=e;t=await td(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},xform:tg})}else if("phone"in e){let{phone:r,password:i,options:s}=e;t=await td(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},xform:tg})}else throw new eV("You must provide either an email or phone number and a password");let{data:r,error:i}=t;if(i)return{data:{user:null,session:null},error:i};if(!r||!r.session||!r.user)return{data:{user:null,session:null},error:new ez};return r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:i}}catch(e){if(e$(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var t,r,i,s;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(i=e.options)||void 0===i?void 0:i.queryParams,skipBrowserRedirect:null===(s=e.options)||void 0===s?void 0:s.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async _exchangeCodeForSession(e){let t=await e5(this.storage,`${this.storageKey}-code-verifier`),[r,i]=(null!=t?t:"").split("/");try{let{data:t,error:s}=await td(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:tf});if(await e8(this.storage,`${this.storageKey}-code-verifier`),s)throw s;if(!t||!t.session||!t.user)return{data:{user:null,session:null,redirectType:null},error:new ez};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=i?i:null}),error:s}}catch(e){if(e$(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:t,provider:r,token:i,access_token:s,nonce:n}=e,{data:a,error:o}=await td(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:i,access_token:s,nonce:n,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:tf});if(o)return{data:{user:null,session:null},error:o};if(!a||!a.session||!a.user)return{data:{user:null,session:null},error:new ez};return a.session&&(await this._saveSession(a.session),await this._notifyAllSubscribers("SIGNED_IN",a.session)),{data:a,error:o}}catch(e){if(e$(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var t,r,i,s,n;try{if("email"in e){let{email:i,options:s}=e,n=null,a=null;"pkce"===this.flowType&&([n,a]=await tn(this.storage,this.storageKey));let{error:o}=await td(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:i,data:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:{},create_user:null===(r=null==s?void 0:s.shouldCreateUser)||void 0===r||r,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},code_challenge:n,code_challenge_method:a},redirectTo:null==s?void 0:s.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){let{phone:t,options:r}=e,{data:a,error:o}=await td(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(i=null==r?void 0:r.data)&&void 0!==i?i:{},create_user:null===(s=null==r?void 0:r.shouldCreateUser)||void 0===s||s,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!==(n=null==r?void 0:r.channel)&&void 0!==n?n:"sms"}});return{data:{user:null,session:null,messageId:null==a?void 0:a.message_id},error:o}}throw new eV("You must provide either an email or phone number.")}catch(e){if(e$(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var t,r;try{let i,s;"options"in e&&(i=null===(t=e.options)||void 0===t?void 0:t.redirectTo,s=null===(r=e.options)||void 0===r?void 0:r.captchaToken);let{data:n,error:a}=await td(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:s}}),redirectTo:i,xform:tf});if(a)throw a;if(!n)throw Error("An error occurred on token verification.");let o=n.session,l=n.user;return(null==o?void 0:o.access_token)&&(await this._saveSession(o),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(e){if(e$(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var t,r,i;try{let s=null,n=null;return"pkce"===this.flowType&&([s,n]=await tn(this.storage,this.storageKey)),await td(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==r?r:void 0}),(null===(i=null==e?void 0:e.options)||void 0===i?void 0:i.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:s,code_challenge_method:n}),headers:this.headers,xform:tm})}catch(e){if(e$(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new eq;let{error:i}=await td(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:i}})}catch(e){if(e$(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let t=`${this.url}/resend`;if("email"in e){let{email:r,type:i,options:s}=e,{error:n}=await td(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},redirectTo:null==s?void 0:s.emailRedirectTo});return{data:{user:null,session:null},error:n}}if("phone"in e){let{phone:r,type:i,options:s}=e,{data:n,error:a}=await td(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}}});return{data:{user:null,session:null,messageId:null==n?void 0:n.message_id},error:a}}throw new eV("You must provide either an email or phone number and a type")}catch(e){if(e$(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch(e){}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,t=await e5(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let r=!!e.expires_at&&1e3*e.expires_at-Date.now()<9e4;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,i)=>(t||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,i))})}return{data:{session:e},error:null}}let{session:i,error:s}=await this._callRefreshToken(e.refresh_token);if(s)return{data:{session:null},error:s};return{data:{session:i},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{if(e)return await td(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:tv});return await this._useSession(async e=>{var t,r,i;let{data:s,error:n}=e;if(n)throw n;return(null===(t=s.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await td(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(i=null===(r=s.session)||void 0===r?void 0:r.access_token)&&void 0!==i?i:void 0,xform:tv}):{data:{user:null},error:new eq}})}catch(e){if(e$(e))return e$(e)&&"AuthSessionMissingError"===e.name&&(await this._removeSession(),await e8(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{let{data:i,error:s}=r;if(s)throw s;if(!i.session)throw new eq;let n=i.session,a=null,o=null;"pkce"===this.flowType&&null!=e.email&&([a,o]=await tn(this.storage,this.storageKey));let{data:l,error:c}=await td(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:a,code_challenge_method:o}),jwt:n.access_token,xform:tv});if(c)throw c;return n.user=l.user,await this._saveSession(n),await this._notifyAllSubscribers("USER_UPDATED",n),{data:{user:n.user},error:null}})}catch(e){if(e$(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new eq;let t=Date.now()/1e3,r=t,i=!0,s=null,{payload:n}=te(e.access_token);if(n.exp&&(i=(r=n.exp)<=t),i){let{session:t,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!t)return{data:{user:null,session:null},error:null};s=t}else{let{data:i,error:n}=await this._getUser(e.access_token);if(n)throw n;s={access_token:e.access_token,refresh_token:e.refresh_token,user:i.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(s),await this._notifyAllSubscribers("SIGNED_IN",s)}return{data:{user:s.user,session:s},error:null}}catch(e){if(e$(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){let{data:i,error:s}=t;if(s)throw s;e=null!==(r=i.session)&&void 0!==r?r:void 0}if(!(null==e?void 0:e.refresh_token))throw new eq;let{session:i,error:s}=await this._callRefreshToken(e.refresh_token);return s?{data:{user:null,session:null},error:s}:i?{data:{user:i.user,session:i},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if(e$(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,t){try{if(!e1())throw new eF("No browser detected.");if(e.error||e.error_description||e.error_code)throw new eF(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new eG("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new eF("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new eG("No code detected.");let{data:t,error:r}=await this._exchangeCodeForSession(e.code);if(r)throw r;let i=new URL(window.location.href);return i.searchParams.delete("code"),window.history.replaceState(window.history.state,"",i.toString()),{data:{session:t.session,redirectType:null},error:null}}let{provider_token:r,provider_refresh_token:i,access_token:s,refresh_token:n,expires_in:a,expires_at:o,token_type:l}=e;if(!s||!a||!n||!l)throw new eF("No session defined in URL");let c=Math.round(Date.now()/1e3),u=parseInt(a),h=c+u;o&&(h=parseInt(o));let d=h-c;1e3*d<=3e4&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${u}s`);let p=h-u;c-p>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",p,h,c):c-p<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",p,h,c);let{data:f,error:g}=await this._getUser(s);if(g)throw g;let v={provider_token:r,provider_refresh_token:i,access_token:s,expires_in:u,expires_at:h,refresh_token:n,token_type:l,user:f.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:v,redirectType:e.type},error:null}}catch(e){if(e$(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let t=await e5(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;let{data:i,error:s}=t;if(s)return{error:s};let n=null===(r=i.session)||void 0===r?void 0:r.access_token;if(n){let{error:t}=await this.admin.signOut(n,e);if(t&&!(e$(t)&&"AuthApiError"===t.name&&(404===t.status||401===t.status||403===t.status)))return{error:t}}return"others"!==e&&(await this._removeSession(),await e8(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,i;try{let{data:{session:i},error:s}=t;if(s)throw s;await (null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",i)),this._debug("INITIAL_SESSION","callback id",e,"session",i)}catch(t){await (null===(i=this.stateChangeEmitters.get(e))||void 0===i?void 0:i.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",t),console.error(t)}})}async resetPasswordForEmail(e,t={}){let r=null,i=null;"pkce"===this.flowType&&([r,i]=await tn(this.storage,this.storageKey,!0));try{return await td(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:i,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(e){if(e$(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(e){if(e$(e))return{data:null,error:e};throw e}}async linkIdentity(e){var t;try{let{data:r,error:i}=await this._useSession(async t=>{var r,i,s,n,a;let{data:o,error:l}=t;if(l)throw l;let c=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(r=e.options)||void 0===r?void 0:r.redirectTo,scopes:null===(i=e.options)||void 0===i?void 0:i.scopes,queryParams:null===(s=e.options)||void 0===s?void 0:s.queryParams,skipBrowserRedirect:!0});return await td(this.fetch,"GET",c,{headers:this.headers,jwt:null!==(a=null===(n=o.session)||void 0===n?void 0:n.access_token)&&void 0!==a?a:void 0})});if(i)throw i;return!e1()||(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)||window.location.assign(null==r?void 0:r.url),{data:{provider:e.provider,url:null==r?void 0:r.url},error:null}}catch(t){if(e$(t))return{data:{provider:e.provider,url:null},error:t};throw t}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,i;let{data:s,error:n}=t;if(n)throw n;return await td(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(i=null===(r=s.session)||void 0===r?void 0:r.access_token)&&void 0!==i?i:void 0})})}catch(e){if(e$(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{var r,i;let s=Date.now();return await (r=async r=>(r>0&&await tt(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await td(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:tf})),i=(e,t)=>t&&eH(t)&&Date.now()+200*Math.pow(2,e)-s<3e4,new Promise((e,t)=>{(async()=>{for(let s=0;s<1/0;s++)try{let t=await r(s);if(!i(s,null,t)){e(t);return}}catch(e){if(!i(s,e)){t(e);return}}})()}))}catch(e){if(this._debug(t,"error",e),e$(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){let r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),e1()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e;let t="#_recoverAndRefresh()";this._debug(t,"begin");try{let r=await e5(this.storage,this.storageKey);if(this._debug(t,"session from storage",r),!this._isValidSession(r)){this._debug(t,"session is not valid"),null!==r&&await this._removeSession();return}let i=(null!==(e=r.expires_at)&&void 0!==e?e:1/0)*1e3-Date.now()<9e4;if(this._debug(t,`session has${i?"":" not"} expired with margin of 90000s`),i){if(this.autoRefreshToken&&r.refresh_token){let{error:e}=await this._callRefreshToken(r.refresh_token);e&&(console.error(e),eH(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(e){this._debug(t,"error",e),console.error(e);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new eq;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let i=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(i,"begin");try{this.refreshingDeferred=new e7;let{data:t,error:r}=await this._refreshAccessToken(e);if(r)throw r;if(!t.session)throw new eq;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);let i={session:t.session,error:null};return this.refreshingDeferred.resolve(i),i}catch(e){if(this._debug(i,"error",e),e$(e)){let r={session:null,error:e};return eH(e)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(r),r}throw null===(r=this.refreshingDeferred)||void 0===r||r.reject(e),e}finally{this.refreshingDeferred=null,this._debug(i,"end")}}async _notifyAllSubscribers(e,t,r=!0){let i=`#_notifyAllSubscribers(${e})`;this._debug(i,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});let i=[],s=Array.from(this.stateChangeEmitters.values()).map(async r=>{try{await r.callback(e,t)}catch(e){i.push(e)}});if(await Promise.all(s),i.length>0){for(let e=0;e<i.length;e+=1)console.error(i[e]);throw i[0]}}finally{this._debug(i,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await e9(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await e8(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&e1()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),3e4);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async t=>{let{data:{session:r}}=t;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}let i=Math.floor((1e3*r.expires_at-e)/3e4);this._debug("#_autoRefreshTokenTick()",`access token expires in ${i} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),i<=3&&await this._callRefreshToken(r.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof tO)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!e1()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState){this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){let i=[`provider=${encodeURIComponent(t)}`];if((null==r?void 0:r.redirectTo)&&i.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),(null==r?void 0:r.scopes)&&i.push(`scopes=${encodeURIComponent(r.scopes)}`),"pkce"===this.flowType){let[e,t]=await tn(this.storage,this.storageKey),r=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});i.push(r.toString())}if(null==r?void 0:r.queryParams){let e=new URLSearchParams(r.queryParams);i.push(e.toString())}return(null==r?void 0:r.skipBrowserRedirect)&&i.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${i.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;let{data:i,error:s}=t;return s?{data:null,error:s}:await td(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(r=null==i?void 0:i.session)||void 0===r?void 0:r.access_token})})}catch(e){if(e$(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async t=>{var r,i;let{data:s,error:n}=t;if(n)return{data:null,error:n};let a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:o,error:l}=await td(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null===(i=null==o?void 0:o.totp)||void 0===i?void 0:i.qr_code)&&(o.totp.qr_code=`data:image/svg+xml;utf-8,${o.totp.qr_code}`),{data:o,error:null})})}catch(e){if(e$(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:i,error:s}=t;if(s)return{data:null,error:s};let{data:n,error:a}=await td(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(r=null==i?void 0:i.session)||void 0===r?void 0:r.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+n.expires_in},n)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",n),{data:n,error:a})})}catch(e){if(e$(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:i,error:s}=t;return s?{data:null,error:s}:await td(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(r=null==i?void 0:i.session)||void 0===r?void 0:r.access_token})})}catch(e){if(e$(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){let{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};let r=(null==e?void 0:e.factors)||[],i=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),s=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:i,phone:s},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;let{data:{session:i},error:s}=e;if(s)return{data:null,error:s};if(!i)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:n}=te(i.access_token),a=null;n.aal&&(a=n.aal);let o=a;return(null!==(r=null===(t=i.user.factors)||void 0===t?void 0:t.filter(e=>"verified"===e.status))&&void 0!==r?r:[]).length>0&&(o="aal2"),{data:{currentLevel:a,nextLevel:o,currentAuthenticationMethods:n.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r||(r=this.jwks.keys.find(t=>t.kid===e))&&this.jwks_cached_at+6e5>Date.now())return r;let{data:i,error:s}=await td(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(s)throw s;if(!i.keys||0===i.keys.length)throw new eW("JWKS is empty");if(this.jwks=i,this.jwks_cached_at=Date.now(),!(r=i.keys.find(t=>t.kid===e)))throw new eW("No matching signing key found in JWKS");return r}async getClaims(e,t={keys:[]}){try{let r=e;if(!r){let{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};r=e.session.access_token}let{header:i,payload:s,signature:n,raw:{header:a,payload:o}}=te(r);if(!function(e){if(!e)throw Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw Error("JWT has expired")}(s.exp),!i.kid||"HS256"===i.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:e}=await this.getUser(r);if(e)throw e;return{data:{claims:s,header:i,signature:n},error:null}}let l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}(i.alg),c=await this.fetchJwk(i.kid,t),u=await crypto.subtle.importKey("jwk",c,l,!0,["verify"]);if(!await crypto.subtle.verify(l,u,n,function(e){let t=[];return function(e,t){for(let r=0;r<e.length;r+=1){let i=e.charCodeAt(r);if(i>55295&&i<=56319){let t=(i-55296)*1024&65535;i=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127){t(e);return}if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(i,t)}}(e,e=>t.push(e)),new Uint8Array(t)}(`${a}.${o}`)))throw new eW("Invalid JWT signature");return{data:{claims:s,header:i,signature:n},error:null}}catch(e){if(e$(e))return{data:null,error:e};throw e}}}tI.nextInstanceID=0;let tj=tI;class tR extends tj{constructor(e){super(e)}}class tA{constructor(e,t,r){var i,s,n;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let a=new URL(function(e){return e.endsWith("/")?e:e+"/"}(e));this.realtimeUrl=new URL("realtime/v1",a),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",a),this.storageUrl=new URL("storage/v1",a),this.functionsUrl=new URL("functions/v1",a);let o=`sb-${a.hostname.split(".")[0]}-auth-token`,l=function(e,t){var r,i;let{db:s,auth:n,realtime:a,global:o}=e,{db:l,auth:c,realtime:u,global:h}=t,d={db:Object.assign(Object.assign({},l),s),auth:Object.assign(Object.assign({},c),n),realtime:Object.assign(Object.assign({},u),a),global:Object.assign(Object.assign(Object.assign({},h),o),{headers:Object.assign(Object.assign({},null!==(r=null==h?void 0:h.headers)&&void 0!==r?r:{}),null!==(i=null==o?void 0:o.headers)&&void 0!==i?i:{})}),accessToken:()=>{var e,t,r,i;return e=this,t=void 0,i=function*(){return""},new(r=void 0,r=Promise)(function(s,n){function a(e){try{l(i.next(e))}catch(e){n(e)}}function o(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(a,o)}l((i=i.apply(e,t||[])).next())})}};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=r?r:{},{db:ex,realtime:eT,auth:Object.assign(Object.assign({},eO),{storageKey:o}),global:ek});this.storageKey=null!==(i=l.auth.storageKey)&&void 0!==i?i:"",this.headers=null!==(s=l.global.headers)&&void 0!==s?s:{},l.accessToken?(this.accessToken=l.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(n=l.auth)&&void 0!==n?n:{},this.headers,l.global.fetch),this.fetch=eI(t,this._getAccessToken.bind(this),l.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},l.realtime)),this.rest=new f(new URL("rest/v1",a).href,{headers:this.headers,schema:l.db.schema,fetch:this.fetch}),l.accessToken||this._listenForAuthEvents()}get functions(){return new p(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new e_(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t,r,i,s,n;return r=this,i=void 0,s=void 0,n=function*(){if(this.accessToken)return yield this.accessToken();let{data:r}=yield this.auth.getSession();return null!==(t=null===(e=r.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null},new(s||(s=Promise))(function(e,t){function a(e){try{l(n.next(e))}catch(e){t(e)}}function o(e){try{l(n.throw(e))}catch(e){t(e)}}function l(t){var r;t.done?e(t.value):((r=t.value)instanceof s?r:new s(function(e){e(r)})).then(a,o)}l((n=n.apply(r,i||[])).next())})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,storageKey:s,flowType:n,lock:a,debug:o},l,c){let u={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new tR({url:this.authUrl.href,headers:Object.assign(Object.assign({},u),l),storageKey:s,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,flowType:n,lock:a,debug:o,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new U(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==r?this.changedAccessToken=r:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}}let tN=(e,t,r)=>new tA(e,t,r);var tL=Object.create,tM=Object.defineProperty,t$=Object.getOwnPropertyDescriptor,tD=Object.getOwnPropertyNames,tU=Object.getPrototypeOf,tB=Object.prototype.hasOwnProperty,tq=(e,t,r,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of tD(t))tB.call(e,s)||s===r||tM(e,s,{get:()=>t[s],enumerable:!(i=t$(t,s))||i.enumerable});return e},tz=(e,t,r)=>(r=null!=e?tL(tU(e)):{},tq(!t&&e&&e.__esModule?r:tM(r,"default",{value:e,enumerable:!0}),e)),tV=(i={"../../node_modules/.pnpm/cookie@0.5.0/node_modules/cookie/index.js"(e){e.parse=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var r={},s=(t||{}).decode||i,n=0;n<e.length;){var a=e.indexOf("=",n);if(-1===a)break;var o=e.indexOf(";",n);if(-1===o)o=e.length;else if(o<a){n=e.lastIndexOf(";",a-1)+1;continue}var l=e.slice(n,a).trim();if(void 0===r[l]){var c=e.slice(a+1,o).trim();34===c.charCodeAt(0)&&(c=c.slice(1,-1)),r[l]=function(e,t){try{return t(e)}catch(t){return e}}(c,s)}n=o+1}return r},e.serialize=function(e,i,n){var a=n||{},o=a.encode||s;if("function"!=typeof o)throw TypeError("option encode is invalid");if(!r.test(e))throw TypeError("argument name is invalid");var l=o(i);if(l&&!r.test(l))throw TypeError("argument val is invalid");var c=e+"="+l;if(null!=a.maxAge){var u=a.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");c+="; Max-Age="+Math.floor(u)}if(a.domain){if(!r.test(a.domain))throw TypeError("option domain is invalid");c+="; Domain="+a.domain}if(a.path){if(!r.test(a.path))throw TypeError("option path is invalid");c+="; Path="+a.path}if(a.expires){var h=a.expires;if("[object Date]"!==t.call(h)&&!(h instanceof Date)||isNaN(h.valueOf()))throw TypeError("option expires is invalid");c+="; Expires="+h.toUTCString()}if(a.httpOnly&&(c+="; HttpOnly"),a.secure&&(c+="; Secure"),a.priority)switch("string"==typeof a.priority?a.priority.toLowerCase():a.priority){case"low":c+="; Priority=Low";break;case"medium":c+="; Priority=Medium";break;case"high":c+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":c+="; SameSite=Strict";break;case"lax":c+="; SameSite=Lax";break;case"none":c+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return c};var t=Object.prototype.toString,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function i(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}function s(e){return encodeURIComponent(e)}}},function(){return s||(0,i[tD(i)[0]])((s={exports:{}}).exports,s),s.exports}),tF=tz(tV()),tG=tz(tV());function tJ(e){if(!e)return null;try{let t=JSON.parse(e);if(!t)return null;if("Object"===t.constructor.name)return t;if("Array"!==t.constructor.name)throw Error(`Unexpected format: ${t.constructor.name}`);let[r,i,s]=t[0].split("."),n=o(i),a=new TextDecoder,{exp:l,sub:c,...u}=JSON.parse(a.decode(n));return{expires_at:l,expires_in:l-Math.round(Date.now()/1e3),token_type:"bearer",access_token:t[0],refresh_token:t[1],provider_token:t[2],provider_refresh_token:t[3],user:{id:c,factors:t[4],...u}}}catch(e){return console.warn("Failed to parse cookie string:",e),null}}function tH(e){var t;return JSON.stringify([e.access_token,e.refresh_token,e.provider_token,e.provider_refresh_token,(null==(t=e.user)?void 0:t.factors)??null])}function tK(){return"undefined"!=typeof window&&void 0!==window.document}var tW={path:"/",sameSite:"lax",maxAge:31536e6},tX=RegExp(".{1,3180}","g"),tY=class{constructor(e){this.cookieOptions={...tW,...e,maxAge:tW.maxAge}}getItem(e){let t=this.getCookie(e);if(e.endsWith("-code-verifier")&&t)return t;if(t)return JSON.stringify(tJ(t));let r=function(e,t=()=>null){let r=[];for(let i=0;;i++){let s=t(`${e}.${i}`);if(!s)break;r.push(s)}return r.length?r.join(""):null}(e,e=>this.getCookie(e));return null!==r?JSON.stringify(tJ(r)):null}setItem(e,t){if(e.endsWith("-code-verifier")){this.setCookie(e,t);return}(function(e,t,r){if(1===Math.ceil(t.length/((void 0)??3180)))return[{name:e,value:t}];let i=[],s=t.match(tX);return null==s||s.forEach((t,r)=>{let s=`${e}.${r}`;i.push({name:s,value:t})}),i})(e,tH(JSON.parse(t))).forEach(e=>{this.setCookie(e.name,e.value)})}removeItem(e){this._deleteSingleCookie(e),this._deleteChunkedCookies(e)}_deleteSingleCookie(e){this.getCookie(e)&&this.deleteCookie(e)}_deleteChunkedCookies(e,t=0){for(let r=t;;r++){let t=`${e}.${r}`;if(void 0===this.getCookie(t))break;this.deleteCookie(t)}}},tZ=class extends tY{constructor(e){super(e)}getCookie(e){return tK()?(0,tF.parse)(document.cookie)[e]:null}setCookie(e,t){if(!tK())return null;document.cookie=(0,tF.serialize)(e,t,{...this.cookieOptions,httpOnly:!1})}deleteCookie(e){if(!tK())return null;document.cookie=(0,tF.serialize)(e,"",{...this.cookieOptions,maxAge:0,httpOnly:!1})}};function tQ(e,t,r){var i;let s=tK();return tN(e,t,{...r,auth:{flowType:"pkce",autoRefreshToken:s,detectSessionInUrl:s,persistSession:!0,storage:r.auth.storage,...(null==(i=r.auth)?void 0:i.storageKey)?{storageKey:r.auth.storageKey}:{}}})}var t0=tG.parse,t1=tG.serialize}},e=>{var t=e(e.s=913);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_middleware=t}]);
//# sourceMappingURL=middleware.js.map