exports.id=785,exports.ids=[785],exports.modules={2052:()=>{},5600:()=>{},3052:(e,t,s)=>{"use strict";let{EMPTY_BUFFER:r}=s(2563),i=Buffer[Symbol.species];function o(e,t,s,r,i){for(let o=0;o<i;o++)s[r+o]=e[o]^t[3&o]}function n(e,t){for(let s=0;s<e.length;s++)e[s]^=t[3&s]}if(e.exports={concat:function(e,t){if(0===e.length)return r;if(1===e.length)return e[0];let s=Buffer.allocUnsafe(t),o=0;for(let t=0;t<e.length;t++){let r=e[t];s.set(r,o),o+=r.length}return o<t?new i(s.buffer,s.byteOffset,o):s},mask:o,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:function e(t){let s;return(e.readOnly=!0,Buffer.isBuffer(t))?t:(t instanceof ArrayBuffer?s=new i(t):ArrayBuffer.isView(t)?s=new i(t.buffer,t.byteOffset,t.byteLength):(s=Buffer.from(t),e.readOnly=!1),s)},unmask:n},!process.env.WS_NO_BUFFER_UTIL)try{let t=s(2052);e.exports.mask=function(e,s,r,i,n){n<48?o(e,s,r,i,n):t.mask(e,s,r,i,n)},e.exports.unmask=function(e,s){e.length<32?n(e,s):t.unmask(e,s)}}catch(e){}},2563:e=>{"use strict";let t=["nodebuffer","arraybuffer","fragments"],s="undefined"!=typeof Blob;s&&t.push("blob"),e.exports={BINARY_TYPES:t,EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",hasBlob:s,kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},7237:(e,t,s)=>{"use strict";let{kForOnEventAttribute:r,kListener:i}=s(2563),o=Symbol("kCode"),n=Symbol("kData"),a=Symbol("kError"),h=Symbol("kMessage"),l=Symbol("kReason"),c=Symbol("kTarget"),d=Symbol("kType"),f=Symbol("kWasClean");class _{constructor(e){this[c]=null,this[d]=e}get target(){return this[c]}get type(){return this[d]}}Object.defineProperty(_.prototype,"target",{enumerable:!0}),Object.defineProperty(_.prototype,"type",{enumerable:!0});class u extends _{constructor(e,t={}){super(e),this[o]=void 0===t.code?0:t.code,this[l]=void 0===t.reason?"":t.reason,this[f]=void 0!==t.wasClean&&t.wasClean}get code(){return this[o]}get reason(){return this[l]}get wasClean(){return this[f]}}Object.defineProperty(u.prototype,"code",{enumerable:!0}),Object.defineProperty(u.prototype,"reason",{enumerable:!0}),Object.defineProperty(u.prototype,"wasClean",{enumerable:!0});class p extends _{constructor(e,t={}){super(e),this[a]=void 0===t.error?null:t.error,this[h]=void 0===t.message?"":t.message}get error(){return this[a]}get message(){return this[h]}}Object.defineProperty(p.prototype,"error",{enumerable:!0}),Object.defineProperty(p.prototype,"message",{enumerable:!0});class m extends _{constructor(e,t={}){super(e),this[n]=void 0===t.data?null:t.data}get data(){return this[n]}}function y(e,t,s){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,s):e.call(t,s)}Object.defineProperty(m.prototype,"data",{enumerable:!0}),e.exports={CloseEvent:u,ErrorEvent:p,Event:_,EventTarget:{addEventListener(e,t,s={}){let o;for(let o of this.listeners(e))if(!s[r]&&o[i]===t&&!o[r])return;if("message"===e)o=function(e,s){let r=new m("message",{data:s?e:e.toString()});r[c]=this,y(t,this,r)};else if("close"===e)o=function(e,s){let r=new u("close",{code:e,reason:s.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});r[c]=this,y(t,this,r)};else if("error"===e)o=function(e){let s=new p("error",{error:e,message:e.message});s[c]=this,y(t,this,s)};else{if("open"!==e)return;o=function(){let e=new _("open");e[c]=this,y(t,this,e)}}o[r]=!!s[r],o[i]=t,s.once?this.once(e,o):this.on(e,o)},removeEventListener(e,t){for(let s of this.listeners(e))if(s[i]===t&&!s[r]){this.removeListener(e,s);break}}},MessageEvent:m}},7363:(e,t,s)=>{"use strict";let{tokenChars:r}=s(9471);function i(e,t,s){void 0===e[t]?e[t]=[s]:e[t].push(s)}e.exports={format:function(e){return Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>[t].concat(Object.keys(e).map(t=>{let s=e[t];return Array.isArray(s)||(s=[s]),s.map(e=>!0===e?t:`${t}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let t,s;let o=Object.create(null),n=Object.create(null),a=!1,h=!1,l=!1,c=-1,d=-1,f=-1,_=0;for(;_<e.length;_++)if(d=e.charCodeAt(_),void 0===t){if(-1===f&&1===r[d])-1===c&&(c=_);else if(0!==_&&(32===d||9===d))-1===f&&-1!==c&&(f=_);else if(59===d||44===d){if(-1===c)throw SyntaxError(`Unexpected character at index ${_}`);-1===f&&(f=_);let s=e.slice(c,f);44===d?(i(o,s,n),n=Object.create(null)):t=s,c=f=-1}else throw SyntaxError(`Unexpected character at index ${_}`)}else if(void 0===s){if(-1===f&&1===r[d])-1===c&&(c=_);else if(32===d||9===d)-1===f&&-1!==c&&(f=_);else if(59===d||44===d){if(-1===c)throw SyntaxError(`Unexpected character at index ${_}`);-1===f&&(f=_),i(n,e.slice(c,f),!0),44===d&&(i(o,t,n),n=Object.create(null),t=void 0),c=f=-1}else if(61===d&&-1!==c&&-1===f)s=e.slice(c,_),c=f=-1;else throw SyntaxError(`Unexpected character at index ${_}`)}else if(h){if(1!==r[d])throw SyntaxError(`Unexpected character at index ${_}`);-1===c?c=_:a||(a=!0),h=!1}else if(l){if(1===r[d])-1===c&&(c=_);else if(34===d&&-1!==c)l=!1,f=_;else if(92===d)h=!0;else throw SyntaxError(`Unexpected character at index ${_}`)}else if(34===d&&61===e.charCodeAt(_-1))l=!0;else if(-1===f&&1===r[d])-1===c&&(c=_);else if(-1!==c&&(32===d||9===d))-1===f&&(f=_);else if(59===d||44===d){if(-1===c)throw SyntaxError(`Unexpected character at index ${_}`);-1===f&&(f=_);let r=e.slice(c,f);a&&(r=r.replace(/\\/g,""),a=!1),i(n,s,r),44===d&&(i(o,t,n),n=Object.create(null),t=void 0),s=void 0,c=f=-1}else throw SyntaxError(`Unexpected character at index ${_}`);if(-1===c||l||32===d||9===d)throw SyntaxError("Unexpected end of input");-1===f&&(f=_);let u=e.slice(c,f);return void 0===t?i(o,u,n):(void 0===s?i(n,u,!0):a?i(n,s,u.replace(/\\/g,"")):i(n,s,u),i(o,t,n)),o}}},2648:e=>{"use strict";let t=Symbol("kDone"),s=Symbol("kRun");class r{constructor(e){this[t]=()=>{this.pending--,this[s]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[s]()}[s](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[t])}}}e.exports=r},2611:(e,t,s)=>{"use strict";let r;let i=s(1568),o=s(3052),n=s(2648),{kStatusCode:a}=s(2563),h=Buffer[Symbol.species],l=Buffer.from([0,0,255,255]),c=Symbol("permessage-deflate"),d=Symbol("total-length"),f=Symbol("callback"),_=Symbol("buffers"),u=Symbol("error");class p{constructor(e,t,s){this._maxPayload=0|s,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,r||(r=new n(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[f];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,s=e.find(e=>(!1!==t.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==t.serverMaxWindowBits&&("number"!=typeof t.serverMaxWindowBits||!(t.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof t.clientMaxWindowBits||!!e.client_max_window_bits));if(!s)throw Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(s.server_no_context_takeover=!0),t.clientNoContextTakeover&&(s.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(s.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?s.client_max_window_bits=t.clientMaxWindowBits:(!0===s.client_max_window_bits||!1===t.clientMaxWindowBits)&&delete s.client_max_window_bits,s}acceptAsClient(e){let t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(t=>{let s=e[t];if(s.length>1)throw Error(`Parameter "${t}" must have only a single value`);if(s=s[0],"client_max_window_bits"===t){if(!0!==s){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else if("server_max_window_bits"===t){let e=+s;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${s}`);s=e}else if("client_no_context_takeover"===t||"server_no_context_takeover"===t){if(!0!==s)throw TypeError(`Invalid value for parameter "${t}": ${s}`)}else throw Error(`Unknown parameter "${t}"`);e[t]=s})}),e}decompress(e,t,s){r.add(r=>{this._decompress(e,t,(e,t)=>{r(),s(e,t)})})}compress(e,t,s){r.add(r=>{this._compress(e,t,(e,t)=>{r(),s(e,t)})})}_decompress(e,t,s){let r=this._isServer?"client":"server";if(!this._inflate){let e=`${r}_max_window_bits`,t="number"!=typeof this.params[e]?i.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=i.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t}),this._inflate[c]=this,this._inflate[d]=0,this._inflate[_]=[],this._inflate.on("error",g),this._inflate.on("data",y)}this._inflate[f]=s,this._inflate.write(e),t&&this._inflate.write(l),this._inflate.flush(()=>{let e=this._inflate[u];if(e){this._inflate.close(),this._inflate=null,s(e);return}let i=o.concat(this._inflate[_],this._inflate[d]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[d]=0,this._inflate[_]=[],t&&this.params[`${r}_no_context_takeover`]&&this._inflate.reset()),s(null,i)})}_compress(e,t,s){let r=this._isServer?"server":"client";if(!this._deflate){let e=`${r}_max_window_bits`,t="number"!=typeof this.params[e]?i.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=i.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t}),this._deflate[d]=0,this._deflate[_]=[],this._deflate.on("data",m)}this._deflate[f]=s,this._deflate.write(e),this._deflate.flush(i.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=o.concat(this._deflate[_],this._deflate[d]);t&&(e=new h(e.buffer,e.byteOffset,e.length-4)),this._deflate[f]=null,this._deflate[d]=0,this._deflate[_]=[],t&&this.params[`${r}_no_context_takeover`]&&this._deflate.reset(),s(null,e)})}}function m(e){this[_].push(e),this[d]+=e.length}function y(e){if(this[d]+=e.length,this[c]._maxPayload<1||this[d]<=this[c]._maxPayload){this[_].push(e);return}this[u]=RangeError("Max payload size exceeded"),this[u].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[u][a]=1009,this.removeListener("data",y),this.reset()}function g(e){if(this[c]._inflate=null,this[u]){this[f](this[u]);return}e[a]=1007,this[f](e)}e.exports=p},8378:(e,t,s)=>{"use strict";let{Writable:r}=s(6162),i=s(2611),{BINARY_TYPES:o,EMPTY_BUFFER:n,kStatusCode:a,kWebSocket:h}=s(2563),{concat:l,toArrayBuffer:c,unmask:d}=s(3052),{isValidStatusCode:f,isValidUTF8:_}=s(9471),u=Buffer[Symbol.species];class p extends r{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||o[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[h]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,t,s){if(8===this._opcode&&0==this._state)return s();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(s)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let t=this._buffers[0];return this._buffers[0]=new u(t.buffer,t.byteOffset+e,t.length-e),new u(t.buffer,t.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let s=this._buffers[0],r=t.length-e;e>=s.length?t.set(this._buffers.shift(),r):(t.set(new Uint8Array(s.buffer,s.byteOffset,e),r),this._buffers[0]=new u(s.buffer,s.byteOffset+e,s.length-e)),e-=s.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((48&t[0])!=0){e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));return}let s=(64&t[0])==64;if(s&&!this._extensions[i.extensionName]){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._fin=(128&t[0])==128,this._opcode=15&t[0],this._payloadLength=127&t[1],0===this._opcode){if(s){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(!this._fragmented){e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented){e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._compressed=s}else if(this._opcode>7&&this._opcode<11){if(!this._fin){e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));return}if(s){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength){e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"));return}}else{e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&t[1])==128,this._isServer){if(!this._masked){e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"));return}}else if(this._masked){e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));return}126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),s=t.readUInt32BE(0);if(s>2097151){e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));return}this._payloadLength=4294967296*s+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let t=n;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&d(t,this._mask)}if(this._opcode>7){this.controlMessage(t,e);return}if(this._compressed){this._state=5,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[i.extensionName].decompress(e,this._fin,(e,s)=>{if(e)return t(e);if(s.length){if(this._messageLength+=s.length,this._messageLength>this._maxPayload&&this._maxPayload>0){t(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._fragments.push(s)}this.dataMessage(t),0===this._state&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=0;return}let t=this._messageLength,s=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let r;r="nodebuffer"===this._binaryType?l(s,t):"arraybuffer"===this._binaryType?c(l(s,t)):"blob"===this._binaryType?new Blob(s):s,this._allowSynchronousEvents?(this.emit("message",r,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",r,!0),this._state=0,this.startLoop(e)}))}else{let r=l(s,t);if(!this._skipUTF8Validation&&!_(r)){e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}5===this._state||this._allowSynchronousEvents?(this.emit("message",r,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",r,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,t){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,n),this.end();else{let s=e.readUInt16BE(0);if(!f(s)){t(this.createError(RangeError,`invalid status code ${s}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));return}let r=new u(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!_(r)){t(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}this._loop=!1,this.emit("conclude",s,r),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(t)}))}createError(e,t,s,r,i){this._loop=!1,this._errored=!0;let o=new e(s?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(o,this.createError),o.code=i,o[a]=r,o}}e.exports=p},8386:(e,t,s)=>{"use strict";let r;let{Duplex:i}=s(6162),{randomFillSync:o}=s(4770),n=s(2611),{EMPTY_BUFFER:a,kWebSocket:h,NOOP:l}=s(2563),{isBlob:c,isValidStatusCode:d}=s(9471),{mask:f,toBuffer:_}=s(3052),u=Symbol("kByteLength"),p=Buffer.alloc(4),m=8192;class y{constructor(e,t,s){this._extensions=t||{},s&&(this._generateMask=s,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._queue=[],this._state=0,this.onerror=l,this[h]=void 0}static frame(e,t){let s,i;let n=!1,a=2,h=!1;t.mask&&(s=t.maskBuffer||p,t.generateMask?t.generateMask(s):(8192===m&&(void 0===r&&(r=Buffer.alloc(8192)),o(r,0,8192),m=0),s[0]=r[m++],s[1]=r[m++],s[2]=r[m++],s[3]=r[m++]),h=(s[0]|s[1]|s[2]|s[3])==0,a=6),"string"==typeof e?i=(!t.mask||h)&&void 0!==t[u]?t[u]:(e=Buffer.from(e)).length:(i=e.length,n=t.mask&&t.readOnly&&!h);let l=i;i>=65536?(a+=8,l=127):i>125&&(a+=2,l=126);let c=Buffer.allocUnsafe(n?i+a:a);return(c[0]=t.fin?128|t.opcode:t.opcode,t.rsv1&&(c[0]|=64),c[1]=l,126===l?c.writeUInt16BE(i,2):127===l&&(c[2]=c[3]=0,c.writeUIntBE(i,4,6)),t.mask)?(c[1]|=128,c[a-4]=s[0],c[a-3]=s[1],c[a-2]=s[2],c[a-1]=s[3],h)?[c,e]:n?(f(e,s,c,a,i),[c]):(f(e,s,e,0,i),[c,e]):[c,e]}close(e,t,s,r){let i;if(void 0===e)i=a;else if("number"==typeof e&&d(e)){if(void 0!==t&&t.length){let s=Buffer.byteLength(t);if(s>123)throw RangeError("The message must not be greater than 123 bytes");(i=Buffer.allocUnsafe(2+s)).writeUInt16BE(e,0),"string"==typeof t?i.write(t,2):i.set(t,2)}else(i=Buffer.allocUnsafe(2)).writeUInt16BE(e,0)}else throw TypeError("First argument must be a valid error code number");let o={[u]:i.length,fin:!0,generateMask:this._generateMask,mask:s,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};0!==this._state?this.enqueue([this.dispatch,i,!1,o,r]):this.sendFrame(y.frame(i,o),r)}ping(e,t,s){let r,i;if("string"==typeof e?(r=Buffer.byteLength(e),i=!1):c(e)?(r=e.size,i=!1):(r=(e=_(e)).length,i=_.readOnly),r>125)throw RangeError("The data size must not be greater than 125 bytes");let o={[u]:r,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:i,rsv1:!1};c(e)?0!==this._state?this.enqueue([this.getBlobData,e,!1,o,s]):this.getBlobData(e,!1,o,s):0!==this._state?this.enqueue([this.dispatch,e,!1,o,s]):this.sendFrame(y.frame(e,o),s)}pong(e,t,s){let r,i;if("string"==typeof e?(r=Buffer.byteLength(e),i=!1):c(e)?(r=e.size,i=!1):(r=(e=_(e)).length,i=_.readOnly),r>125)throw RangeError("The data size must not be greater than 125 bytes");let o={[u]:r,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:i,rsv1:!1};c(e)?0!==this._state?this.enqueue([this.getBlobData,e,!1,o,s]):this.getBlobData(e,!1,o,s):0!==this._state?this.enqueue([this.dispatch,e,!1,o,s]):this.sendFrame(y.frame(e,o),s)}send(e,t,s){let r,i;let o=this._extensions[n.extensionName],a=t.binary?2:1,h=t.compress;"string"==typeof e?(r=Buffer.byteLength(e),i=!1):c(e)?(r=e.size,i=!1):(r=(e=_(e)).length,i=_.readOnly),this._firstFragment?(this._firstFragment=!1,h&&o&&o.params[o._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(h=r>=o._threshold),this._compress=h):(h=!1,a=0),t.fin&&(this._firstFragment=!0);let l={[u]:r,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:i,rsv1:h};c(e)?0!==this._state?this.enqueue([this.getBlobData,e,this._compress,l,s]):this.getBlobData(e,this._compress,l,s):0!==this._state?this.enqueue([this.dispatch,e,this._compress,l,s]):this.dispatch(e,this._compress,l,s)}getBlobData(e,t,s,r){this._bufferedBytes+=s[u],this._state=2,e.arrayBuffer().then(e=>{if(this._socket.destroyed){let e=Error("The socket was closed while the blob was being read");process.nextTick(g,this,e,r);return}this._bufferedBytes-=s[u];let i=_(e);t?this.dispatch(i,t,s,r):(this._state=0,this.sendFrame(y.frame(i,s),r),this.dequeue())}).catch(e=>{process.nextTick(b,this,e,r)})}dispatch(e,t,s,r){if(!t){this.sendFrame(y.frame(e,s),r);return}let i=this._extensions[n.extensionName];this._bufferedBytes+=s[u],this._state=1,i.compress(e,s.fin,(e,t)=>{if(this._socket.destroyed){g(this,Error("The socket was closed while data was being compressed"),r);return}this._bufferedBytes-=s[u],this._state=0,s.readOnly=!1,this.sendFrame(y.frame(t,s),r),this.dequeue()})}dequeue(){for(;0===this._state&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][u],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][u],this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}function g(e,t,s){"function"==typeof s&&s(t);for(let s=0;s<e._queue.length;s++){let r=e._queue[s],i=r[r.length-1];"function"==typeof i&&i(t)}}function b(e,t,s){g(e,t,s),e.onerror(t)}e.exports=y},4944:(e,t,s)=>{"use strict";s(3667);let{Duplex:r}=s(6162);function i(e){e.emit("close")}function o(){!this.destroyed&&this._writableState.finished&&this.destroy()}function n(e){this.removeListener("error",n),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,t){let s=!0,a=new r({...t,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(t,s){let r=!s&&a._readableState.objectMode?t.toString():t;a.push(r)||e.pause()}),e.once("error",function(e){a.destroyed||(s=!1,a.destroy(e))}),e.once("close",function(){a.destroyed||a.push(null)}),a._destroy=function(t,r){if(e.readyState===e.CLOSED){r(t),process.nextTick(i,a);return}let o=!1;e.once("error",function(e){o=!0,r(e)}),e.once("close",function(){o||r(t),process.nextTick(i,a)}),s&&e.terminate()},a._final=function(t){if(e.readyState===e.CONNECTING){e.once("open",function(){a._final(t)});return}null!==e._socket&&(e._socket._writableState.finished?(t(),a._readableState.endEmitted&&a.destroy()):(e._socket.once("finish",function(){t()}),e.close()))},a._read=function(){e.isPaused&&e.resume()},a._write=function(t,s,r){if(e.readyState===e.CONNECTING){e.once("open",function(){a._write(t,s,r)});return}e.send(t,r)},a.on("end",o),a.on("error",n),a}},7385:(e,t,s)=>{"use strict";let{tokenChars:r}=s(9471);e.exports={parse:function(e){let t=new Set,s=-1,i=-1,o=0;for(;o<e.length;o++){let n=e.charCodeAt(o);if(-1===i&&1===r[n])-1===s&&(s=o);else if(0!==o&&(32===n||9===n))-1===i&&-1!==s&&(i=o);else if(44===n){if(-1===s)throw SyntaxError(`Unexpected character at index ${o}`);-1===i&&(i=o);let r=e.slice(s,i);if(t.has(r))throw SyntaxError(`The "${r}" subprotocol is duplicated`);t.add(r),s=i=-1}else throw SyntaxError(`Unexpected character at index ${o}`)}if(-1===s||-1!==i)throw SyntaxError("Unexpected end of input");let n=e.slice(s,o);if(t.has(n))throw SyntaxError(`The "${n}" subprotocol is duplicated`);return t.add(n),t}}},9471:(e,t,s)=>{"use strict";let{isUtf8:r}=s(8893),{hasBlob:i}=s(2563);function o(e){let t=e.length,s=0;for(;s<t;)if((128&e[s])==0)s++;else if((224&e[s])==192){if(s+1===t||(192&e[s+1])!=128||(254&e[s])==192)return!1;s+=2}else if((240&e[s])==224){if(s+2>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||224===e[s]&&(224&e[s+1])==128||237===e[s]&&(224&e[s+1])==160)return!1;s+=3}else{if((248&e[s])!=240||s+3>=t||(192&e[s+1])!=128||(192&e[s+2])!=128||(192&e[s+3])!=128||240===e[s]&&(240&e[s+1])==128||244===e[s]&&e[s+1]>143||e[s]>244)return!1;s+=4}return!0}if(e.exports={isBlob:function(e){return i&&"object"==typeof e&&"function"==typeof e.arrayBuffer&&"string"==typeof e.type&&"function"==typeof e.stream&&("Blob"===e[Symbol.toStringTag]||"File"===e[Symbol.toStringTag])},isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:o,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},r)e.exports.isValidUTF8=function(e){return e.length<24?o(e):r(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=s(5600);e.exports.isValidUTF8=function(e){return e.length<32?o(e):t(e)}}catch(e){}},169:(e,t,s)=>{"use strict";let r=s(7702),i=s(2615),{Duplex:o}=s(6162),{createHash:n}=s(4770),a=s(7363),h=s(2611),l=s(7385),c=s(3667),{GUID:d,kWebSocket:f}=s(2563),_=/^[+/0-9A-Za-z]{22}==$/;class u extends r{constructor(e,t){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:c,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=i.createServer((e,t)=>{let s=i.STATUS_CODES[426];t.writeHead(426,{"Content-Length":s.length,"Content-Type":"text/plain"}),t.end(s)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(let s of Object.keys(t))e.on(s,t[s]);return function(){for(let s of Object.keys(t))e.removeListener(s,t[s])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,s,r)=>{this.handleUpgrade(t,s,r,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(p,this);return}if(e&&this.once("close",e),1!==this._state){if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(p,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{p(this)})}}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,s,r){t.on("error",m);let i=e.headers["sec-websocket-key"],o=e.headers.upgrade,n=+e.headers["sec-websocket-version"];if("GET"!==e.method){g(this,e,t,405,"Invalid HTTP method");return}if(void 0===o||"websocket"!==o.toLowerCase()){g(this,e,t,400,"Invalid Upgrade header");return}if(void 0===i||!_.test(i)){g(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");return}if(8!==n&&13!==n){g(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){y(t,400);return}let c=e.headers["sec-websocket-protocol"],d=new Set;if(void 0!==c)try{d=l.parse(c)}catch(s){g(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let f=e.headers["sec-websocket-extensions"],u={};if(this.options.perMessageDeflate&&void 0!==f){let s=new h(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=a.parse(f);e[h.extensionName]&&(s.accept(e[h.extensionName]),u[h.extensionName]=s)}catch(s){g(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let o={origin:e.headers[`${8===n?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length){this.options.verifyClient(o,(o,n,a,h)=>{if(!o)return y(t,n||401,a,h);this.completeUpgrade(u,i,d,e,t,s,r)});return}if(!this.options.verifyClient(o))return y(t,401)}this.completeUpgrade(u,i,d,e,t,s,r)}completeUpgrade(e,t,s,r,i,o,l){if(!i.readable||!i.writable)return i.destroy();if(i[f])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return y(i,503);let c=n("sha1").update(t+d).digest("base64"),_=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${c}`],u=new this.options.WebSocket(null,void 0,this.options);if(s.size){let e=this.options.handleProtocols?this.options.handleProtocols(s,r):s.values().next().value;e&&(_.push(`Sec-WebSocket-Protocol: ${e}`),u._protocol=e)}if(e[h.extensionName]){let t=e[h.extensionName].params,s=a.format({[h.extensionName]:[t]});_.push(`Sec-WebSocket-Extensions: ${s}`),u._extensions=e}this.emit("headers",_,r),i.write(_.concat("\r\n").join("\r\n")),i.removeListener("error",m),u.setSocket(i,o,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(u),u.on("close",()=>{this.clients.delete(u),this._shouldEmitClose&&!this.clients.size&&process.nextTick(p,this)})),l(u,r)}}function p(e){e._state=2,e.emit("close")}function m(){this.destroy()}function y(e,t,s,r){s=s||i.STATUS_CODES[t],r={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(s),...r},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${i.STATUS_CODES[t]}\r
`+Object.keys(r).map(e=>`${e}: ${r[e]}`).join("\r\n")+"\r\n\r\n"+s)}function g(e,t,s,r,i){if(e.listenerCount("wsClientError")){let r=Error(i);Error.captureStackTrace(r,g),e.emit("wsClientError",r,s,t)}else y(s,r,i)}e.exports=u},3667:(e,t,s)=>{"use strict";let r=s(7702),i=s(5240),o=s(2615),n=s(8216),a=s(2452),{randomBytes:h,createHash:l}=s(4770),{Duplex:c,Readable:d}=s(6162),{URL:f}=s(7360),_=s(2611),u=s(8378),p=s(8386),{isBlob:m}=s(9471),{BINARY_TYPES:y,EMPTY_BUFFER:g,GUID:b,kForOnEventAttribute:S,kListener:v,kStatusCode:E,kWebSocket:k,NOOP:x}=s(2563),{EventTarget:{addEventListener:w,removeEventListener:O}}=s(7237),{format:T,parse:C}=s(7363),{toBuffer:N}=s(3052),L=Symbol("kAborted"),P=[8,13],B=["CONNECTING","OPEN","CLOSING","CLOSED"],R=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class I extends r{constructor(e,t,s){super(),this._binaryType=y[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=g,this._closeTimer=null,this._errorEmitted=!1,this._extensions={},this._paused=!1,this._protocol="",this._readyState=I.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===t?t=[]:Array.isArray(t)||("object"==typeof t&&null!==t?(s=t,t=[]):t=[t]),function e(t,s,r,n){let a,c,d,u;let p={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:P[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...n,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=p.autoPong,!P.includes(p.protocolVersion))throw RangeError(`Unsupported protocol version: ${p.protocolVersion} (supported versions: ${P.join(", ")})`);if(s instanceof f)a=s;else try{a=new f(s)}catch(e){throw SyntaxError(`Invalid URL: ${s}`)}"http:"===a.protocol?a.protocol="ws:":"https:"===a.protocol&&(a.protocol="wss:"),t._url=a.href;let m="wss:"===a.protocol,y="ws+unix:"===a.protocol;if("ws:"===a.protocol||m||y?y&&!a.pathname?c="The URL's pathname is empty":a.hash&&(c="The URL contains a fragment identifier"):c='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https:", or "ws+unix:"',c){let e=SyntaxError(c);if(0===t._redirects)throw e;U(t,e);return}let g=m?443:80,S=h(16).toString("base64"),v=m?i.request:o.request,E=new Set;if(p.createConnection=p.createConnection||(m?W:D),p.defaultPort=p.defaultPort||g,p.port=a.port||g,p.host=a.hostname.startsWith("[")?a.hostname.slice(1,-1):a.hostname,p.headers={...p.headers,"Sec-WebSocket-Version":p.protocolVersion,"Sec-WebSocket-Key":S,Connection:"Upgrade",Upgrade:"websocket"},p.path=a.pathname+a.search,p.timeout=p.handshakeTimeout,p.perMessageDeflate&&(d=new _(!0!==p.perMessageDeflate?p.perMessageDeflate:{},!1,p.maxPayload),p.headers["Sec-WebSocket-Extensions"]=T({[_.extensionName]:d.offer()})),r.length){for(let e of r){if("string"!=typeof e||!R.test(e)||E.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");E.add(e)}p.headers["Sec-WebSocket-Protocol"]=r.join(",")}if(p.origin&&(p.protocolVersion<13?p.headers["Sec-WebSocket-Origin"]=p.origin:p.headers.Origin=p.origin),(a.username||a.password)&&(p.auth=`${a.username}:${a.password}`),y){let e=p.path.split(":");p.socketPath=e[0],p.path=e[1]}if(p.followRedirects){if(0===t._redirects){t._originalIpc=y,t._originalSecure=m,t._originalHostOrSocketPath=y?p.socketPath:a.host;let e=n&&n.headers;if(n={...n,headers:{}},e)for(let[t,s]of Object.entries(e))n.headers[t.toLowerCase()]=s}else if(0===t.listenerCount("redirect")){let e=y?!!t._originalIpc&&p.socketPath===t._originalHostOrSocketPath:!t._originalIpc&&a.host===t._originalHostOrSocketPath;e&&(!t._originalSecure||m)||(delete p.headers.authorization,delete p.headers.cookie,e||delete p.headers.host,p.auth=void 0)}p.auth&&!n.headers.authorization&&(n.headers.authorization="Basic "+Buffer.from(p.auth).toString("base64")),u=t._req=v(p),t._redirects&&t.emit("redirect",t.url,u)}else u=t._req=v(p);p.timeout&&u.on("timeout",()=>{M(t,u,"Opening handshake has timed out")}),u.on("error",e=>{null===u||u[L]||(u=t._req=null,U(t,e))}),u.on("response",i=>{let o=i.headers.location,a=i.statusCode;if(o&&p.followRedirects&&a>=300&&a<400){let i;if(++t._redirects>p.maxRedirects){M(t,u,"Maximum redirects exceeded");return}u.abort();try{i=new f(o,s)}catch(e){U(t,SyntaxError(`Invalid URL: ${o}`));return}e(t,i,r,n)}else t.emit("unexpected-response",u,i)||M(t,u,`Unexpected server response: ${i.statusCode}`)}),u.on("upgrade",(e,s,r)=>{let i;if(t.emit("upgrade",e),t.readyState!==I.CONNECTING)return;u=t._req=null;let o=e.headers.upgrade;if(void 0===o||"websocket"!==o.toLowerCase()){M(t,s,"Invalid Upgrade header");return}let n=l("sha1").update(S+b).digest("base64");if(e.headers["sec-websocket-accept"]!==n){M(t,s,"Invalid Sec-WebSocket-Accept header");return}let a=e.headers["sec-websocket-protocol"];if(void 0!==a?E.size?E.has(a)||(i="Server sent an invalid subprotocol"):i="Server sent a subprotocol but none was requested":E.size&&(i="Server sent no subprotocol"),i){M(t,s,i);return}a&&(t._protocol=a);let h=e.headers["sec-websocket-extensions"];if(void 0!==h){let e;if(!d){M(t,s,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}try{e=C(h)}catch(e){M(t,s,"Invalid Sec-WebSocket-Extensions header");return}let r=Object.keys(e);if(1!==r.length||r[0]!==_.extensionName){M(t,s,"Server indicated an extension that was not requested");return}try{d.accept(e[_.extensionName])}catch(e){M(t,s,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[_.extensionName]=d}t.setSocket(s,r,{allowSynchronousEvents:p.allowSynchronousEvents,generateMask:p.generateMask,maxPayload:p.maxPayload,skipUTF8Validation:p.skipUTF8Validation})}),p.finishRequest?p.finishRequest(u,t):u.end()}(this,e,t,s)):(this._autoPong=s.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){y.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,s){let r=new u({allowSynchronousEvents:s.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:s.maxPayload,skipUTF8Validation:s.skipUTF8Validation}),i=new p(e,this._extensions,s.generateMask);this._receiver=r,this._sender=i,this._socket=e,r[k]=this,i[k]=this,e[k]=this,r.on("conclude",F),r.on("drain",$),r.on("error",j),r.on("message",V),r.on("ping",q),r.on("pong",z),i.onerror=X,e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",Z),e.on("data",Y),e.on("end",J),e.on("error",Q),this._readyState=I.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=I.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[_.extensionName]&&this._extensions[_.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=I.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==I.CLOSED){if(this.readyState===I.CONNECTING){M(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===I.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=I.CLOSING,this._sender.close(e,t,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),K(this)}}pause(){this.readyState!==I.CONNECTING&&this.readyState!==I.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,t,s){if(this.readyState===I.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==I.OPEN){A(this,e,s);return}void 0===t&&(t=!this._isServer),this._sender.ping(e||g,t,s)}pong(e,t,s){if(this.readyState===I.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(s=e,e=t=void 0):"function"==typeof t&&(s=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==I.OPEN){A(this,e,s);return}void 0===t&&(t=!this._isServer),this._sender.pong(e||g,t,s)}resume(){this.readyState!==I.CONNECTING&&this.readyState!==I.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,s){if(this.readyState===I.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t&&(s=t,t={}),"number"==typeof e&&(e=e.toString()),this.readyState!==I.OPEN){A(this,e,s);return}let r={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[_.extensionName]||(r.compress=!1),this._sender.send(e||g,r,s)}terminate(){if(this.readyState!==I.CLOSED){if(this.readyState===I.CONNECTING){M(this,this._req,"WebSocket was closed before the connection was established");return}this._socket&&(this._readyState=I.CLOSING,this._socket.destroy())}}}function U(e,t){e._readyState=I.CLOSING,e._errorEmitted=!0,e.emit("error",t),e.emitClose()}function D(e){return e.path=e.socketPath,n.connect(e)}function W(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=n.isIP(e.host)?"":e.host),a.connect(e)}function M(e,t,s){e._readyState=I.CLOSING;let r=Error(s);Error.captureStackTrace(r,M),t.setHeader?(t[L]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(U,e,r)):(t.destroy(r),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function A(e,t,s){if(t){let s=m(t)?t.size:N(t).length;e._socket?e._sender._bufferedBytes+=s:e._bufferedAmount+=s}if(s){let t=Error(`WebSocket is not open: readyState ${e.readyState} (${B[e.readyState]})`);process.nextTick(s,t)}}function F(e,t){let s=this[k];s._closeFrameReceived=!0,s._closeMessage=t,s._closeCode=e,void 0!==s._socket[k]&&(s._socket.removeListener("data",Y),process.nextTick(H,s._socket),1005===e?s.close():s.close(e,t))}function $(){let e=this[k];e.isPaused||e._socket.resume()}function j(e){let t=this[k];void 0!==t._socket[k]&&(t._socket.removeListener("data",Y),process.nextTick(H,t._socket),t.close(e[E])),t._errorEmitted||(t._errorEmitted=!0,t.emit("error",e))}function G(){this[k].emitClose()}function V(e,t){this[k].emit("message",e,t)}function q(e){let t=this[k];t._autoPong&&t.pong(e,!this._isServer,x),t.emit("ping",e)}function z(e){this[k].emit("pong",e)}function H(e){e.resume()}function X(e){let t=this[k];t.readyState===I.CLOSED||(t.readyState===I.OPEN&&(t._readyState=I.CLOSING,K(t)),this._socket.end(),t._errorEmitted||(t._errorEmitted=!0,t.emit("error",e)))}function K(e){e._closeTimer=setTimeout(e._socket.destroy.bind(e._socket),3e4)}function Z(){let e;let t=this[k];this.removeListener("close",Z),this.removeListener("data",Y),this.removeListener("end",J),t._readyState=I.CLOSING,this._readableState.endEmitted||t._closeFrameReceived||t._receiver._writableState.errorEmitted||null===(e=t._socket.read())||t._receiver.write(e),t._receiver.end(),this[k]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",G),t._receiver.on("finish",G))}function Y(e){this[k]._receiver.write(e)||this.pause()}function J(){let e=this[k];e._readyState=I.CLOSING,e._receiver.end(),this.end()}function Q(){let e=this[k];this.removeListener("error",Q),this.on("error",x),e&&(e._readyState=I.CLOSING,this.destroy())}Object.defineProperty(I,"CONNECTING",{enumerable:!0,value:B.indexOf("CONNECTING")}),Object.defineProperty(I.prototype,"CONNECTING",{enumerable:!0,value:B.indexOf("CONNECTING")}),Object.defineProperty(I,"OPEN",{enumerable:!0,value:B.indexOf("OPEN")}),Object.defineProperty(I.prototype,"OPEN",{enumerable:!0,value:B.indexOf("OPEN")}),Object.defineProperty(I,"CLOSING",{enumerable:!0,value:B.indexOf("CLOSING")}),Object.defineProperty(I.prototype,"CLOSING",{enumerable:!0,value:B.indexOf("CLOSING")}),Object.defineProperty(I,"CLOSED",{enumerable:!0,value:B.indexOf("CLOSED")}),Object.defineProperty(I.prototype,"CLOSED",{enumerable:!0,value:B.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(I.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(I.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[S])return t[v];return null},set(t){for(let t of this.listeners(e))if(t[S]){this.removeListener(e,t);break}"function"==typeof t&&this.addEventListener(e,t,{[S]:!0})}})}),I.prototype.addEventListener=w,I.prototype.removeEventListener=O,e.exports=I},7785:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Receiver:()=>i,Sender:()=>o,WebSocket:()=>n,WebSocketServer:()=>a,createWebSocketStream:()=>r,default:()=>h});var r=s(4944),i=s(8378),o=s(8386),n=s(3667),a=s(169);let h=n}};