{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/api/bug-reports/ministry/[ministry]", "regex": "^/api/bug\\-reports/ministry/([^/]+?)(?:/)?$", "routeKeys": {"nxtPministry": "nxtPministry"}, "namedRegex": "^/api/bug\\-reports/ministry/(?<nxtPministry>[^/]+?)(?:/)?$"}, {"page": "/api/bug-reports/[id]", "regex": "^/api/bug\\-reports/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/bug\\-reports/(?<nxtPid>[^/]+?)(?:/)?$"}], "staticRoutes": [], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}